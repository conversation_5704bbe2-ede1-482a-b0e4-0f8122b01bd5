"""
Utilities for working with Row-Level Security (RLS) in PostgreSQL.

This module provides functions for securely interacting with the
SECURITY DEFINER functions in PostgreSQL that are used for maintenance
operations that require bypassing RLS.
"""
from typing import Dict, Any, Optional, List
from sqlalchemy.ext.asyncio import AsyncSession
from sqlalchemy import text
import json
import logging
from datetime import datetime, timezone, timedelta

from src.utils.base_logger import log_info, log_error, log_warning

async def execute_cleanup_old_data(
    db: AsyncSession,
    account_id: int,
    days_to_keep: int
) -> Dict[str, Any]:
    """
    Executes the cleanup_old_data PostgreSQL function securely.
    
    This function calls the SECURITY DEFINER function in PostgreSQL
    that cleans up old data for a specific account. It handles the
    result parsing and error handling.
    
    Args:
        db: Database session
        account_id: ID of the account to clean up data for
        days_to_keep: Number of days to keep data (1-3650)
        
    Returns:
        Dictionary with operation results
        
    Raises:
        ValueError: If parameters are invalid
        Exception: If the database operation fails
    """
    try:
        # Validate parameters
        if account_id <= 0:
            raise ValueError(f"Invalid account_id: {account_id}. Must be a positive integer.")
        
        if days_to_keep < 1 or days_to_keep > 3650:
            raise ValueError(f"Invalid days_to_keep: {days_to_keep}. Must be between 1 and 3650.")
        
        log_info(f"Executing cleanup_old_data for account_id={account_id} with days_to_keep={days_to_keep}")
        
        # Execute the function
        result = await db.execute(
            text("SELECT cleanup_old_data(:account_id, :days_old)"),
            {"account_id": account_id, "days_old": days_to_keep}
        )
        
        # Get the result
        json_result = result.scalar_one()
        
        # Parse the JSON result
        if json_result:
            cleanup_data = json.loads(json_result)
            log_info(f"Cleanup completed: {cleanup_data}")
            return cleanup_data
        else:
            log_warning(f"Cleanup returned no result for account_id={account_id}")
            return {
                "account_id": account_id,
                "days_old": days_to_keep,
                "success": False,
                "error": "No result returned",
                "timestamp": datetime.now(timezone.utc).isoformat()
            }
    except ValueError as e:
        log_error(f"Invalid parameters for cleanup_old_data: {str(e)}")
        raise
    except Exception as e:
        log_error(f"Error executing cleanup_old_data: {str(e)}")
        return {
            "account_id": account_id,
            "days_old": days_to_keep,
            "success": False,
            "error": str(e),
            "timestamp": datetime.now(timezone.utc).isoformat()
        }

class RLSBypassContext:
    """
    ⚠️  SECURITY CRITICAL: RLS BYPASS CONTEXT ⚠️
    
    Context manager for safely bypassing RLS for a specific account.
    
    This class provides a context manager that safely bypasses RLS
    for a specific account and ensures that the session is properly
    restored when the context is exited.
    
    SECURITY RESTRICTIONS:
    - MUST only be used for maintenance operations
    - MUST specify a valid account_id (cannot be None or 0)
    - MUST be used within proper tenant context
    - All usage is logged for security auditing
    
    Example:
        ```python
        async with RLSBypassContext(db, account_id):
            # Code here runs with RLS bypassed for the specified account
            await db.execute(...)
        # RLS is restored here
        ```
    """
    
    def __init__(self, db: AsyncSession, account_id: int):
        """
        Initialize the context manager.
        
        Args:
            db: Database session
            account_id: ID of the account to bypass RLS for (REQUIRED, must be > 0)
        """
        self.db = db
        self.account_id = account_id
        self._bypass_started_at = None
    
    async def __aenter__(self):
        """
        Enter the context, bypassing RLS for the specified account.
        
        Returns:
            The database session
        """
        try:
            # SECURITY VALIDATION: Strict account_id validation
            if self.account_id is None:
                log_error("[SECURITY VIOLATION] RLS bypass attempted with None account_id")
                raise ValueError("account_id cannot be None for RLS bypass")
                
            if self.account_id <= 0:
                log_error(f"[SECURITY VIOLATION] RLS bypass attempted with invalid account_id: {self.account_id}")
                raise ValueError(f"Invalid account_id: {self.account_id}. Must be a positive integer.")
            
            # Log security-critical operation
            log_warning(f"[SECURITY AUDIT] RLS bypass starting for account_id={self.account_id}")
            self._bypass_started_at = datetime.now(timezone.utc)
            
            # Call the bypass_rls function
            await self.db.execute(
                text("SELECT bypass_rls(:account_id)"),
                {"account_id": self.account_id}
            )
            
            log_info(f"[SECURITY AUDIT] RLS bypass active for account_id={self.account_id}")
            return self.db
        except Exception as e:
            log_error(f"[SECURITY VIOLATION] Error bypassing RLS for account_id={self.account_id}: {str(e)}")
            raise
    
    async def __aexit__(self, exc_type, exc_val, exc_tb):
        """
        Exit the context, restoring the session.
        """
        try:
            # Calculate bypass duration for audit
            duration = None
            if self._bypass_started_at:
                duration = (datetime.now(timezone.utc) - self._bypass_started_at).total_seconds()
            
            # Reset the session authorization
            await self.db.execute(text("RESET SESSION AUTHORIZATION"))
            
            # Log security audit information
            duration_str = f" (duration: {duration:.2f}s)" if duration else ""
            log_warning(f"[SECURITY AUDIT] RLS bypass ended for account_id={self.account_id}{duration_str}")
            
            # If there was an exception during bypass, log it
            if exc_type is not None:
                log_error(f"[SECURITY AUDIT] Exception during RLS bypass for account_id={self.account_id}: {exc_type.__name__}: {exc_val}")
                
        except Exception as e:
            log_error(f"[SECURITY VIOLATION] Error resetting session after RLS bypass for account_id={self.account_id}: {str(e)}")
            # Don't suppress the original exception
            return False
