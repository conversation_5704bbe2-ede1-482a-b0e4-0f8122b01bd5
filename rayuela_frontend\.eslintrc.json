{"extends": ["next/core-web-vitals"], "rules": {"no-restricted-syntax": ["warn", {"selector": "JSXAttribute[name.name='className'] > Literal[value*=' mr-2']", "message": "Avoid using 'mr-2' directly. Use IconText component for consistent icon-text spacing."}, {"selector": "JSXAttribute[name.name='className'] > Literal[value*=' ml-2']", "message": "Avoid using 'ml-2' directly. Use IconText component for consistent icon-text spacing."}], "jsx-a11y/no-static-element-interactions": "off", "jsx-a11y/click-events-have-key-events": "off"}, "overrides": [{"files": ["src/components/ui/spacing-system.tsx"], "rules": {"no-restricted-syntax": "off"}}]}