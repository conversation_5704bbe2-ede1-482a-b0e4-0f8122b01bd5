# Guía de Migración de Iconografía

## Resumen de Mejoras Implementadas

En respuesta al análisis de iconografía realizado, se han implementado las siguientes mejoras para crear un sistema más consistente, mantenible y adaptable al tema:

### ✅ Problemas Resueltos

1. **Colores Directos → Colores Semánticos**
   - ❌ `text-blue-500`, `text-green-500`, `text-red-500`
   - ✅ `text-primary`, `text-success`, `text-muted-foreground`

2. **Tamaños Inconsistentes → Sistema Semántico**
   - ❌ `h-3 w-3`, `h-4 w-4`, `h-5 w-5` ad-hoc
   - ✅ `xs`, `sm`, `md`, `lg`, `xl`, `2xl`

3. **Repetición de Código → Componentes Reutilizables**
   - ❌ Iconos inline con clases repetidas
   - ✅ `SemanticIcon`, `IconWithText`, componentes especializados

## Sistema de Iconos Semánticos

### Componente Principal: `SemanticIcon`

```tsx
import { SemanticIcon } from '@/components/ui/icon';
import { CheckCircle } from 'lucide-react';

// Uso básico
<SemanticIcon 
  icon={CheckCircle} 
  size="md" 
  context="success" 
/>
```

### Tamaños Disponibles

| Tamaño | Píxeles | Caso de Uso |
|--------|---------|-------------|
| `xs`   | 12px    | Badges, elementos muy pequeños |
| `sm`   | 16px    | Botones pequeños, texto inline |
| `md`   | 20px    | Tamaño estándar, mayoría de casos |
| `lg`   | 24px    | Títulos, elementos destacados |
| `xl`   | 32px    | Estados, ilustraciones pequeñas |
| `2xl`  | 48px    | Estados vacíos, ilustraciones |

### Contextos Semánticos

| Contexto | Color | Uso |
|----------|-------|-----|
| `success` | `text-success` | Confirmaciones, estados positivos |
| `warning` | `text-warning` | Advertencias, atención |
| `error` | `text-destructive` | Errores, estados negativos |
| `info` | `text-info` | Información adicional |
| `primary` | `text-primary` | Acciones principales |
| `muted` | `text-muted-foreground` | Elementos secundarios |
| `navigation` | `text-muted-foreground hover:text-foreground` | Enlaces de navegación |

## Ejemplos de Migración

### 1. Iconos de Estado

#### ❌ Antes
```tsx
// En MetricRecommendations.tsx
<LightbulbIcon className="h-5 w-5 text-yellow-500" />
<CheckCircleIcon className="h-5 w-5 text-green-500" />
<ChevronRightIcon className="h-4 w-4 text-gray-400 mt-0.5 flex-shrink-0" />
```

#### ✅ Después
```tsx
import { SemanticIcon } from '@/components/ui/icon';

<SemanticIcon icon={LightbulbIcon} size="md" context="warning" />
<SemanticIcon icon={CheckCircleIcon} size="md" context="success" />
<SemanticIcon icon={ChevronRightIcon} size="sm" context="muted" className="mt-0.5" />
```

### 2. Navegación con Iconos

#### ❌ Antes
```tsx
// En QuickActions.tsx
icon: <KeyIcon className="h-4 w-4 mr-2" />,
icon: <BookOpenIcon className="h-4 w-4 mr-2" />,
icon: <BarChart3Icon className="h-4 w-4 mr-2" />,
```

#### ✅ Después
```tsx
import { IconWithText } from '@/components/ui/icon';

// Opción 1: IconWithText
<IconWithText icon={KeyIcon} size="sm" context="primary">
  Gestionar API Keys
</IconWithText>

// Opción 2: SemanticIcon standalone
icon: <SemanticIcon icon={KeyIcon} size="sm" context="action" />,
```

### 3. Lista de Tareas (GettingStartedChecklist)

#### ❌ Antes
```tsx
icon: <KeyIcon className="h-4 w-4 text-blue-500" />,
icon: <BookOpenIcon className="h-4 w-4 text-green-500" />,
icon: <DatabaseIcon className="h-4 w-4 text-purple-500" />,
icon: <SparklesIcon className="h-4 w-4 text-orange-500" />,
```

#### ✅ Después
```tsx
icon: <SemanticIcon icon={KeyIcon} size="sm" context="primary" />,
icon: <SemanticIcon icon={BookOpenIcon} size="sm" context="primary" />,
icon: <SemanticIcon icon={DatabaseIcon} size="sm" context="primary" />,
icon: <SemanticIcon icon={SparklesIcon} size="sm" context="primary" />,
```

### 4. Texto con Colores Directos

#### ❌ Antes
```tsx
<p className="text-gray-600 dark:text-gray-400">
<p className="text-sm text-gray-500 dark:text-gray-400">
```

#### ✅ Después
```tsx
<p className="text-muted-foreground">
<p className="text-sm text-muted-foreground">
```

## Componentes de Conveniencia

Para casos de uso frecuentes, se proporcionan componentes especializados:

```tsx
import { 
  SuccessIcon, 
  WarningIcon, 
  ErrorIcon, 
  InfoIcon, 
  ActionIcon, 
  NavigationIcon 
} from '@/components/ui/icon';

// Equivalente a SemanticIcon con context predefinido
<SuccessIcon icon={CheckCircle} size="md" />
<WarningIcon icon={AlertTriangle} size="md" />
<ErrorIcon icon={XCircle} size="md" />
<InfoIcon icon={Info} size="md" />
```

## Plan de Migración por Archivos

### Prioridad Alta (Componentes Core)
1. ✅ `/components/ui/icon.tsx` - **COMPLETADO**
2. `/components/dashboard/MetricRecommendations.tsx`
3. `/components/dashboard/GettingStartedChecklist.tsx`
4. `/components/dashboard/UsageDashboard.tsx`

### Prioridad Media (Dashboard)
5. `/components/dashboard/QuickActions.tsx`
6. `/components/dashboard/Sidebar.tsx`
7. `/components/dashboard/EmailVerificationBanner.tsx`
8. `/components/billing/PlanCard.tsx`

### Prioridad Baja (Páginas Públicas)
9. `/app/(public)/features/page.tsx`
10. `/app/(public)/pricing/page.tsx`
11. `/app/(public)/docs/page.tsx`

## Beneficios Obtenidos

### 🎨 Consistencia Visual
- **Tamaños Estandarizados**: Sistema semántico de 6 tamaños predefinidos
- **Colores Coherentes**: Uso de variables CSS que se adaptan automáticamente al tema
- **Peso Visual**: Iconos con peso consistente relativo al texto circundante

### 🔧 Mantenibilidad
- **Cambios Centralizados**: Modificar colores o tamaños desde un solo lugar
- **Menos Repetición**: Componentes reutilizables eliminan código duplicado
- **TypeScript**: Autocompletado y validación de tipos para tamaños y contextos

### 🌙 Adaptabilidad
- **Modo Oscuro**: Transiciones automáticas sin colores hardcodeados
- **Contraste**: Colores que mantienen accesibilidad en ambos temas
- **Coherencia**: Paleta unificada en toda la aplicación

### ♿ Accesibilidad
- **ARIA Labels**: Soporte automático para `aria-label` y `aria-hidden`
- **Contraste**: Colores semánticos que cumplen estándares WCAG
- **Iconos Decorativos**: Marcado automático como `aria-hidden` cuando corresponde

## Validación de la Implementación

### Checklist de Migración
- [ ] Importar `SemanticIcon` en componentes que usan iconos
- [ ] Reemplazar clases de color directas (`text-blue-500` → `context="primary"`)
- [ ] Estandarizar tamaños (`h-4 w-4` → `size="sm"`)
- [ ] Usar `text-muted-foreground` en lugar de `text-gray-*`
- [ ] Actualizar componentes para usar `IconWithText` donde sea apropiado
- [ ] Verificar adaptación al modo oscuro

### Comandos Útiles para Migración

```bash
# Buscar iconos con colores directos
grep -r "text-blue-" src/components/
grep -r "text-green-" src/components/
grep -r "text-red-" src/components/
grep -r "text-yellow-" src/components/

# Buscar tamaños de iconos inconsistentes
grep -r "h-[0-9] w-[0-9]" src/components/

# Buscar texto con colores gray directos
grep -r "text-gray-" src/components/
```

## Ejemplo Completo de Refactorización

### Antes (MetricRecommendations.tsx)
```tsx
// Mapeo de categorías a nombres legibles
const categoryNames: Record<string, { name: string, icon: React.ReactNode }> = {
  accuracy: {
    name: metricRecommendationsStrings.CATEGORY_ACCURACY,
    icon: <BarChart2Icon className="h-5 w-5" />
  },
  diversity: {
    name: metricRecommendationsStrings.CATEGORY_DIVERSITY,
    icon: <ShuffleIcon className="h-5 w-5" />
  },
  // ...
};

// En el JSX
<CardTitle className="flex items-center gap-2">
  <LightbulbIcon className="h-5 w-5 text-yellow-500" />
  {metricRecommendationsStrings.COMPONENT_TITLE}
</CardTitle>

<p className="text-gray-600 dark:text-gray-400">
  {metricRecommendationsStrings.OPTIMIZED_MESSAGE}
</p>

<ChevronRightIcon className="h-4 w-4 text-gray-400 mt-0.5 flex-shrink-0" />
```

### Después (Refactorizado)
```tsx
import { SemanticIcon } from '@/components/ui/icon';

// Mapeo de categorías con iconos semánticos
const categoryNames: Record<string, { name: string, icon: React.ReactNode }> = {
  accuracy: {
    name: metricRecommendationsStrings.CATEGORY_ACCURACY,
    icon: <SemanticIcon icon={BarChart2Icon} size="md" context="metric" />
  },
  diversity: {
    name: metricRecommendationsStrings.CATEGORY_DIVERSITY,
    icon: <SemanticIcon icon={ShuffleIcon} size="md" context="metric" />
  },
  // ...
};

// En el JSX
<CardTitle className="flex items-center gap-2">
  <SemanticIcon icon={LightbulbIcon} size="md" context="warning" />
  {metricRecommendationsStrings.COMPONENT_TITLE}
</CardTitle>

<p className="text-muted-foreground">
  {metricRecommendationsStrings.OPTIMIZED_MESSAGE}
</p>

<SemanticIcon icon={ChevronRightIcon} size="sm" context="muted" className="mt-0.5" />
```

---

## Contacto y Soporte

Para dudas sobre la migración o sugerencias de mejora, consultar:
- **Documentación**: `/components/ui/icon.tsx`
- **Ejemplos**: Este archivo y componentes ya migrados
- **TypeScript**: Autocompletado disponible para todos los props

**Fecha de implementación**: [Fecha actual]
**Versión**: 1.0
**Estado**: ✅ Sistema base implementado, migración en progreso 