# Database Connection Optimization Guide

## Overview

This guide documents the database connection optimization implemented to reduce Cloud SQL costs and improve resource efficiency for Rayuela's startup environment.

## Current Optimized Configuration

### SQLAlchemy Connection Pool Settings

**File:** `rayuela_backend/src/db/session.py`

```python
# Optimized settings for startup environment
pool_size=20         # Optimized for 2 workers with high async concurrency
max_overflow=5       # Allows spikes without overloading DB
pool_timeout=30      # Connection timeout
pool_recycle=1800    # Recycle connections every 30 minutes
```

### Gunicorn Configuration

**File:** `rayuela_backend/gunicorn_conf.py`

```python
# Optimized settings
workers = min(cpu_count + 1, 2)  # Maximum 2 workers for startup cost optimization
worker_connections = 20          # Reduced from 200 to 20, configurable via GUNICORN_WORKER_CONNECTIONS
```

## Connection Load Calculation

### Before Optimization
- **Theoretical Maximum:** 4 workers × 1500 connections = 6,000 connections
- **SQLAlchemy Pool:** 4 workers × 20 pool_size = 80 base connections + 40 overflow = 120 connections
- **Cloud SQL Requirement:** Very large instance needed to handle connection overhead

### After Optimization (Current)
- **Theoretical Maximum:** 2 workers × 20 connections = 40 connections
- **SQLAlchemy Pool:** 20 pool_size + 5 overflow = 25 physical connections
- **Cloud SQL Requirement:** Very small instance (db-f1-micro) sufficient with max_connections=30-40

## Cloud SQL Configuration

### Recommended max_connections Setting

Based on the optimized configuration, set Cloud SQL `max_connections` parameter to:

```bash
# Recommended value for startup environment
max_connections = 100

# Calculation: (workers × pool_size) + (workers × max_overflow) + buffer
# (4 × 8) + (4 × 5) + 48 = 32 + 20 + 48 = 100
```

### How to Configure Cloud SQL max_connections

#### Option 1: Using gcloud CLI
```bash
gcloud sql instances patch rayuela-postgres \
  --database-flags=max_connections=100
```

#### Option 2: Using Google Cloud Console
1. Go to Cloud SQL instances
2. Select your `rayuela-postgres` instance
3. Click "Edit"
4. Under "Flags", add or modify:
   - Flag: `max_connections`
   - Value: `100`
5. Save changes

#### Option 3: Using Terraform (if using IaC)
```hcl
resource "google_sql_database_instance" "rayuela_postgres" {
  # ... other configuration ...
  
  settings {
    database_flags {
      name  = "max_connections"
      value = "100"
    }
  }
}
```

## Environment Variables

The following environment variables can be used to fine-tune the configuration:

### Gunicorn Variables
```bash
# Number of Gunicorn workers (default: min(cpu_count + 1, 2))
GUNICORN_WORKERS=2

# Worker connections per worker (default: 20, optimized for startup costs)
# Total logical connections: workers * worker_connections = 2 * 20 = 40
# Aligned with SQLAlchemy pool: pool_size=20 + max_overflow=5 = 25 physical connections
GUNICORN_WORKER_CONNECTIONS=20

# Request timeout (default: 120)
GUNICORN_TIMEOUT=120
```

### Database Pool Variables (Future Enhancement)
Consider adding these environment variables to make SQLAlchemy pool settings configurable:

```bash
# SQLAlchemy pool settings (not yet implemented)
DB_POOL_SIZE=8
DB_MAX_OVERFLOW=5
DB_POOL_TIMEOUT=30
DB_POOL_RECYCLE=1800
```

## Cost Impact

### Before Optimization
- **Required Instance:** Larger instance (e.g., 2-4 vCPU, 8-16GB RAM) to handle connection overhead
- **Monthly Cost:** Higher due to oversized instance

### After Optimization
- **Required Instance:** Smaller instance (e.g., 1-2 vCPU, 4-8GB RAM) sufficient for actual workload
- **Monthly Cost:** Significantly reduced (estimated 30-50% savings on Cloud SQL costs)

## Monitoring and Tuning

### Key Metrics to Monitor

1. **Connection Pool Utilization**
   - Monitor SQLAlchemy pool metrics
   - Watch for pool exhaustion warnings

2. **Cloud SQL Connections**
   - Monitor active connections in Cloud SQL
   - Ensure max_connections is not reached

3. **Application Performance**
   - Monitor response times
   - Watch for connection timeout errors

### Tuning Guidelines

If you experience:

- **Connection pool exhaustion:** Increase `pool_size` or `max_overflow`
- **High connection count:** Decrease `worker_connections` or `pool_size`
- **Slow responses:** Check if `pool_timeout` needs adjustment
- **Memory issues:** Decrease `pool_size` and `worker_connections`

## Implementation Checklist

- [x] Optimize SQLAlchemy `pool_size` to 20 for 2-worker configuration
- [x] Keep SQLAlchemy `max_overflow` at 5 for spike handling
- [x] Reduce Gunicorn `workers` from 4 to 2 for startup cost optimization
- [x] Reduce Gunicorn `worker_connections` from 200 to 20 (configurable via environment variable)
- [x] Configure Cloud SQL `max_connections` to 40 (optimized for new connection limits)
- [ ] Monitor connection usage after deployment
- [ ] Adjust settings based on real-world usage patterns

## Rollback Plan

If issues arise, you can quickly rollback by setting environment variables:

```bash
# Rollback to previous settings if needed
GUNICORN_WORKER_CONNECTIONS=200

# Or for even higher capacity:
GUNICORN_WORKER_CONNECTIONS=1500
```

## Next Steps

1. **Deploy the optimized configuration**
2. **Configure Cloud SQL max_connections to 100**
3. **Monitor for 1-2 weeks**
4. **Fine-tune based on actual usage patterns**
5. **Consider making SQLAlchemy pool settings configurable via environment variables**
