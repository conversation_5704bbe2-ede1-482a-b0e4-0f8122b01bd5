"""
Endpoints for database maintenance operations.
"""

from typing import Dict, Any, Optional
from datetime import datetime
from fastapi import APIRouter, Depends, BackgroundTasks, HTTPException
from sqlalchemy.ext.asyncio import AsyncSession
from src.core.deps import get_db, get_current_admin_user
from src.db.models.system_user import SystemUser
from src.utils.maintenance import (
    cleanup_old_audit_logs,
    cleanup_old_interactions,
    cleanup_old_data_secure,
)
from src.utils.base_logger import log_info, log_error
from src.core.exceptions import handle_exceptions
from src.workers.celery_app import celery_app

router = APIRouter()


@router.post("/maintenance/cleanup-audit-logs", response_model=Dict[str, Any])
@handle_exceptions
async def cleanup_audit_logs(
    days_to_keep: int = 90,
    account_id: Optional[int] = None,
    run_async: bool = True,
    db: AsyncSession = Depends(get_db),
    current_admin: SystemUser = Depends(get_current_admin_user),
):
    """
    Limpia logs de auditoría más antiguos que el período especificado.

    Args:
        days_to_keep: Número de días a mantener los logs (por defecto 90)
        account_id: ID de la cuenta específica (None para todas las cuentas)
        run_async: Si es True, ejecuta la limpieza en segundo plano

    Returns:
        Diccionario con información sobre la operación o el ID de la tarea
    """
    try:
        account_msg = f" for account {account_id}" if account_id else ""

        if run_async:
            # Ejecutar como tarea de Celery
            task = celery_app.send_task(
                "cleanup_old_audit_logs",
                kwargs={
                    "days_to_keep": days_to_keep,
                    "account_id": account_id
                },
                queue="maintenance",
            )
            log_info(f"Scheduled audit logs cleanup task{account_msg}: {task.id}")
            return {
                "task_id": task.id,
                "status": "scheduled",
                "message": f"Cleanup of audit logs older than {days_to_keep} days{account_msg} scheduled",
            }
        else:
            # Ejecutar sincrónicamente
            result = await cleanup_old_audit_logs(days_to_keep, account_id=account_id)
            return result
    except Exception as e:
        log_error(f"Error cleaning up audit logs: {str(e)}")
        raise HTTPException(
            status_code=500, detail=f"Error cleaning up audit logs: {str(e)}"
        )


@router.post("/maintenance/cleanup-interactions", response_model=Dict[str, Any])
@handle_exceptions
async def cleanup_interactions(
    days_to_keep: int = 180,
    account_id: Optional[int] = None,
    batch_size: int = 10000,
    run_async: bool = True,
    db: AsyncSession = Depends(get_db),
    current_admin: SystemUser = Depends(get_current_admin_user),
):
    """
    Limpia interacciones más antiguas que el período especificado.

    Args:
        days_to_keep: Número de días a mantener las interacciones (por defecto 180)
        account_id: ID de la cuenta específica (None para todas las cuentas)
        batch_size: Tamaño del lote para eliminación (por defecto 10000)
        run_async: Si es True, ejecuta la limpieza en segundo plano

    Returns:
        Diccionario con información sobre la operación o el ID de la tarea
    """
    try:
        if run_async:
            # Ejecutar como tarea de Celery
            task = celery_app.send_task(
                "cleanup_old_interactions",
                kwargs={
                    "days_to_keep": days_to_keep,
                    "account_id": account_id,
                    "batch_size": batch_size,
                },
                queue="maintenance",
            )
            account_msg = f" for account {account_id}" if account_id else ""
            log_info(f"Scheduled interactions cleanup task{account_msg}: {task.id}")
            return {
                "task_id": task.id,
                "status": "scheduled",
                "message": f"Cleanup of interactions older than {days_to_keep} days{account_msg} scheduled",
            }
        else:
            # Ejecutar sincrónicamente
            result = await cleanup_old_interactions(
                days_to_keep=days_to_keep,
                account_id=account_id,
                batch_size=batch_size,
            )
            return result
    except Exception as e:
        log_error(f"Error cleaning up interactions: {str(e)}")
        raise HTTPException(
            status_code=500, detail=f"Error cleaning up interactions: {str(e)}"
        )


@router.get("/maintenance/task/{task_id}", response_model=Dict[str, Any])
@handle_exceptions
async def get_task_status(
    task_id: str,
    db: AsyncSession = Depends(get_db),
    current_admin: SystemUser = Depends(get_current_admin_user),
):
    """
    Obtiene el estado de una tarea de mantenimiento.

    Args:
        task_id: ID de la tarea

    Returns:
        Diccionario con información sobre la tarea
    """
    try:
        # Obtener el estado de la tarea de Celery
        task_result = celery_app.AsyncResult(task_id)

        result = {
            "task_id": task_id,
            "status": task_result.status,
        }

        # Si la tarea ha terminado, incluir el resultado
        if task_result.ready():
            if task_result.successful():
                result["result"] = task_result.result
            else:
                result["error"] = str(task_result.result)

        return result
    except Exception as e:
        log_error(f"Error getting task status: {str(e)}")
        raise HTTPException(
            status_code=500, detail=f"Error getting task status: {str(e)}"
        )


@router.post("/maintenance/cleanup-data-secure", response_model=Dict[str, Any])
@handle_exceptions
async def cleanup_data_secure_endpoint(
    days_to_keep: int = 180,
    account_id: Optional[int] = None,
    run_async: bool = True,
    db: AsyncSession = Depends(get_db),
    current_admin: SystemUser = Depends(get_current_admin_user),
):
    """
    ⚠️  SECURITY CRITICAL: ENDPOINT DISABLED ⚠️
    
    This endpoint has been disabled due to critical security risks.
    RLS bypass operations should ONLY be executed via:
    1. Internal Celery cron jobs with proper service account restrictions
    2. Direct database maintenance scripts with limited network access
    
    DO NOT RE-ENABLE without implementing additional security measures:
    - IP whitelist restrictions
    - Service account isolation  
    - Network-level access controls
    """
    raise HTTPException(
        status_code=503,
        detail="This endpoint has been disabled for security reasons. RLS bypass operations must be performed via internal maintenance processes only."
    )


@router.post("/maintenance/archive-and-cleanup-audit-logs", response_model=Dict[str, Any])
@handle_exceptions
async def archive_and_cleanup_audit_logs(
    days_to_keep: int = 90,
    account_id: Optional[int] = None,
    batch_size: int = 10000,
    run_async: bool = True,
    db: AsyncSession = Depends(get_db),
    current_admin: SystemUser = Depends(get_current_admin_user),
):
    """
    Archiva y luego limpia logs de auditoría más antiguos que el período especificado.

    Esta función primero exporta los datos a GCS y luego los elimina de Cloud SQL,
    proporcionando una estrategia de retención de datos costo-efectiva.

    Args:
        days_to_keep: Número de días a mantener los logs en Cloud SQL (por defecto 90)
        account_id: ID de la cuenta específica (None para todas las cuentas)
        batch_size: Tamaño del lote para procesamiento (por defecto 10000)
        run_async: Si es True, ejecuta el archivado en segundo plano

    Returns:
        Diccionario con información sobre la operación o el ID de la tarea
    """
    try:
        account_msg = f" for account {account_id}" if account_id else ""

        if run_async:
            # Ejecutar como tarea de Celery
            task = celery_app.send_task(
                "archive_and_cleanup_old_audit_logs",
                kwargs={
                    "days_to_keep": days_to_keep,
                    "account_id": account_id,
                    "batch_size": batch_size
                },
                queue="maintenance",
            )
            log_info(f"Scheduled archive and cleanup audit logs task{account_msg}: {task.id}")
            return {
                "task_id": task.id,
                "status": "scheduled",
                "message": f"Archive and cleanup of audit logs older than {days_to_keep} days{account_msg} scheduled",
            }
        else:
            # Ejecutar sincrónicamente
            from src.utils.maintenance import archive_and_cleanup_audit_logs_async
            result = await archive_and_cleanup_audit_logs_async(
                days_to_keep=days_to_keep,
                account_id=account_id,
                batch_size=batch_size
            )
            return result
    except Exception as e:
        log_error(f"Error archiving and cleaning up audit logs: {str(e)}")
        raise HTTPException(
            status_code=500, detail=f"Error archiving and cleaning up audit logs: {str(e)}"
        )


@router.post("/maintenance/archive-and-cleanup-interactions", response_model=Dict[str, Any])
@handle_exceptions
async def archive_and_cleanup_interactions(
    days_to_keep: int = 180,
    account_id: Optional[int] = None,
    batch_size: int = 10000,
    run_async: bool = True,
    db: AsyncSession = Depends(get_db),
    current_admin: SystemUser = Depends(get_current_admin_user),
):
    """
    Archiva y luego limpia interacciones más antiguas que el período especificado.

    Esta función primero exporta los datos a GCS y luego los elimina de Cloud SQL,
    proporcionando una estrategia de retención de datos costo-efectiva.

    Args:
        days_to_keep: Número de días a mantener las interacciones en Cloud SQL (por defecto 180)
        account_id: ID de la cuenta específica (None para todas las cuentas)
        batch_size: Tamaño del lote para procesamiento (por defecto 10000)
        run_async: Si es True, ejecuta el archivado en segundo plano

    Returns:
        Diccionario con información sobre la operación o el ID de la tarea
    """
    try:
        account_msg = f" for account {account_id}" if account_id else ""

        if run_async:
            # Ejecutar como tarea de Celery
            task = celery_app.send_task(
                "archive_and_cleanup_old_interactions",
                kwargs={
                    "days_to_keep": days_to_keep,
                    "account_id": account_id,
                    "batch_size": batch_size
                },
                queue="maintenance",
            )
            log_info(f"Scheduled archive and cleanup interactions task{account_msg}: {task.id}")
            return {
                "task_id": task.id,
                "status": "scheduled",
                "message": f"Archive and cleanup of interactions older than {days_to_keep} days{account_msg} scheduled",
            }
        else:
            # Ejecutar sincrónicamente
            from src.utils.maintenance import archive_and_cleanup_interactions_async
            result = await archive_and_cleanup_interactions_async(
                days_to_keep=days_to_keep,
                account_id=account_id,
                batch_size=batch_size
            )
            return result
    except Exception as e:
        log_error(f"Error archiving and cleaning up interactions: {str(e)}")
        raise HTTPException(
            status_code=500, detail=f"Error archiving and cleaning up interactions: {str(e)}"
        )


@router.get("/maintenance/archived-files/{table_name}", response_model=Dict[str, Any])
@handle_exceptions
async def list_archived_files(
    table_name: str,
    start_date: Optional[str] = None,
    end_date: Optional[str] = None,
    account_id: Optional[int] = None,
    current_admin: SystemUser = Depends(get_current_admin_user),
):
    """
    Lista archivos archivados para una tabla específica.

    Args:
        table_name: Nombre de la tabla (audit_logs o interactions)
        start_date: Fecha de inicio para filtrar (formato ISO)
        end_date: Fecha de fin para filtrar (formato ISO)
        account_id: ID de cuenta para filtrar

    Returns:
        Lista de archivos archivados con metadatos
    """
    try:
        if table_name not in ["audit_logs", "interactions"]:
            raise HTTPException(
                status_code=400,
                detail="table_name must be 'audit_logs' or 'interactions'"
            )

        # Convertir fechas si se proporcionan
        start_datetime = None
        end_datetime = None

        if start_date:
            try:
                start_datetime = datetime.fromisoformat(start_date.replace('Z', '+00:00'))
            except ValueError:
                raise HTTPException(status_code=400, detail="Invalid start_date format. Use ISO format.")

        if end_date:
            try:
                end_datetime = datetime.fromisoformat(end_date.replace('Z', '+00:00'))
            except ValueError:
                raise HTTPException(status_code=400, detail="Invalid end_date format. Use ISO format.")

        # Crear servicio de archivado y listar archivos
        from src.services.data_archival_service import DataArchivalService
        archival_service = DataArchivalService()

        files = await archival_service.list_archived_files(
            table_name=table_name,
            start_date=start_datetime,
            end_date=end_datetime,
            account_id=account_id
        )

        return {
            "table_name": table_name,
            "files": files,
            "total_files": len(files),
            "filters": {
                "start_date": start_date,
                "end_date": end_date,
                "account_id": account_id
            }
        }
    except HTTPException:
        raise
    except Exception as e:
        log_error(f"Error listing archived files: {str(e)}")
        raise HTTPException(
            status_code=500, detail=f"Error listing archived files: {str(e)}"
        )


@router.post("/maintenance/cleanup-soft-deleted-records", response_model=Dict[str, Any])
@handle_exceptions
async def cleanup_soft_deleted_records(
    retention_days: int = 365,
    account_id: Optional[int] = None,
    dry_run: bool = False,
    run_async: bool = True,
    db: AsyncSession = Depends(get_db),
    current_admin: SystemUser = Depends(get_current_admin_user),
):
    """
    Limpia registros con soft delete que han excedido el período de retención final.

    Esta función identifica y elimina permanentemente (o archiva y luego elimina)
    registros que tienen is_active = FALSE y deleted_at anterior al umbral definido.

    Args:
        retention_days: Número de días de retención después del soft delete (por defecto 365)
        account_id: ID de la cuenta específica (None para todas las cuentas)
        dry_run: Si es True, solo reporta qué se eliminaría sin hacer cambios
        run_async: Si es True, ejecuta la limpieza en segundo plano

    Returns:
        Diccionario con información sobre la operación o el ID de la tarea
    """
    try:
        account_msg = f" for account {account_id}" if account_id else ""
        action = "DRY RUN - Would cleanup" if dry_run else "Cleaning up"

        if run_async:
            # Ejecutar como tarea de Celery
            task = celery_app.send_task(
                "cleanup_soft_deleted_records",
                kwargs={
                    "retention_days": retention_days,
                    "account_id": account_id,
                    "dry_run": dry_run
                },
                queue="maintenance",
            )
            log_info(f"Scheduled soft delete cleanup task{account_msg}: {task.id}")
            return {
                "task_id": task.id,
                "status": "scheduled",
                "message": f"{action} soft deleted records older than {retention_days} days{account_msg} scheduled",
                "retention_days": retention_days,
                "dry_run": dry_run
            }
        else:
            # Ejecutar sincrónicamente
            from src.utils.maintenance import cleanup_soft_deleted_records_async
            result = await cleanup_soft_deleted_records_async(
                retention_days=retention_days,
                account_id=account_id,
                dry_run=dry_run
            )
            return result
    except Exception as e:
        log_error(f"Error cleaning up soft deleted records: {str(e)}")
        raise HTTPException(
            status_code=500, detail=f"Error cleaning up soft deleted records: {str(e)}"
        )


@router.get("/maintenance/soft-delete-statistics", response_model=Dict[str, Any])
@handle_exceptions
async def get_soft_delete_statistics(
    account_id: Optional[int] = None,
    run_async: bool = False,
    current_admin: SystemUser = Depends(get_current_admin_user),
):
    """
    Obtiene estadísticas sobre registros con soft delete.

    Args:
        account_id: ID de cuenta para filtrar (None para todas las cuentas)
        run_async: Si es True, ejecuta como tarea en segundo plano

    Returns:
        Estadísticas por tabla o ID de tarea si run_async=True
    """
    try:
        if run_async:
            # Ejecutar como tarea de Celery
            task = celery_app.send_task(
                "get_soft_delete_statistics",
                kwargs={"account_id": account_id},
                queue="maintenance",
            )
            log_info(f"Scheduled soft delete statistics task: {task.id}")
            return {
                "task_id": task.id,
                "status": "scheduled",
                "message": "Soft delete statistics collection scheduled"
            }
        else:
            # Ejecutar sincrónicamente
            from src.utils.maintenance import get_soft_delete_statistics_async
            stats = await get_soft_delete_statistics_async(account_id=account_id)
            return stats
    except Exception as e:
        log_error(f"Error getting soft delete statistics: {str(e)}")
        raise HTTPException(
            status_code=500, detail=f"Error getting soft delete statistics: {str(e)}"
        )


@router.post("/maintenance/monitor-tables", response_model=Dict[str, Any])
@handle_exceptions
async def monitor_tables(
    run_async: bool = True,
    db: AsyncSession = Depends(get_db),
    current_admin: SystemUser = Depends(get_current_admin_user),
):
    """
    Monitorea las tablas de alto volumen y devuelve estadísticas.

    Args:
        run_async: Si es True, ejecuta el monitoreo en segundo plano

    Returns:
        Diccionario con estadísticas de las tablas o el ID de la tarea
    """
    try:
        if run_async:
            # Ejecutar como tarea de Celery
            task = celery_app.send_task(
                "monitor_high_volume_tables",
                queue="maintenance",
            )
            log_info(f"Scheduled table monitoring task: {task.id}")
            return {
                "task_id": task.id,
                "status": "scheduled",
                "message": "Table monitoring scheduled",
            }
        else:
            # Esta función aún no está implementada en el módulo de mantenimiento
            # En una implementación real, llamaríamos a una función como:
            # result = await monitor_high_volume_tables()

            # Por ahora, devolvemos un mensaje de placeholder
            return {
                "status": "not_implemented",
                "message": "Synchronous table monitoring not yet implemented",
                "tables": ["interactions", "audit_logs"],
            }
    except Exception as e:
        log_error(f"Error monitoring tables: {str(e)}")
        raise HTTPException(
            status_code=500, detail=f"Error monitoring tables: {str(e)}"
        )
