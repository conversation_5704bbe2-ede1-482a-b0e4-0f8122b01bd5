#!/usr/bin/env python3
"""
Script para validar y analizar los datos de entity_id en audit_logs antes de la migración.

Este script analiza todos los valores de entity_id en la tabla audit_logs para:
1. Identificar cuántos son enteros válidos (convertibles)
2. Identificar valores no numéricos que se perderían en la migración
3. Proporcionar estadísticas para tomar decisiones informadas

Uso:
    python -m scripts.migrations.validate_audit_log_entity_id
"""

import asyncio
import sys
import re
from typing import Dict, List, Tuple
from sqlalchemy.ext.asyncio import AsyncSession
from sqlalchemy import text, select, func
from src.db.session import AsyncSessionLocal
from src.db.models.audit_log import AuditLog


class AuditLogEntityIdValidator:
    """Validador para analizar datos de entity_id en audit_logs."""

    def __init__(self):
        self.numeric_pattern = re.compile(r'^[0-9]+$')

    async def analyze_entity_ids(self) -> Dict[str, any]:
        """Analiza todos los entity_id en la tabla audit_logs."""
        
        async with AsyncSessionLocal() as session:
            # Obtener todos los entity_id únicos y su frecuencia
            result = await session.execute(
                select(AuditLog.entity_id, func.count(AuditLog.entity_id).label('count'))
                .group_by(AuditLog.entity_id)
                .order_by(func.count(AuditLog.entity_id).desc())
            )
            
            entity_id_data = result.fetchall()
            
            # Obtener el total de registros
            total_result = await session.execute(select(func.count(AuditLog.id)))
            total_records = total_result.scalar_one()

            # Análisis de tipos
            numeric_count = 0
            non_numeric_count = 0
            empty_count = 0
            non_numeric_values = []
            
            for entity_id, count in entity_id_data:
                if entity_id == "" or entity_id is None:
                    empty_count += count
                elif self.numeric_pattern.match(str(entity_id)):
                    numeric_count += count
                else:
                    non_numeric_count += count
                    non_numeric_values.append((entity_id, count))

            return {
                'total_records': total_records,
                'numeric_count': numeric_count,
                'non_numeric_count': non_numeric_count,
                'empty_count': empty_count,
                'non_numeric_values': non_numeric_values[:20],  # Top 20 non-numeric values
                'all_entity_id_data': entity_id_data[:50]  # Top 50 entity_id values
            }

    async def check_potential_data_loss(self) -> List[Tuple[str, int]]:
        """Identifica registros que se perderían en la migración."""
        
        async with AsyncSessionLocal() as session:
            # Buscar entity_ids que no son enteros válidos
            result = await session.execute(
                text("""
                    SELECT entity_id, COUNT(*) as count
                    FROM audit_logs 
                    WHERE entity_id !~ '^[0-9]+$' AND entity_id != ''
                    GROUP BY entity_id
                    ORDER BY count DESC
                """)
            )
            
            return result.fetchall()

    async def suggest_migration_strategy(self) -> Dict[str, any]:
        """Sugiere estrategias de migración basadas en los datos."""
        
        analysis = await self.analyze_entity_ids()
        data_loss = await self.check_potential_data_loss()
        
        total_records = analysis['total_records']
        numeric_percentage = (analysis['numeric_count'] / total_records * 100) if total_records > 0 else 0
        non_numeric_percentage = (analysis['non_numeric_count'] / total_records * 100) if total_records > 0 else 0
        
        strategy = {
            'migration_safety': 'safe' if non_numeric_percentage < 1 else 'requires_attention',
            'numeric_percentage': numeric_percentage,
            'non_numeric_percentage': non_numeric_percentage,
            'records_at_risk': len(data_loss),
            'recommended_action': self._get_recommended_action(non_numeric_percentage)
        }
        
        return strategy

    def _get_recommended_action(self, non_numeric_percentage: float) -> str:
        """Determina la acción recomendada basada en el porcentaje de datos no numéricos."""
        
        if non_numeric_percentage == 0:
            return "Migración segura: Todos los entity_id son numéricos o vacíos"
        elif non_numeric_percentage < 1:
            return "Migración con precaución: Menos del 1% de datos no numéricos. Considerar convertir a NULL"
        elif non_numeric_percentage < 5:
            return "Revisar datos: 1-5% de datos no numéricos. Analizar si se pueden convertir o preservar"
        else:
            return "Alto riesgo: Más del 5% de datos no numéricos. Considerar estrategia alternativa"

    async def print_analysis_report(self):
        """Imprime un reporte completo del análisis."""
        
        print("=" * 80)
        print("ANÁLISIS DE MIGRACIÓN: AuditLog.entity_id STRING → INTEGER")
        print("=" * 80)
        
        analysis = await self.analyze_entity_ids()
        strategy = await self.suggest_migration_strategy()
        data_loss = await self.check_potential_data_loss()
        
        print(f"\n📊 ESTADÍSTICAS GENERALES:")
        print(f"   Total de registros en audit_logs: {analysis['total_records']:,}")
        print(f"   Registros con entity_id numérico: {analysis['numeric_count']:,} ({strategy['numeric_percentage']:.1f}%)")
        print(f"   Registros con entity_id no numérico: {analysis['non_numeric_count']:,} ({strategy['non_numeric_percentage']:.1f}%)")
        print(f"   Registros con entity_id vacío: {analysis['empty_count']:,}")
        
        print(f"\n🎯 EVALUACIÓN DE MIGRACIÓN:")
        print(f"   Estado: {strategy['migration_safety'].upper()}")
        print(f"   Registros en riesgo: {strategy['records_at_risk']}")
        print(f"   Recomendación: {strategy['recommended_action']}")
        
        if analysis['non_numeric_values']:
            print(f"\n❌ VALORES NO NUMÉRICOS MÁS FRECUENTES:")
            for value, count in analysis['non_numeric_values']:
                print(f"   '{value}': {count} registros")
        
        print(f"\n📋 ENTITY_ID MÁS FRECUENTES (Top 20):")
        for i, (entity_id, count) in enumerate(analysis['all_entity_id_data'][:20], 1):
            is_numeric = "✅" if self.numeric_pattern.match(str(entity_id)) or entity_id == "" else "❌"
            print(f"   {i:2d}. '{entity_id}': {count} registros {is_numeric}")
        
        if data_loss:
            print(f"\n⚠️  REGISTROS QUE SE PERDERÍAN EN LA MIGRACIÓN:")
            for entity_id, count in data_loss[:10]:
                print(f"   '{entity_id}': {count} registros")
        
        print(f"\n💡 PRÓXIMOS PASOS:")
        if strategy['migration_safety'] == 'safe':
            print("   ✅ La migración es segura. Proceder con la migración.")
        else:
            print("   ⚠️  Revisar los datos no numéricos antes de proceder.")
            print("   💭 Considerar:")
            print("      - Convertir valores no numéricos a NULL")
            print("      - Crear una columna adicional para preservar IDs string")
            print("      - Mapear valores string a enteros")
        
        print("\n" + "=" * 80)


async def main():
    """Función principal del script."""
    
    validator = AuditLogEntityIdValidator()
    
    try:
        await validator.print_analysis_report()
        print("✅ Análisis completado exitosamente.")
        return 0
        
    except Exception as e:
        print(f"❌ Error durante el análisis: {e}")
        return 1


if __name__ == "__main__":
    exit_code = asyncio.run(main())
    sys.exit(exit_code) 