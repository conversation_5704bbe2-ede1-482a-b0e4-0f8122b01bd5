/**
 * This file contains utility functions for analyzing metrics and generating recommendations
 * based on the data from the API. These functions are pure and can be tested independently
 * of the UI components.
 */

import { RecommendationPerformanceMetrics, ConfidenceMetrics } from '@/lib/api/recommendation-metrics';
import { UsageStats } from '@/lib/generated/rayuelaAPI';
import { AccountInfo } from '@/lib/api';
import { ChecklistItem } from '@/types/checklist';
import React from 'react';
import { allRecommendationRules, RecommendationRule } from '@/lib/recommendationRules';
import { formatString, metricRecommendationsStrings } from '@/lib/constants';

// Types for metric recommendations
export interface Recommendation {
  id: string;
  title: string;
  description: string;
  priority: 'high' | 'medium' | 'low';
  category: 'accuracy' | 'diversity' | 'confidence' | 'performance';
  icon: React.ReactNode;
  actions: string[];
  metrics: {
    name: string;
    value: number;
    target: number;
    unit: string;
  }[];
}

/**
 * Helper function to safely access strings from metricRecommendationsStrings
 */
function getMetricString(key: string): string {
  return (metricRecommendationsStrings as Record<string, string>)[key] || key;
}

/**
 * Gets a value from an object using a path array
 *
 * @param obj The object to get the value from
 * @param path The path to the value as an array of keys
 * @returns The value at the path or undefined if not found
 */
function getValueByPath(obj: any, path: string[]): any {
  return path.reduce((acc, key) => (acc && acc[key] !== undefined) ? acc[key] : undefined, obj);
}

/**
 * Compares two values using the specified comparison operator
 *
 * @param value1 First value
 * @param value2 Second value
 * @param operator Comparison operator
 * @returns Result of the comparison
 */
function compareValues(value1: number, value2: number, operator: 'lt' | 'gt' | 'eq' | 'lte' | 'gte'): boolean {
  switch (operator) {
    case 'lt': return value1 < value2;
    case 'gt': return value1 > value2;
    case 'eq': return value1 === value2;
    case 'lte': return value1 <= value2;
    case 'gte': return value1 >= value2;
    default: return false;
  }
}

/**
 * Handles special logic for recommendation rules
 *
 * @param rule The rule with special logic
 * @param performanceData Performance metrics data
 * @param confidenceData Confidence metrics data
 * @param iconComponents Object containing icon components
 * @returns A recommendation object if the rule applies, null otherwise
 */
function handleSpecialLogic(
  rule: RecommendationRule,
  performanceData: RecommendationPerformanceMetrics,
  confidenceData: ConfidenceMetrics,
  iconComponents: Record<string, React.ElementType>
): Recommendation | null {
  if (!rule.specialLogic) return null;

  const { type, params } = rule.specialLogic;

  switch (type) {
    case 'avgConfidence': {
      if (!params?.paths || !params.threshold) return null;

      // Calculate average confidence across multiple paths
      const values = params.paths.map((path: string[]) => getValueByPath(confidenceData, path) || 0);
      const avgValue = values.reduce((sum: number, val: number) => sum + val, 0) / values.length;

      if (avgValue < params.threshold) {
        return {
          id: rule.id,
          title: formatString(getMetricString(rule.title), {}),
          description: formatString(getMetricString(rule.description), {}),
          priority: rule.priority,
          category: rule.category,
          icon: React.createElement(iconComponents[rule.iconKey], { className: "h-5 w-5 text-red-500" }),
          actions: rule.actions.map(action => getMetricString(action)),
          metrics: rule.metrics.map(metric => ({
            name: formatString(getMetricString(metric.name), {}),
            value: avgValue * (metric.valueMultiplier || 1),
            target: metric.target,
            unit: metric.unit
          }))
        };
      }
      return null;
    }

    case 'worstModel': {
      if (!params?.models || !params.threshold) return null;

      // Find the worst performing model
      const modelValues: Record<string, { value: number, name: string, actions: string[] }> = {};

      Object.entries(params.models).forEach(([modelKey, modelConfig]: [string, any]) => {
        const value = getValueByPath(confidenceData, modelConfig.path) || 0;
        modelValues[modelKey] = {
          value,
          name: (metricRecommendationsStrings as Record<string, string>)[modelConfig.name],
          actions: modelConfig.actions
        };
      });

      const worstModel = Object.entries(modelValues)
        .sort(([, a], [, b]) => a.value - b.value)[0];

      if (worstModel && worstModel[1].value < params.threshold) {
        const modelName = worstModel[1].name;

        return {
          id: `${rule.id}-${worstModel[0]}`,
          title: formatString(metricRecommendationsStrings[rule.title], { model: modelName }),
          description: formatString(metricRecommendationsStrings[rule.description], { model: modelName }),
          priority: rule.priority,
          category: rule.category,
          icon: React.createElement(iconComponents[rule.iconKey], { className: "h-5 w-5 text-amber-500" }),
          actions: worstModel[1].actions.map(action => metricRecommendationsStrings[action]),
          metrics: rule.metrics.map(metric => ({
            name: formatString(metricRecommendationsStrings[metric.name], { model: modelName }),
            value: worstModel[1].value * (metric.valueMultiplier || 1),
            target: metric.target,
            unit: metric.unit
          }))
        };
      }
      return null;
    }

    case 'lowConfidenceCategories': {
      if (!params?.threshold || !params.maxCategories) return null;

      if (Object.keys(confidenceData.category_confidence).length === 0) return null;

      const categories = Object.entries(confidenceData.category_confidence)
        .sort(([, a], [, b]) => a - b)
        .slice(0, params.maxCategories);

      if (categories[0][1] < params.threshold) {
        const worstCategory = categories[0][0];

        return {
          id: rule.id,
          title: formatString(metricRecommendationsStrings[rule.title], {}),
          description: formatString(metricRecommendationsStrings[rule.description], { category: worstCategory }),
          priority: rule.priority,
          category: rule.category,
          icon: React.createElement(iconComponents[rule.iconKey], { className: "h-5 w-5 text-amber-500" }),
          actions: rule.actions.map(action =>
            formatString(metricRecommendationsStrings[action], { category: worstCategory })
          ),
          metrics: categories.map(category => ({
            name: `Confianza en ${category[0]}`,
            value: category[1] * 100,
            target: params.threshold * 100,
            unit: '%'
          }))
        };
      }
      return null;
    }

    case 'lowestConfidenceFactor': {
      if (!params?.factors || !params.threshold) return null;

      const lowestFactor = Object.entries(confidenceData.confidence_factors)
        .sort(([, a], [, b]) => a - b)[0];

      if (lowestFactor[1] < params.threshold) {
        const factorKey = lowestFactor[0];
        const factorConfig = params.factors[factorKey];

        if (!factorConfig) return null;

        const factorName = metricRecommendationsStrings[factorConfig.name];

        return {
          id: `${rule.id}-${factorKey}`,
          title: formatString(metricRecommendationsStrings[rule.title], { factor: factorName }),
          description: formatString(metricRecommendationsStrings[rule.description], { factor: factorName }),
          priority: rule.priority,
          category: rule.category,
          icon: React.createElement(iconComponents[rule.iconKey], { className: "h-5 w-5 text-amber-500" }),
          actions: factorConfig.actions.map(action => metricRecommendationsStrings[action]),
          metrics: rule.metrics.map(metric => ({
            name: formatString(metricRecommendationsStrings[metric.name], { factor: factorName }),
            value: lowestFactor[1] * (metric.valueMultiplier || 1),
            target: metric.target,
            unit: metric.unit
          }))
        };
      }
      return null;
    }

    case 'negativeTrend': {
      if (!params?.minDays || !params.threshold || !params.path) return null;

      const trendsData = getValueByPath(confidenceData, params.path);

      if (trendsData && trendsData.length >= params.minDays) {
        const lastDays = trendsData.slice(-params.minDays);

        // Check if there's a negative trend
        if (lastDays[lastDays.length - 1].avg_confidence < lastDays[0].avg_confidence * params.threshold) {
          const changeValue = ((lastDays[lastDays.length - 1].avg_confidence / lastDays[0].avg_confidence) - 1) * 100;

          return {
            id: rule.id,
            title: formatString(metricRecommendationsStrings[rule.title], {}),
            description: formatString(metricRecommendationsStrings[rule.description], {}),
            priority: rule.priority,
            category: rule.category,
            icon: React.createElement(iconComponents[rule.iconKey], { className: "h-5 w-5 text-red-500" }),
            actions: rule.actions.map(action => metricRecommendationsStrings[action]),
            metrics: rule.metrics.map(metric => ({
              name: formatString(metricRecommendationsStrings[metric.name], {}),
              value: changeValue,
              target: metric.target,
              unit: metric.unit
            }))
          };
        }
      }
      return null;
    }

    default:
      return null;
  }
}

/**
 * Generates recommendations based on performance and confidence metrics
 *
 * @param performanceData Performance metrics data
 * @param confidenceData Confidence metrics data
 * @param iconComponents Object containing icon components to use in recommendations
 * @returns Array of recommendations
 */
export function generateMetricRecommendations(
  performanceData: RecommendationPerformanceMetrics,
  confidenceData: ConfidenceMetrics,
  iconComponents: Record<string, React.ElementType>
): Recommendation[] {
  const recommendations: Recommendation[] = [];

  // Process all rules
  allRecommendationRules.forEach(rule => {
    // Handle rules with special logic
    if (rule.specialLogic) {
      const specialRecommendation = handleSpecialLogic(rule, performanceData, confidenceData, iconComponents);
      if (specialRecommendation) {
        recommendations.push(specialRecommendation);
      }
      return;
    }

    // Handle standard rules
    const metricValue = getValueByPath(
      rule.metricPath[0] === 'summary' ? performanceData : confidenceData,
      rule.metricPath
    );

    if (metricValue !== undefined && compareValues(metricValue, rule.threshold, rule.comparison)) {
      recommendations.push({
        id: rule.id,
        title: formatString(metricRecommendationsStrings[rule.title], {}),
        description: formatString(metricRecommendationsStrings[rule.description], {}),
        priority: rule.priority,
        category: rule.category,
        icon: React.createElement(
          iconComponents[rule.iconKey],
          {
            className: `h-5 w-5 ${rule.priority === 'high' ? 'text-red-500' :
              rule.priority === 'medium' ? 'text-amber-500' :
                'text-blue-500'
              }`
          }
        ),
        actions: rule.actions.map(action => metricRecommendationsStrings[action]),
        metrics: rule.metrics.map(metric => {
          const value = getValueByPath(
            metric.valuePath[0] === 'summary' ? performanceData : confidenceData,
            metric.valuePath
          );

          return {
            name: formatString(metricRecommendationsStrings[metric.name], {}),
            value: value * (metric.valueMultiplier || 1),
            target: metric.target,
            unit: metric.unit
          };
        })
      });
    }
  });

  // Sort recommendations by priority
  const priorityOrder = { high: 0, medium: 1, low: 2 };
  recommendations.sort((a, b) => priorityOrder[a.priority] - priorityOrder[b.priority]);

  return recommendations;
}

/**
 * Analyzes account and usage data to determine which checklist items should be marked as completed
 *
 * @param accountData Account information from the API
 * @param usageData Usage information from the API
 * @param checklistItems Current checklist items
 * @param apiKeyData API Key data from useApiKeys hook (optional)
 * @returns Updated checklist items with completion status
 */
export function analyzeChecklistCompletion(
  accountData: AccountInfo,
  usageData: UsageStats,
  checklistItems: ChecklistItem[],
  apiKeyData?: any // API Key data from useApiKeys hook
): {
  updatedItems: ChecklistItem[],
  isNewApiKey: boolean,
  hasApiKey: boolean,
  hasSentCatalogData: boolean,
  hasSentInteractionData: boolean,
  hasTrainedModel: boolean,
  hasMadeApiCalls: boolean
} {
  // Check if the user has an API Key - use apiKeyData if available, fallback to legacy method
  const hasApiKey = apiKeyData ? !!apiKeyData : false;

  // Check if the user has seen the PostModalHighlight
  const hasSeenPostModal = localStorage.getItem('seenPostModalHighlight') === 'true';

  // Check if the API Key is recent (less than 24 hours) - use apiKeyData if available
  let isNewApiKey = false;
  if (apiKeyData && apiKeyData.created_at) {
    const keyCreatedAt = new Date(apiKeyData.created_at);
    const now = new Date();
    const hoursSinceCreation = (now.getTime() - keyCreatedAt.getTime()) / (1000 * 60 * 60);
    isNewApiKey = hoursSinceCreation < 24;
  }

  // Get storage details from usage data
  const storageDetails = usageData.storage?.details || {};

  // Check if the user has sent catalog data (products)
  const hasSentCatalogData = (storageDetails.products_count || 0) > 0;

  // Check if the user has sent interaction data
  const hasSentInteractionData = (storageDetails.interactions_count || 0) > 0;

  // Check if the user has trained a model
  const hasTrainedModel = usageData.training?.last_training_date !== null;

  // Check if the user has made API calls to the recommendations endpoint
  const hasMadeApiCalls = usageData.api_calls?.used > 0;

  // Update checklist items
  const updatedItems = checklistItems.map(item => {
    if (item.id === 'generate_key' && item.autoDetect) {
      // Mark as completed if the user has an API Key or has seen the PostModalHighlight
      return { ...item, completed: hasApiKey || hasSeenPostModal };
    }
    if (item.id === 'send_catalog_data' && item.autoDetect) {
      return { ...item, completed: hasSentCatalogData };
    }
    if (item.id === 'send_interaction_data' && item.autoDetect) {
      return { ...item, completed: hasSentInteractionData };
    }
    if (item.id === 'train_model' && item.autoDetect) {
      return { ...item, completed: hasTrainedModel };
    }
    if (item.id === 'first_recommendation' && item.autoDetect) {
      return { ...item, completed: hasMadeApiCalls };
    }
    return item;
  });

  return {
    updatedItems,
    isNewApiKey,
    hasApiKey,
    hasSentCatalogData,
    hasSentInteractionData,
    hasTrainedModel,
    hasMadeApiCalls
  };
}

/**
 * Creates a checklist state object for localStorage
 *
 * @param checklistItems Checklist items
 * @param hasApiKey Whether the user has an API Key
 * @param hasSeenPostModal Whether the user has seen the post-modal highlight
 * @param hasSentCatalogData Whether the user has sent catalog data
 * @param hasSentInteractionData Whether the user has sent interaction data
 * @param hasTrainedModel Whether the user has trained a model
 * @param hasMadeApiCalls Whether the user has made API calls
 * @returns Record of checklist item IDs and their completion status
 */
export function createChecklistState(
  checklistItems: ChecklistItem[],
  hasApiKey: boolean,
  hasSeenPostModal: boolean,
  hasSentCatalogData: boolean,
  hasSentInteractionData: boolean,
  hasTrainedModel: boolean,
  hasMadeApiCalls: boolean
): Record<string, boolean> {
  const checklistState: Record<string, boolean> = {};

  checklistItems.forEach(item => {
    if (item.id === 'generate_key' && item.autoDetect) {
      checklistState[item.id] = hasApiKey || hasSeenPostModal;
    } else if (item.id === 'send_catalog_data' && item.autoDetect) {
      checklistState[item.id] = hasSentCatalogData;
    } else if (item.id === 'send_interaction_data' && item.autoDetect) {
      checklistState[item.id] = hasSentInteractionData;
    } else if (item.id === 'train_model' && item.autoDetect) {
      checklistState[item.id] = hasTrainedModel;
    } else if (item.id === 'first_recommendation' && item.autoDetect) {
      checklistState[item.id] = hasMadeApiCalls;
    } else {
      checklistState[item.id] = item.completed;
    }
  });

  return checklistState;
}

/**
 * Determines if the checklist should be highlighted based on user actions
 *
 * @param hasSeenPostModal Whether the user has seen the post-modal highlight
 * @param isNewApiKey Whether the API Key is recent
 * @returns Whether the checklist should be highlighted
 */
export function shouldHighlightChecklist(
  hasSeenPostModal: boolean,
  isNewApiKey: boolean
): boolean {
  return hasSeenPostModal && !localStorage.getItem('checklistHighlighted');
}
