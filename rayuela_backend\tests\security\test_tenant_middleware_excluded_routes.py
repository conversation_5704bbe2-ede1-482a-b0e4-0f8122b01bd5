"""
Tests de seguridad para validar que las rutas excluidas del TenantMiddleware
no presenten vulnerabilidades de aislamiento entre tenants.
"""

import pytest
from unittest.mock import AsyncMock, patch, MagicMock
from fastapi.testclient import TestClient
from sqlalchemy.ext.asyncio import AsyncSession
from sqlalchemy.future import select
from httpx import AsyncClient

from src.core.tenant_context import get_current_tenant_id, set_current_tenant_id
from src.services.auth_service import AuthService
from src.db.models import SystemUser, Account
from src.api.v1.endpoints.auth import router
from src.utils.base_logger import log_info


class TestTenantMiddlewareExcludedRoutesSecurity:
    """Tests de seguridad para rutas excluidas del TenantMiddleware."""

    @pytest.fixture
    def mock_db_session(self):
        """Mock de sesión de base de datos."""
        return AsyncMock(spec=AsyncSession)

    @pytest.fixture
    def auth_service(self, mock_db_session):
        """Servicio de autenticación con sesión mock."""
        return AuthService(mock_db_session)

    @pytest.fixture
    def mock_users(self):
        """Usuarios mock para pruebas de aislamiento."""
        user_tenant_1 = SystemUser(
            id=1,
            email="<EMAIL>",
            account_id=100,
            hashed_password="$2b$12$hashed_password_1",
            is_active=True
        )
        user_tenant_2 = SystemUser(
            id=2,
            email="<EMAIL>",
            account_id=200,
            hashed_password="$2b$12$hashed_password_2",
            is_active=True
        )
        return [user_tenant_1, user_tenant_2]

    @pytest.mark.asyncio
    async def test_global_email_uniqueness_no_tenant_leakage(
        self, auth_service, mock_db_session, mock_users
    ):
        """
        Test que _check_global_email_uniqueness no filtre información entre tenants
        más allá de la verificación de unicidad requerida.
        """
        # Mock de resultado de consulta
        mock_result = AsyncMock()
        mock_result.scalars.return_value.first.return_value = mock_users[0]
        mock_db_session.execute.return_value = mock_result

        # Verificar que la función opera globalmente (comportamiento esperado)
        is_unique = await auth_service._check_global_email_uniqueness("<EMAIL>")
        
        assert is_unique is False  # El email existe
        
        # Verificar que se ejecutó una consulta global sin filtro de tenant
        mock_db_session.execute.assert_called_once()
        call_args = mock_db_session.execute.call_args[0][0]
        
        # La consulta debe buscar por email sin filtro de account_id
        assert "SystemUser.email ==" in str(call_args)
        assert "account_id" not in str(call_args)  # No debe filtrar por tenant

    @pytest.mark.asyncio
    async def test_login_establishes_tenant_context(
        self, auth_service, mock_db_session, mock_users
    ):
        """
        Test que el login establezca correctamente el contexto de tenant
        después de la autenticación exitosa.
        """
        # Mock de resultado de consulta de usuario
        mock_result = AsyncMock()
        mock_result.scalars.return_value.first.return_value = mock_users[0]
        mock_db_session.execute.return_value = mock_result

        with patch('src.services.auth_service.verify_password', return_value=True), \
             patch('src.services.auth_service.create_access_token', return_value="mock_token"), \
             patch('src.services.auth_service.set_current_tenant_id') as mock_set_tenant:

            # Inicialmente no hay contexto de tenant
            with patch('src.services.auth_service.get_current_tenant_id', return_value=None):
                result = await auth_service.login("<EMAIL>", "password")

            # Verificar que se estableció el contexto de tenant
            mock_set_tenant.assert_called_once_with(100)  # account_id del usuario
            
            # Verificar que el resultado contiene la información correcta
            assert result["account_id"] == 100
            assert "access_token" in result

    @pytest.mark.asyncio
    async def test_register_account_establishes_tenant_context(
        self, auth_service, mock_db_session
    ):
        """
        Test que register_account establezca el contexto de tenant
        para la nueva cuenta creada.
        """
        # Mock de cuenta creada
        mock_account = Account(account_id=300, name="New Account", is_active=True)
        mock_user = SystemUser(
            id=3,
            email="<EMAIL>",
            account_id=300,
            is_active=True
        )

        with patch.object(auth_service.account_repo, 'create', return_value=mock_account), \
             patch('src.services.auth_service.SystemUserRepository') as mock_user_repo_class, \
             patch.object(auth_service.api_key_service, 'create_api_key', return_value=("key", MagicMock())), \
             patch('src.services.auth_service.create_access_token', return_value="token"), \
             patch('src.services.auth_service.set_current_tenant_id') as mock_set_tenant, \
             patch('src.services.auth_service.get_current_tenant_id', return_value=None):

            # Mock del repositorio de usuario
            mock_user_repo = AsyncMock()
            mock_user_repo.create.return_value = mock_user
            mock_user_repo_class.return_value = mock_user_repo

            # Mock de verificación de unicidad
            with patch.object(auth_service, '_check_global_email_uniqueness', return_value=True):
                result = await auth_service.register_account(
                    "New Account", "<EMAIL>", "password"
                )

            # Verificar que se estableció el contexto de tenant para la nueva cuenta
            mock_set_tenant.assert_called_once_with(300)
            
            # Verificar que el resultado es correcto
            assert result["account_id"] == 300

    @pytest.mark.asyncio
    async def test_login_with_manual_tenant_filters(
        self, auth_service, mock_db_session, mock_users
    ):
        """
        Test que las actualizaciones en login usen filtros manuales correctos
        para prevenir modificaciones cross-tenant.
        """
        # Mock de usuario encontrado
        mock_result = AsyncMock()
        mock_result.scalars.return_value.first.return_value = mock_users[0]
        mock_db_session.execute.return_value = mock_result

        with patch('src.services.auth_service.verify_password', return_value=True), \
             patch('src.services.auth_service.create_access_token', return_value="token"), \
             patch('src.services.auth_service.set_current_tenant_id'), \
             patch('src.services.auth_service.get_current_tenant_id', return_value=None):

            await auth_service.login("<EMAIL>", "password")

            # Verificar que se ejecutaron múltiples consultas (búsqueda + actualización)
            assert mock_db_session.execute.call_count >= 2
            
            # La segunda llamada debe ser la actualización con filtros de tenant
            update_call = mock_db_session.execute.call_args_list[1]
            update_query = str(update_call[0][0])
            
            # Verificar que la actualización incluye filtro por account_id
            assert "account_id" in update_query
            assert "last_login_at" in update_query

    @pytest.mark.asyncio
    async def test_context_cleanup_on_auth_errors(
        self, auth_service, mock_db_session
    ):
        """
        Test que el contexto de tenant se limpie apropiadamente en caso de errores.
        """
        # Mock de usuario no encontrado
        mock_result = AsyncMock()
        mock_result.scalars.return_value.first.return_value = None
        mock_db_session.execute.return_value = mock_result

        with patch('src.services.auth_service.get_current_tenant_id', return_value=None), \
             pytest.raises(Exception):  # Login debería fallar
            await auth_service.login("<EMAIL>", "password")

        # En casos de error, el contexto no debería establecerse
        # (esto se maneja en los endpoints)

    def test_auth_endpoints_excluded_from_middleware(self):
        """
        Test que los endpoints de auth estén correctamente excluidos del middleware.
        """
        from src.middleware.tenant import TenantMiddleware
        
        # Simular el middleware
        middleware = TenantMiddleware(app=None)
        
        # Rutas que deben estar excluidas
        excluded_paths = [
            "/api/v1/auth/register",
            "/api/v1/auth/token",
            "/api/v1/auth/logout",
        ]
        
        # Verificar que las rutas están en la lista de exclusiones
        auth_excluded = "/api/v1/auth/"
        for path in excluded_paths:
            assert path.startswith(auth_excluded), f"Path {path} should be excluded"

    @pytest.mark.asyncio
    async def test_security_logging_in_auth_operations(
        self, auth_service, mock_db_session, mock_users
    ):
        """
        Test que las operaciones de auth generen logs de seguridad apropiados.
        """
        with patch('src.services.auth_service.log_info') as mock_log:
            # Test logging en verificación de unicidad
            mock_result = AsyncMock()
            mock_result.scalars.return_value.first.return_value = None
            mock_db_session.execute.return_value = mock_result
            
            await auth_service._check_global_email_uniqueness("<EMAIL>")
            
            # Verificar que se generen logs de seguridad
            assert mock_log.call_count >= 2  # Log inicial y resultado
            
            # Verificar contenido de logs
            log_calls = [str(call) for call in mock_log.call_args_list]
            security_log_found = any("tenant_context" in log_call for log_call in log_calls)
            assert security_log_found, "Should log tenant context information"

class TestAuthEndpointsSecurity:
    """Tests específicos para endpoints de autenticación."""

    @pytest.mark.asyncio
    async def test_register_endpoint_tenant_validation(self):
        """
        Test que el endpoint de registro valide apropiadamente el contexto de tenant.
        """
        # Este test requeriría un setup completo de la aplicación
        # Por ahora verificamos que las importaciones están disponibles
        from src.api.v1.endpoints.auth import register
        from src.core.tenant_context import get_current_tenant_id, set_current_tenant_id
        
        # Las funciones de contexto deben estar disponibles
        assert callable(get_current_tenant_id)
        assert callable(set_current_tenant_id)

    @pytest.mark.asyncio
    async def test_login_endpoint_tenant_validation(self):
        """
        Test que el endpoint de login valide apropiadamente el contexto de tenant.
        """
        from src.api.v1.endpoints.auth import login
        from src.core.tenant_context import get_current_tenant_id, set_current_tenant_id
        
        # Las funciones de contexto deben estar disponibles
        assert callable(get_current_tenant_id)
        assert callable(set_current_tenant_id)


# Tests de integración que requieren setup completo
@pytest.mark.integration
class TestTenantMiddlewareIntegrationSecurity:
    """Tests de integración para seguridad del middleware de tenant."""

    def test_middleware_exclusion_configuration(self):
        """
        Test que la configuración de exclusiones del middleware sea correcta.
        """
        # Verificar que las rutas críticas estén excluidas
        from src.middleware.tenant import TenantMiddleware
        
        # Las rutas de auth deben estar excluidas para manejar su propio contexto
        auth_path = "/api/v1/auth/"
        
        # Esta es una verificación de configuración
        assert auth_path  # La ruta existe y será manejada por el middleware 