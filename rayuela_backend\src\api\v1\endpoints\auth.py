"""
Endpoints de autenticación para el API de Rayuela.
"""

from typing import Optional
from fastapi import APIRouter, Depends, HTTPException, status, Header
from sqlalchemy.ext.asyncio import AsyncSession
from src.db import schemas, models, session
from src.db.schemas.auth import RegisterRequest, LoginRequest, RegisterResponse, LoginResponse
from src.core.deps import get_current_account, get_current_active_user, oauth2_scheme
from src.core.redis_utils import get_redis
from src.core.config import settings
from src.utils.base_logger import logger, log_info, log_error, log_warning
from redis.asyncio import Redis
from src.core.tenant_context import get_current_tenant_id, set_current_tenant_id
from src.core.exceptions import (
    InvalidCredentialsError,
    RateLimitExceededError,
    EmailNotVerifiedError,
)
from src.core.security import (
    get_password_hash,
    verify_password,
    create_access_token,
    revoke_token,
)
from src.core.security.api_key import generate_api_key, hash_api_key
from src.db.session import get_db
from src.db.models.system_user import SystemUser
from src.db.repositories.account import AccountRepository
from src.db.repositories.user import SystemUserRepository
from src.db.enums import RoleType, SubscriptionPlan, PLAN_LIMITS
from datetime import datetime, timezone
from src.services.email_verification_service import EmailVerificationService
from src.services.auth_service import AuthService

router = APIRouter()


@router.post("/send-verification-email", status_code=status.HTTP_202_ACCEPTED)
async def send_verification_email(
    current_user: SystemUser = Depends(get_current_active_user),
    db: AsyncSession = Depends(get_db),
):
    """Envía un email de verificación al usuario actual."""
    try:
        auth_service = AuthService(db)
        await auth_service.send_verification_email(current_user)
        return {"message": "Verification email sent successfully"}
    except Exception as e:
        log_error(f"Error sending verification email: {str(e)}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Error sending verification email",
        )


@router.get("/verify-email", status_code=status.HTTP_200_OK)
async def verify_email(
    token: str,
    db: AsyncSession = Depends(get_db),
):
    """Verifica el email de un usuario usando un token."""
    try:
        auth_service = AuthService(db)
        await auth_service.verify_email(token)
        return {"message": "Email verified successfully"}
    except Exception as e:
        log_error(f"Error verifying email: {str(e)}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Error verifying email",
        )


@router.post(
    "/register",
    response_model=RegisterResponse,
    summary="Register a new account",
    description="Creates a new account with global email uniqueness validation and returns a JWT token and the first API Key",
)
async def register(
    request: RegisterRequest,
    db: AsyncSession = Depends(session.get_db),
):
    """
    Register a new account with global email uniqueness validation.

    This endpoint creates a new account with an administrator user and generates
    your initial API Key that you'll need for all recommendation API requests.

    **CRITICAL**: This is the ONLY endpoint that provides an initial API Key during account creation.
    The API Key is only shown once, so make sure to save it securely. If you lose it, you'll need
    to generate a new one using the authenticated `/api/v1/api-keys/` endpoint.

    **SECURITY NOTE**: Email addresses must be globally unique across all accounts
    to prevent user confusion and potential security issues.

    **Authentication Flow**:
    1. Use this endpoint to register and get both JWT + API Key
    2. Use JWT for dashboard/management operations
    3. Use API Key for recommendation API calls
    4. Use `/auth/token` for subsequent logins (JWT only)
    5. Use `/api/v1/api-keys/` to manage API Keys when authenticated

    Parameters:
    - **account_name**: Name of your account/organization
    - **email**: Email address for the administrator user (must be globally unique)
    - **password**: Password for the administrator user (min 8 characters)

    Returns:
    - **access_token**: JWT token for dashboard authentication
    - **token_type**: Type of token (bearer)
    - **account_id**: ID of the created account
    - **user_id**: ID of the created administrator user
    - **is_admin**: Administrator status (always true for registration)
    - **api_key**: Your initial API Key for making recommendation API requests
    - **message**: Important information about the API Key

    Example request:
    ```json
    {
        "account_name": "My Company",
        "email": "<EMAIL>",
        "password": "securepassword123"
    }
    ```

    Example response:
    ```json
    {
        "access_token": "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9...",
        "token_type": "bearer",
        "account_id": 123,
        "user_id": 456,
        "is_admin": true,
        "api_key": "ray_aBcDeFgHiJkLmNoPqRsTuVwXyZ0123456789",
        "message": "Account registered successfully. This is your API Key - save it securely as it will only be shown once."
    }
    ```
    """
    try:
        # SEGURIDAD: Validar que no haya contexto de tenant conflictivo durante registro
        current_tenant = get_current_tenant_id()
        if current_tenant is not None:
            log_warning(f"Unexpected tenant context during registration: {current_tenant}")
            # Limpiar contexto para asegurar registro limpio
            set_current_tenant_id(None)
        
        auth_service = AuthService(db)
        result = await auth_service.register_account(
            account_name=request.account_name,
            email=request.email,
            password=request.password,
        )
        
        # SEGURIDAD: Log del contexto después del registro
        final_tenant = get_current_tenant_id()
        log_info(f"Registration completed - final tenant context: {final_tenant}")
        
        return result
    except Exception as e:
        log_error(f"Registration error: {str(e)}")
        # SEGURIDAD: Limpiar contexto en caso de error
        set_current_tenant_id(None)
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Error during registration",
        )


async def check_login_attempts(redis: Redis, account_id: int | object, email: str):
    """Verifica intentos de login para prevenir ataques de fuerza bruta"""
    key = f"login_attempts:{account_id}:{email}"
    attempts = await redis.get(key)

    if attempts and int(attempts) >= settings.MAX_LOGIN_ATTEMPTS:
        cooldown_key = f"login_cooldown:{account_id}:{email}"
        if await redis.exists(cooldown_key):
            raise RateLimitExceededError(
                f"Too many failed attempts. Please try again in {settings.LOGIN_COOLDOWN_MINUTES} minutes"
            )

        # Reset attempts after cooldown
        await redis.delete(key)

    return attempts


@router.post(
    "/token",
    response_model=LoginResponse,
    summary="Login to obtain JWT token",
    description="Authenticates a user and returns a JWT token for dashboard access. Accepts JSON payload with email and password.",
)
async def login(
    request_body: LoginRequest,
    db: AsyncSession = Depends(session.get_db),
    redis: Redis = Depends(get_redis),
    x_api_key: Optional[str] = Header(
        None,
        description="Optional API Key for account/tenant identification. If provided, it will be used to identify the account.",
    ),
):
    """
    Authenticate a user and return a JWT token for dashboard access.

    This endpoint authenticates a user with their email and password, and returns a JWT token
    that can be used for subsequent authenticated requests to the dashboard and management endpoints.

    **IMPORTANT**: This endpoint only returns a JWT token for user authentication.
    If you need an API Key for making recommendation API calls:
    - For new accounts: Use the `/auth/register` endpoint which provides both JWT and API Key
    - For existing accounts: Use the authenticated `/api/v1/api-keys/` endpoint to generate a new API Key

    The JWT token is used for authenticating user-specific operations in the dashboard,
    while the API Key is used for authenticating API requests for recommendation services.

    Parameters:
    - **email**: Email address of the user
    - **password**: User's password
    - **x_api_key**: Optional API Key for account/tenant identification

    Returns:
    - **access_token**: JWT token for authentication
    - **token_type**: Type of token (bearer)
    - **account_id**: ID of the user's account
    - **is_admin**: Whether the user has administrator privileges

    Example request:
    ```json
    {
        "email": "<EMAIL>",
        "password": "securepassword123"
    }
    ```

    Example response:
    ```json
    {
        "access_token": "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9...",
        "token_type": "bearer",
        "account_id": 123,
        "is_admin": true
    }
    ```
    """
    try:
        # SEGURIDAD: Log del contexto inicial antes del login
        current_tenant = get_current_tenant_id()
        log_info(f"Login attempt - initial tenant context: {current_tenant}")
        
        auth_service = AuthService(db)
        result = await auth_service.login(
            email=request_body.email, password=request_body.password, redis_client=redis
        )
        
        # SEGURIDAD: Verificar que el contexto se estableció correctamente
        final_tenant = get_current_tenant_id()
        log_info(f"Login completed - final tenant context: {final_tenant}")
        
        return result
    except Exception as e:
        log_error(f"Login error: {str(e)}")
        # SEGURIDAD: Limpiar contexto en caso de error
        set_current_tenant_id(None)
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Error during login",
        )


@router.post("/logout")
async def logout(
    token: str = Depends(oauth2_scheme),
    current_user: SystemUser = Depends(get_current_active_user),
    current_account: models.Account = Depends(get_current_account),
    db: AsyncSession = Depends(get_db),
):
    """Revoca el token JWT actual."""
    try:
        auth_service = AuthService(db)
        await auth_service.logout(token)
        return {"message": "Successfully logged out"}
    except Exception as e:
        log_error(f"Logout error: {str(e)}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Error during logout",
        )


# The /first-api-key endpoint has been removed for security reasons.
# API Keys are now only generated and returned during the registration process.
# If a user needs a new API Key, they should use the authenticated /api-keys endpoint.
