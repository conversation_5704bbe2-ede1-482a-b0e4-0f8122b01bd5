# Eliminación Física Programada para Soft Deletes

## 📋 Resumen

Este documento describe la implementación de la eliminación física programada para registros con soft delete en Rayuela. Esta estrategia complementa el sistema de soft deletes existente proporcionando una limpieza automática de registros que han excedido su período de retención final, reduciendo costos de almacenamiento y mejorando el rendimiento.

## 🎯 Objetivos

- **Reducir costos de almacenamiento**: Eliminar físicamente datos soft deleted antiguos
- **Mejorar rendimiento**: Mantener tablas más pequeñas y consultas más rápidas
- **Cumplimiento de retención**: Aplicar políticas de retención de datos a largo plazo
- **Preservar datos valiosos**: Archivar antes de eliminar cuando sea necesario

## 🏗️ Arquitectura

### Componentes Principales

1. **SoftDeleteCleanupService** (`src/services/soft_delete_cleanup_service.py`)
   - Servicio central para limpieza de soft deletes
   - Soporte para archivado opcional antes de eliminación
   - Procesamiento en lotes para seguridad
   - Verificación de integridad y rollback

2. **Tareas Celery de Limpieza**
   - `cleanup_soft_deleted_records`: Limpia registros soft deleted antiguos
   - `get_soft_delete_statistics`: Obtiene estadísticas de soft deletes
   - Ejecución programada mensual

3. **Endpoints API**
   - `/maintenance/cleanup-soft-deleted-records`
   - `/maintenance/soft-delete-statistics`

## 📊 Flujo de Eliminación Física

```mermaid
graph TD
    A[Tarea Celery Mensual] --> B[Identificar Soft Deletes Antiguos]
    B --> C{Archivado Habilitado?}
    C -->|Sí| D[Archivar a GCS]
    C -->|No| E[Proceder a Eliminación]
    D --> F{Archivado Exitoso?}
    F -->|Sí| E[Proceder a Eliminación]
    F -->|No| G[Registrar Error y Abortar]
    E --> H[Eliminar en Lotes]
    H --> I[Confirmar Operación]
    G --> J[Mantener Datos]
```

### Proceso Detallado

1. **Identificación**: Buscar registros con `is_active = FALSE` y `deleted_at < cutoff_date`
2. **Archivado Opcional**: Si está habilitado, exportar a GCS antes de eliminar
3. **Eliminación en Lotes**: Procesar en lotes pequeños para evitar bloqueos
4. **Verificación**: Confirmar que la eliminación fue exitosa
5. **Logging**: Registrar todas las operaciones para auditoría

## 🗂️ Modelos con Soft Delete

Los siguientes modelos soportan soft delete y son procesados por el servicio:

### **Accounts** (`accounts`)
- **Campos**: `is_active`, `deleted_at`
- **Retención**: 365 días después de soft delete
- **Archivado**: Recomendado (datos de facturación)

### **System Users** (`system_users`)
- **Campos**: `is_active`, `deleted_at`
- **Retención**: 365 días después de soft delete
- **Archivado**: Recomendado (datos de auditoría)

### **End Users** (`end_users`)
- **Campos**: `is_active`, `deleted_at`
- **Retención**: 365 días después de soft delete
- **Archivado**: Opcional (datos de preferencias)

### **Products** (`products`)
- **Campos**: `is_active`, `deleted_at`
- **Retención**: 365 días después de soft delete
- **Archivado**: Opcional (catálogo histórico)

## ⚙️ Configuración

### Variables de Entorno

```bash
# Configuración de limpieza de soft deletes
SOFT_DELETE_RETENTION_DAYS=365              # Días de retención después de soft delete
SOFT_DELETE_ARCHIVE_BEFORE_DELETION=true    # Archivar antes de eliminar
SOFT_DELETE_BATCH_SIZE=1000                 # Tamaño de lote para eliminación
```

### Configuración en `config.py`

```python
# Soft Delete Physical Cleanup Configuration
SOFT_DELETE_RETENTION_DAYS: int = int(os.getenv("SOFT_DELETE_RETENTION_DAYS", "365"))
SOFT_DELETE_ARCHIVE_BEFORE_DELETION: bool = os.getenv("SOFT_DELETE_ARCHIVE_BEFORE_DELETION", "true").lower() == "true"
SOFT_DELETE_BATCH_SIZE: int = int(os.getenv("SOFT_DELETE_BATCH_SIZE", "1000"))
```

## 📅 Programación de Tareas

### Beat Schedule (Celery)

```python
# Soft delete physical cleanup tasks
"cleanup-soft-deleted-records": {
    "task": "cleanup_soft_deleted_records",
    "schedule": 86400.0 * 30,  # Once a month
    "kwargs": {"retention_days": 365, "account_id": None, "dry_run": False},
    "options": {"queue": "maintenance"},
},
```

### Políticas de Retención

- **Período de Gracia**: 365 días después de soft delete (configurable)
- **Frecuencia**: Ejecución mensual automática
- **Batch Size**: 1,000 registros por lote (configurable)
- **Archivado**: Habilitado por defecto para preservar datos

## 🔧 Uso Manual

### API Endpoints

#### Limpiar Soft Deletes

```bash
curl -X POST "http://localhost:8001/api/v1/maintenance/cleanup-soft-deleted-records" \
  -H "Authorization: Bearer $TOKEN" \
  -H "Content-Type: application/json" \
  -d '{
    "retention_days": 365,
    "account_id": null,
    "dry_run": false,
    "run_async": true
  }'
```

#### Dry Run (Solo Reportar)

```bash
curl -X POST "http://localhost:8001/api/v1/maintenance/cleanup-soft-deleted-records" \
  -H "Authorization: Bearer $TOKEN" \
  -H "Content-Type: application/json" \
  -d '{
    "retention_days": 365,
    "account_id": 123,
    "dry_run": true,
    "run_async": false
  }'
```

#### Obtener Estadísticas

```bash
curl -X GET "http://localhost:8001/api/v1/maintenance/soft-delete-statistics?account_id=123" \
  -H "Authorization: Bearer $TOKEN"
```

### Ejecución Directa con Celery

```bash
# Limpieza para cuenta específica
celery -A src.workers.celery_app call cleanup_soft_deleted_records \
  --kwargs='{"retention_days": 365, "account_id": 123, "dry_run": false}'

# Dry run para todas las cuentas
celery -A src.workers.celery_app call cleanup_soft_deleted_records \
  --kwargs='{"retention_days": 365, "account_id": null, "dry_run": true}'

# Obtener estadísticas
celery -A src.workers.celery_app call get_soft_delete_statistics \
  --kwargs='{"account_id": null}'
```

## 💰 Impacto en Costos

### Estimaciones de Ahorro

**Antes (Solo Soft Delete)**:
- Accounts soft deleted: ~100 registros/mes → $0.02/mes
- System Users soft deleted: ~50 registros/mes → $0.01/mes
- End Users soft deleted: ~1000 registros/mes → $0.20/mes
- Products soft deleted: ~500 registros/mes → $0.10/mes
- **Total mensual**: ~$0.33

**Después (Soft Delete + Limpieza Física)**:
- Datos activos (últimos 365 días): ~30% → $0.10/mes
- Datos archivados en GCS: ~70% → $0.05/mes
- **Total mensual**: ~$0.15
- **Ahorro**: ~55% ($0.18/mes)

### Factores de Costo

1. **Reducción de índices**: Menos registros = índices más pequeños y rápidos
2. **Backups más pequeños**: Menos datos para respaldar
3. **Consultas más rápidas**: Menos filas para filtrar
4. **Archivado económico**: GCS es 85% más barato que Cloud SQL

## 🔍 Monitoreo y Alertas

### Métricas Clave

- **Registros elegibles para eliminación**: Por tabla y cuenta
- **Tasa de éxito de limpieza**: >98%
- **Tiempo de procesamiento**: <2 horas por ejecución mensual
- **Registros archivados vs eliminados**: Ratio de preservación

### Logs de Auditoría

Todas las operaciones de limpieza se registran con:
- Timestamp de inicio y fin
- Número de registros procesados por tabla
- Archivos creados en GCS (si aplica)
- Errores y reintentos
- Duración total de la operación

## 🚨 Manejo de Errores

### Estrategias de Recuperación

1. **Archivado Fallido**: No eliminar si el archivado falla
2. **Eliminación Parcial**: Continuar con siguientes lotes si uno falla
3. **Rollback de Lote**: Revertir lote completo si hay errores críticos
4. **Reintentos**: Hasta 3 intentos con backoff exponencial

### Escenarios de Error Comunes

- **Dependencias FK**: Verificar y manejar restricciones de clave foránea
- **Bloqueos de tabla**: Usar lotes pequeños y pausas entre lotes
- **Falta de espacio**: Alertar si GCS no tiene espacio suficiente
- **Permisos**: Verificar permisos de eliminación antes de proceder

## 📈 Migración y Rollout

### Fase 1: Implementación (Completada)
- ✅ Crear `SoftDeleteCleanupService`
- ✅ Implementar tareas Celery de limpieza
- ✅ Agregar endpoints API
- ✅ Configurar beat schedule mensual

### Fase 2: Pruebas (En Progreso)
- 🔄 Pruebas unitarias del servicio de limpieza
- 🔄 Pruebas de dry run con datos reales
- 🔄 Validación de archivado antes de eliminación
- 🔄 Pruebas de rendimiento con lotes grandes

### Fase 3: Despliegue (Pendiente)
- ⏳ Despliegue en staging con dry run habilitado
- ⏳ Monitoreo de métricas durante 1 mes
- ⏳ Despliegue en producción con eliminación real
- ⏳ Optimización basada en métricas reales

## 🔗 Referencias

- [SOFT_DELETES.md](./SOFT_DELETES.md) - Documentación de soft deletes
- [DATA_ARCHIVAL_STRATEGY.md](./DATA_ARCHIVAL_STRATEGY.md) - Estrategia de archivado
- [HIGH_VOLUME_TABLES.md](./HIGH_VOLUME_TABLES.md) - Tablas de alto volumen
- [PostgreSQL Row Level Security](https://www.postgresql.org/docs/current/ddl-rowsecurity.html)
