"use client";

import { useState } from 'react';
import useS<PERSON> from 'swr';
import { useAuth } from '@/lib/auth';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
// import { Skeleton } from '@/components/ui/skeleton'; // No usado actualmente
import { Button } from '@/components/ui/button';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { Tabs, TabsList, TabsTrigger } from '@/components/ui/tabs';
import { toast } from 'sonner';
import { handleApiError } from '@/lib/error-handler';
import { TooltipHelper, MetricDescription, metricDefinitions } from '@/components/ui/tooltip-helper';
import {
  BarChart3Icon,
  RefreshCwIcon,
  ShieldCheckIcon
} from 'lucide-react';
import RecommendationMetricsChart from '@/components/dashboard/RecommendationMetricsChart';
import ConfidenceMetricsChart from '@/components/dashboard/ConfidenceMetricsChart';
import MetricRecommendations from '@/components/dashboard/MetricRecommendations';
import { getRecommendationPerformance, getConfidenceMetrics } from '@/lib/api/recommendation-metrics';
import AdminRoute from '@/components/auth/AdminRoute';

export default function RecommendationMetricsPage() {
  const { token, apiKey } = useAuth();
  const [isRefreshing, setIsRefreshing] = useState(false);
  const [activeTab, setActiveTab] = useState('performance');
  const [selectedModelId, setSelectedModelId] = useState<string>('all');
  const [selectedMetricType, setSelectedMetricType] = useState<string>('all');

  // Obtener métricas de rendimiento de recomendaciones
  const {
    data: performanceData,
    error: performanceError,
    isLoading: isPerformanceLoading,
    mutate: mutatePerformance
  } = useSWR(
    token && apiKey ? ['recommendation-performance', selectedModelId, selectedMetricType] : null,
    async ([_, modelId, metricType]) => {
      return await getRecommendationPerformance(
        token!,
        apiKey!,
        modelId !== 'all' ? parseInt(modelId) : undefined,
        metricType !== 'all' ? metricType : undefined
      );
    }
  );

  // Obtener métricas de confianza
  const {
    data: confidenceData,
    error: confidenceError,
    isLoading: isConfidenceLoading,
    mutate: mutateConfidence
  } = useSWR(
    token && apiKey ? 'confidence-metrics' : null,
    async () => {
      return await getConfidenceMetrics(token!, apiKey!);
    }
  );

  // Función para refrescar los datos
  const handleRefresh = async () => {
    setIsRefreshing(true);
    try {
      await Promise.all([mutatePerformance(), mutateConfidence()]);
      toast.success('Métricas actualizadas');
    } catch (error: any) {
      handleApiError(error, 'Error al actualizar las métricas');
    } finally {
      setIsRefreshing(false);
    }
  };

  // Obtener lista de modelos disponibles para el filtro
  const availableModels = performanceData?.models.map(model => ({
    id: model.model_id.toString(),
    name: `${model.model_type} (${model.version})`
  })) || [];

  // Lista de tipos de métricas disponibles
  const metricTypes = [
    { id: 'precision', name: 'Precisión' },
    { id: 'recall', name: 'Recall' },
    { id: 'ndcg', name: 'NDCG' },
    { id: 'map', name: 'MAP' },
    { id: 'catalog_coverage', name: 'Cobertura del Catálogo' },
    { id: 'diversity', name: 'Diversidad' },
    { id: 'novelty', name: 'Novedad' },
    { id: 'serendipity', name: 'Serendipia' },
  ];

  return (
    <AdminRoute>
      <div className="container mx-auto py-8 space-y-8">
        {/* Header con agrupación visual mejorada */}
        <div className="bg-card/30 dark:bg-card/20 border border-border/50 rounded-lg p-6">
          <div className="flex justify-between items-start">
            <div className="space-y-2">
              <div className="flex items-center gap-2">
                <h1 className="text-3xl font-bold tracking-tight">
                  Métricas de Recomendación
                </h1>
              <TooltipHelper
                content={
                  <div className="space-y-3 max-w-md">
                    <h4 className="font-semibold text-gray-900 dark:text-gray-100">Acerca de las Métricas de Recomendación</h4>
                    <p className="text-gray-700 dark:text-gray-300">
                      Esta página muestra métricas detalladas sobre el rendimiento y la confianza de los modelos de recomendación.
                    </p>
                    <div>
                      <h5 className="font-medium text-gray-800 dark:text-gray-200">Métricas de Rendimiento:</h5>
                      <ul className="list-disc pl-4 text-gray-700 dark:text-gray-300">
                        <li><span className="font-medium">Precisión y Recall:</span> Miden la relevancia de las recomendaciones</li>
                        <li><span className="font-medium">NDCG y MAP:</span> Evalúan la calidad del ranking de recomendaciones</li>
                        <li><span className="font-medium">Diversidad y Novedad:</span> Miden la variedad y originalidad de las recomendaciones</li>
                        <li><span className="font-medium">Serendipia:</span> Evalúa la capacidad de sorprender positivamente al usuario</li>
                      </ul>
                    </div>
                    <div>
                      <h5 className="font-medium text-gray-800 dark:text-gray-200">Métricas de Confianza:</h5>
                      <ul className="list-disc pl-4 text-gray-700 dark:text-gray-300">
                        <li><span className="font-medium">Distribución de Confianza:</span> Niveles de confianza por tipo de modelo</li>
                        <li><span className="font-medium">Confianza por Categoría:</span> Qué categorías generan recomendaciones más confiables</li>
                        <li><span className="font-medium">Factores de Confianza:</span> Qué aspectos influyen más en la confianza</li>
                        <li><span className="font-medium">Tendencias:</span> Evolución de la confianza a lo largo del tiempo</li>
                      </ul>
                    </div>
                    <p className="text-gray-700 dark:text-gray-300 italic">
                      Pase el cursor sobre cualquier métrica para obtener información detallada sobre su significado y cálculo.
                    </p>
                  </div>
                }
                iconSize={18}
                side="right"
                align="start"
                iconClassName="mt-2"
              />
            </div>
            <p className="text-gray-600 dark:text-gray-400 mt-2">
              Análisis detallado del rendimiento y confianza de los modelos de recomendación
            </p>
          </div>
          <div className="flex items-center gap-2">
            <Button
              variant="outline"
              onClick={handleRefresh}
              disabled={isRefreshing || isPerformanceLoading || isConfidenceLoading}
              className="flex items-center gap-2"
            >
              <RefreshCwIcon className={`h-4 w-4 ${isRefreshing ? 'animate-spin' : ''}`} />
              Actualizar
            </Button>
          </div>
        </div>

        {/* Recomendaciones automáticas */}
        <MetricRecommendations
          performanceData={performanceData}
          confidenceData={confidenceData}
          isLoading={isPerformanceLoading || isConfidenceLoading}
        />

        {/* Tabs para cambiar entre métricas de rendimiento y confianza */}
        <Tabs value={activeTab} onValueChange={setActiveTab} className="w-full">
          <TabsList className="grid grid-cols-2 w-full max-w-md">
            <TabsTrigger value="performance" className="flex items-center gap-2">
              <BarChart3Icon className="h-4 w-4" />
              Rendimiento
            </TabsTrigger>
            <TabsTrigger value="confidence" className="flex items-center gap-2">
              <ShieldCheckIcon className="h-4 w-4" />
              Confianza
            </TabsTrigger>
          </TabsList>
        </Tabs>

        {/* Contenido de la pestaña de rendimiento */}
        {activeTab === 'performance' && (
          <div className="space-y-6">
            {/* Filtros para métricas de rendimiento */}
            <div className="flex flex-wrap gap-4">
              <div className="w-full md:w-auto">
                <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">
                  Modelo
                </label>
                <Select value={selectedModelId} onValueChange={setSelectedModelId}>
                  <SelectTrigger className="w-full md:w-[200px]">
                    <SelectValue placeholder="Todos los modelos" />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="all">Todos los modelos</SelectItem>
                    {availableModels.map(model => (
                      <SelectItem key={model.id} value={model.id}>{model.name}</SelectItem>
                    ))}
                  </SelectContent>
                </Select>
              </div>
              <div className="w-full md:w-auto">
                <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">
                  Tipo de Métrica
                </label>
                <Select value={selectedMetricType} onValueChange={setSelectedMetricType}>
                  <SelectTrigger className="w-full md:w-[200px]">
                    <SelectValue placeholder="Todas las métricas" />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="all">Todas las métricas</SelectItem>
                    {metricTypes.map(metric => (
                      <SelectItem key={metric.id} value={metric.id}>{metric.name}</SelectItem>
                    ))}
                  </SelectContent>
                </Select>
              </div>
            </div>

            {/* Tarjetas de resumen */}
            {performanceData?.summary && (
              <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
                <Card>
                  <CardHeader className="pb-2">
                    <div className="flex items-center gap-2">
                      <CardTitle className="text-sm font-medium text-gray-500 dark:text-gray-400">Precisión</CardTitle>
                      <TooltipHelper
                        content={<MetricDescription {...metricDefinitions.precision} />}
                        iconSize={14}
                      />
                    </div>
                  </CardHeader>
                  <CardContent>
                    <div className="text-2xl font-bold">{(performanceData.summary.precision * 100).toFixed(2)}%</div>
                  </CardContent>
                </Card>
                <Card>
                  <CardHeader className="pb-2">
                    <div className="flex items-center gap-2">
                      <CardTitle className="text-sm font-medium text-gray-500 dark:text-gray-400">NDCG</CardTitle>
                      <TooltipHelper
                        content={<MetricDescription {...metricDefinitions.ndcg} />}
                        iconSize={14}
                      />
                    </div>
                  </CardHeader>
                  <CardContent>
                    <div className="text-2xl font-bold">{(performanceData.summary.ndcg * 100).toFixed(2)}%</div>
                  </CardContent>
                </Card>
                <Card>
                  <CardHeader className="pb-2">
                    <div className="flex items-center gap-2">
                      <CardTitle className="text-sm font-medium text-gray-500 dark:text-gray-400">Diversidad</CardTitle>
                      <TooltipHelper
                        content={<MetricDescription {...metricDefinitions.diversity} />}
                        iconSize={14}
                      />
                    </div>
                  </CardHeader>
                  <CardContent>
                    <div className="text-2xl font-bold">{(performanceData.summary.diversity * 100).toFixed(2)}%</div>
                  </CardContent>
                </Card>
                <Card>
                  <CardHeader className="pb-2">
                    <div className="flex items-center gap-2">
                      <CardTitle className="text-sm font-medium text-gray-500 dark:text-gray-400">Novedad</CardTitle>
                      <TooltipHelper
                        content={<MetricDescription {...metricDefinitions.novelty} />}
                        iconSize={14}
                      />
                    </div>
                  </CardHeader>
                  <CardContent>
                    <div className="text-2xl font-bold">{(performanceData.summary.novelty * 100).toFixed(2)}%</div>
                  </CardContent>
                </Card>
              </div>
            )}

            {/* Gráfico de métricas de rendimiento */}
            <RecommendationMetricsChart
              data={performanceData}
              isLoading={isPerformanceLoading}
              error={performanceError}
              title="Métricas de Rendimiento"
              description="Análisis detallado del rendimiento de los modelos de recomendación"
            />
          </div>
        )}

        {/* Contenido de la pestaña de confianza */}
        {activeTab === 'confidence' && (
          <div className="space-y-6">
            {/* Tarjetas de resumen de confianza */}
            {confidenceData && (
              <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
                <Card>
                  <CardHeader className="pb-2">
                    <div className="flex items-center gap-2">
                      <CardTitle className="text-sm font-medium text-gray-500 dark:text-gray-400">Confianza Promedio</CardTitle>
                      <TooltipHelper
                        content={<MetricDescription {...metricDefinitions.confidence} />}
                        iconSize={14}
                      />
                    </div>
                  </CardHeader>
                  <CardContent>
                    <div className="text-2xl font-bold">
                      {(
                        (confidenceData.confidence_distribution.collaborative.avg +
                          confidenceData.confidence_distribution.content.avg +
                          confidenceData.confidence_distribution.hybrid.avg) / 3 * 100
                      ).toFixed(2)}%
                    </div>
                  </CardContent>
                </Card>
                <Card>
                  <CardHeader className="pb-2">
                    <div className="flex items-center gap-2">
                      <CardTitle className="text-sm font-medium text-gray-500 dark:text-gray-400">Modelo Más Confiable</CardTitle>
                      <TooltipHelper
                        content={
                          <div className="space-y-2">
                            <p className="text-gray-700 dark:text-gray-300">
                              Tipo de modelo que genera recomendaciones con mayor nivel de confianza en promedio.
                            </p>
                            <ul className="list-disc pl-4 text-gray-700 dark:text-gray-300">
                              <li><span className="font-medium">Colaborativo:</span> Basado en comportamientos similares de usuarios</li>
                              <li><span className="font-medium">Contenido:</span> Basado en atributos de los productos</li>
                              <li><span className="font-medium">Híbrido:</span> Combinación de ambos enfoques</li>
                            </ul>
                          </div>
                        }
                        iconSize={14}
                      />
                    </div>
                  </CardHeader>
                  <CardContent>
                    <div className="text-2xl font-bold">
                      {confidenceData.confidence_distribution.collaborative.avg > confidenceData.confidence_distribution.content.avg &&
                        confidenceData.confidence_distribution.collaborative.avg > confidenceData.confidence_distribution.hybrid.avg
                        ? 'Colaborativo'
                        : confidenceData.confidence_distribution.content.avg > confidenceData.confidence_distribution.hybrid.avg
                          ? 'Contenido'
                          : 'Híbrido'
                      }
                    </div>
                  </CardContent>
                </Card>
                <Card>
                  <CardHeader className="pb-2">
                    <div className="flex items-center gap-2">
                      <CardTitle className="text-sm font-medium text-gray-500 dark:text-gray-400">Categoría Más Confiable</CardTitle>
                      <TooltipHelper
                        content={
                          <div className="space-y-2">
                            <p className="text-gray-700 dark:text-gray-300">
                              Categoría de productos para la cual el sistema genera recomendaciones con mayor nivel de confianza.
                            </p>
                            <p className="text-gray-700 dark:text-gray-300">
                              Una alta confianza en una categoría específica puede indicar que:
                            </p>
                            <ul className="list-disc pl-4 text-gray-700 dark:text-gray-300">
                              <li>Hay más datos de interacciones en esta categoría</li>
                              <li>Los patrones de preferencia son más claros</li>
                              <li>Los atributos de los productos son más distintivos</li>
                            </ul>
                          </div>
                        }
                        iconSize={14}
                      />
                    </div>
                  </CardHeader>
                  <CardContent>
                    <div className="text-2xl font-bold">
                      {Object.entries(confidenceData.category_confidence).length > 0
                        ? Object.entries(confidenceData.category_confidence)
                          .sort((a, b) => b[1] - a[1])[0][0]
                        : 'N/A'
                      }
                    </div>
                  </CardContent>
                </Card>
                <Card>
                  <CardHeader className="pb-2">
                    <div className="flex items-center gap-2">
                      <CardTitle className="text-sm font-medium text-gray-500 dark:text-gray-400">Factor Principal</CardTitle>
                      <TooltipHelper
                        content={
                          <div className="space-y-2">
                            <p className="text-gray-700 dark:text-gray-300">
                              Factor que más influye en la confianza de las recomendaciones según el análisis del sistema.
                            </p>
                            <ul className="list-disc pl-4 text-gray-700 dark:text-gray-300">
                              <li><span className="font-medium">Tamaño Historial Usuario:</span> Cantidad de interacciones previas</li>
                              <li><span className="font-medium">Popularidad del Ítem:</span> Qué tan común es el producto</li>
                              <li><span className="font-medium">Fuerza de Categoría:</span> Afinidad del usuario por la categoría</li>
                              <li><span className="font-medium">Tipo de Modelo:</span> Algoritmo utilizado para la recomendación</li>
                            </ul>
                          </div>
                        }
                        iconSize={14}
                      />
                    </div>
                  </CardHeader>
                  <CardContent>
                    <div className="text-2xl font-bold">
                      {Object.entries(confidenceData.confidence_factors)
                        .sort((a, b) => b[1] - a[1])[0][0]
                        .split('_')
                        .map(word => word.charAt(0).toUpperCase() + word.slice(1))
                        .join(' ')
                      }
                    </div>
                  </CardContent>
                </Card>
              </div>
            )}

            {/* Gráfico de métricas de confianza */}
            <ConfidenceMetricsChart
              data={confidenceData}
              isLoading={isConfidenceLoading}
              error={confidenceError}
              title="Métricas de Confianza"
              description="Análisis detallado de la confianza en las recomendaciones"
            />
          </div>
        )}
      </div>
    </AdminRoute>
  );
}
