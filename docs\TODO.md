# Rayuela - TODO y Próximos Pasos

## 🚀 **Estado Actual del Proyecto**

### ✅ **Completado Recientemente:**
- ✅ Migración al cliente API generado
- ✅ Fixes de seguridad (timing attacks, API key uniqueness)
- ✅ Optimización de pipeline CI/CD
- ✅ Configuración de despliegue en producción
- ✅ Limpieza de scripts y documentación obsoleta

## 🎯 **Prioridades Inmediatas**

### 🔥 **CRÍTICO - Despliegue en Producción**
- [ ] **Completar despliegue actual en GCP**
  - Estado: En progreso con mejoras de conectividad DB
  - Configuración: Cloud Build con Cloud SQL Proxy mejorado
  - Próximo: Verificar estado del build actual

### 🛡️ **ALTA - Seguridad y Calidad**
- [ ] **Eliminar permisividad en build del frontend**
  - Cambiar `ignoreBuildErrors: true` a `false` en `tsconfig.json`
  - Eliminar `ignoreDuringBuilds: true` en `eslint.config.mjs`
  - Resolver todos los errores de TypeScript y ESLint

- [ ] **Optimizar granularidad de auditoría**
  - Modificar `AuditMiddleware` para registrar solo acciones críticas
  - Enviar logs de acceso completos a Cloud Logging
  - Reducir costos de almacenamiento en Cloud SQL

### 📊 **MEDIA - Optimización y Monitoreo**
- [ ] **Configurar monitoreo proactivo**
  - Habilitar `pg_stat_statements` en Cloud SQL
  - Configurar dashboards en Cloud Monitoring
  - Establecer alertas de presupuesto y rendimiento

- [ ] **Optimizar conexiones de base de datos**
  - Ajustar `worker_connections` en Gunicorn
  - Alinear `max_connections` en Cloud SQL
  - Monitorear uso de conexiones

## 🔧 **Tareas de Desarrollo**

### 🎨 **Frontend**
- [ ] **Migración completa al cliente API generado**
  - Ejecutar script de migración de componentes
  - Reemplazar `api.ts` con `api-generated.ts`
  - Verificar funcionamiento en todas las páginas

### 🗄️ **Backend**
- [ ] **Optimización de base de datos**
  - Añadir índices GIN para columnas JSONB frecuentemente consultadas
  - Consolidar métricas de uso en tabla `Subscription`
  - Aumentar `SOFT_DELETE_BATCH_SIZE` para limpieza más eficiente

### 🌐 **Infraestructura**
- [ ] **Configuración de dominio personalizado**
  - Configurar SSL/TLS certificado
  - Configurar DNS y routing
  - Actualizar configuración de CORS

## 📋 **Backlog de Mejoras**

### 🔍 **Monitoreo y Observabilidad**
- [ ] Implementar métricas de negocio personalizadas
- [ ] Configurar alertas de SLA y uptime
- [ ] Dashboard de métricas de uso por cuenta

### 🚀 **Escalabilidad**
- [ ] Evaluar necesidad de sharding por `account_id`
- [ ] Optimizar queries N+1 identificadas
- [ ] Implementar caching adicional con Redis

### 🔐 **Seguridad Avanzada**
- [ ] Implementar rate limiting por IP
- [ ] Configurar WAF (Web Application Firewall)
- [ ] Auditoría de seguridad completa

## 📝 **Notas de Desarrollo**

### 🛠️ **Comandos Útiles**
```bash
# Despliegue en producción
bash scripts/deploy-production.sh --direct

# Verificar estado de servicios
bash scripts/verify-production-deployment.sh

# Generar cliente API
cd rayuela_frontend && npm run generate-api:dev

# Ejecutar migraciones
cd rayuela_backend && alembic upgrade head
```

### 📚 **Documentación Relevante**
- `docs/DEPLOYMENT_GUIDE.md` - Guía completa de despliegue
- `docs/ARCHITECTURE.md` - Arquitectura del sistema
- `docs/VPC_SECURITY_SETUP.md` - Configuración de seguridad
- `docs/DATA_ARCHIVAL_STRATEGY.md` - Estrategia de archivado

---

**Última actualización:** $(date '+%Y-%m-%d')
**Estado del proyecto:** En despliegue activo en GCP