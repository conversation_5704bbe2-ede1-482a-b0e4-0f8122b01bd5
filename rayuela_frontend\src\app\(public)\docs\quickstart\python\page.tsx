import { Card, CardContent, CardDescription, Card<PERSON><PERSON>er, CardTitle } from "@/components/ui/card";
import { But<PERSON> } from "@/components/ui/button";
import Link from "next/link";
import { generateMetadata as generateSEOMetadata, generateJsonLd } from '@/lib/seo';
import { Code, Copy, ExternalLink } from "lucide-react";

export const metadata = generateSEOMetadata({
  title: 'Inicio Rápido con Python - Documentación',
  description: 'Aprende a integrar Rayuela en tu aplicación Python en menos de 5 minutos. Guía paso a paso con ejemplos de código.',
  path: '/docs/quickstart/python',
  keywords: ['python', 'quickstart', 'tutorial', 'API', 'integración', 'SDK'],
  type: 'article',
});

export default function PythonQuickstartPage() {
  const articleSchema = generateJsonLd('APIReference', {
    name: 'Rayuela Python Quickstart Guide',
    description: 'Complete guide to integrate Rayuela recommendation system with Python',
    url: 'https://rayuela.ai/docs/quickstart/python',
    programmingLanguage: 'Python',
  });

  return (
    <>
      <script
        type="application/ld+json"
        dangerouslySetInnerHTML={{
          __html: JSON.stringify(articleSchema),
        }}
      />
      
      <div className="min-h-screen bg-gray-50 dark:bg-gray-900">
        <div className="container mx-auto px-4 py-16 max-w-4xl">
          {/* Breadcrumb */}
          <nav className="mb-8 text-sm">
            <Link href="/docs" className="text-blue-600 hover:text-blue-800">Documentación</Link>
            <span className="mx-2 text-gray-500">/</span>
            <Link href="/docs/quickstart" className="text-blue-600 hover:text-blue-800">Inicio Rápido</Link>
            <span className="mx-2 text-gray-500">/</span>
            <span className="text-gray-700">Python</span>
          </nav>

          {/* Header */}
          <div className="mb-12">
            <h1 className="text-4xl font-bold text-gray-900 dark:text-white mb-4">
              🐍 Inicio Rápido con Python
            </h1>
            <p className="text-xl text-gray-600 dark:text-gray-300">
              Integra Rayuela en tu aplicación Python en menos de 5 minutos
            </p>
          </div>

          {/* Prerequisites */}
          <Card className="mb-8">
            <CardHeader>
              <CardTitle className="flex items-center">
                <Code className="w-5 h-5 mr-2" />
                Requisitos Previos
              </CardTitle>
            </CardHeader>
            <CardContent>
              <ul className="list-disc list-inside space-y-2 text-gray-700 dark:text-gray-300">
                <li>Python 3.7 o superior</li>
                <li>pip (gestor de paquetes de Python)</li>
                <li>Una cuenta en Rayuela (gratis)</li>
                <li>Tu API Key de Rayuela</li>
              </ul>
            </CardContent>
          </Card>

          {/* Step 1: Installation */}
          <Card className="mb-8">
            <CardHeader>
              <CardTitle>1. Instalación</CardTitle>
              <CardDescription>Instala el SDK de Python para Rayuela</CardDescription>
            </CardHeader>
            <CardContent>
              <div className="bg-gray-900 rounded-lg p-4 mb-4">
                <code className="text-green-400 text-sm">
                  pip install rayuela-python
                </code>
              </div>
              <p className="text-sm text-gray-600 dark:text-gray-400">
                O si usas poetry:
              </p>
              <div className="bg-gray-900 rounded-lg p-4 mt-2">
                <code className="text-green-400 text-sm">
                  poetry add rayuela-python
                </code>
              </div>
            </CardContent>
          </Card>

          {/* Step 2: Configuration */}
          <Card className="mb-8">
            <CardHeader>
              <CardTitle>2. Configuración</CardTitle>
              <CardDescription>Configura tu cliente de Rayuela</CardDescription>
            </CardHeader>
            <CardContent>
              <div className="bg-gray-900 rounded-lg p-4 mb-4">
                <pre className="text-green-400 text-sm overflow-x-auto">
{`from rayuela import RayuelaClient

# Inicializa el cliente
client = RayuelaClient(
    api_key="tu-api-key-aqui",
    base_url="https://api.rayuela.ai"  # Opcional
)

# Verifica la conexión
try:
    health = client.health_check()
    print("✅ Conexión exitosa:", health)
except Exception as e:
    print("❌ Error de conexión:", e)`}
                </pre>
              </div>
              <div className="bg-blue-50 dark:bg-blue-900/20 border border-blue-200 dark:border-blue-800 rounded-lg p-4">
                <p className="text-sm text-blue-800 dark:text-blue-200">
                  <strong>💡 Tip:</strong> Nunca hardcodees tu API key. Usa variables de entorno:
                </p>
                <div className="bg-gray-900 rounded-lg p-2 mt-2">
                  <code className="text-green-400 text-xs">
                    import os<br/>
                    api_key = os.getenv("RAYUELA_API_KEY")
                  </code>
                </div>
              </div>
            </CardContent>
          </Card>

          {/* Step 3: First Recommendation */}
          <Card className="mb-8">
            <CardHeader>
              <CardTitle>3. Tu Primera Recomendación</CardTitle>
              <CardDescription>Obtén recomendaciones para un usuario</CardDescription>
            </CardHeader>
            <CardContent>
              <div className="bg-gray-900 rounded-lg p-4 mb-4">
                <pre className="text-green-400 text-sm overflow-x-auto">
{`# Obtener recomendaciones para un usuario
recommendations = client.get_recommendations(
    user_id="user_123",
    limit=5,
    filters={
        "category": "electronics",
        "price_max": 1000
    }
)

print("Recomendaciones:")
for rec in recommendations:
    print(f"- Producto {rec['product_id']}: {rec['score']:.2f}")`}
                </pre>
              </div>
              
              <h4 className="font-semibold mb-2">Respuesta esperada:</h4>
              <div className="bg-gray-100 dark:bg-gray-800 rounded-lg p-4">
                <pre className="text-sm text-gray-700 dark:text-gray-300">
{`Recomendaciones:
- Producto prod_456: 0.95
- Producto prod_789: 0.87
- Producto prod_123: 0.82
- Producto prod_321: 0.78
- Producto prod_654: 0.75`}
                </pre>
              </div>
            </CardContent>
          </Card>

          {/* Step 4: Send Data */}
          <Card className="mb-8">
            <CardHeader>
              <CardTitle>4. Enviar Datos de Interacción</CardTitle>
              <CardDescription>Mejora las recomendaciones enviando datos de comportamiento</CardDescription>
            </CardHeader>
            <CardContent>
              <div className="bg-gray-900 rounded-lg p-4 mb-4">
                <pre className="text-green-400 text-sm overflow-x-auto">
{`# Registrar una interacción
client.track_interaction(
    user_id="user_123",
    product_id="prod_456",
    interaction_type="view",
    timestamp="2024-01-15T10:30:00Z"
)

# Registrar una compra
client.track_interaction(
    user_id="user_123",
    product_id="prod_456",
    interaction_type="purchase",
    value=299.99,
    timestamp="2024-01-15T10:35:00Z"
)`}
                </pre>
              </div>
            </CardContent>
          </Card>

          {/* Next Steps */}
          <Card className="mb-8">
            <CardHeader>
              <CardTitle>🚀 Próximos Pasos</CardTitle>
            </CardHeader>
            <CardContent>
              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                <Link 
                  href="/docs/guides/data-ingestion"
                  className="p-4 border rounded-lg hover:shadow-md transition-shadow"
                >
                  <h4 className="font-semibold mb-2">📊 Ingesta de Datos</h4>
                  <p className="text-sm text-gray-600 dark:text-gray-400">
                    Aprende a enviar datos de productos y usuarios
                  </p>
                </Link>
                
                <Link 
                  href="/docs/api/recommendations"
                  className="p-4 border rounded-lg hover:shadow-md transition-shadow"
                >
                  <h4 className="font-semibold mb-2">🎯 API de Recomendaciones</h4>
                  <p className="text-sm text-gray-600 dark:text-gray-400">
                    Referencia completa de la API
                  </p>
                </Link>
                
                <Link 
                  href="/docs/guides/cold-start"
                  className="p-4 border rounded-lg hover:shadow-md transition-shadow"
                >
                  <h4 className="font-semibold mb-2">🆕 Cold Start</h4>
                  <p className="text-sm text-gray-600 dark:text-gray-400">
                    Manejo de usuarios y productos nuevos
                  </p>
                </Link>
                
                <Link 
                  href="/docs/examples"
                  className="p-4 border rounded-lg hover:shadow-md transition-shadow"
                >
                  <h4 className="font-semibold mb-2">💡 Ejemplos</h4>
                  <p className="text-sm text-gray-600 dark:text-gray-400">
                    Casos de uso y ejemplos completos
                  </p>
                </Link>
              </div>
            </CardContent>
          </Card>

          {/* Support */}
          <div className="text-center">
            <p className="text-gray-600 dark:text-gray-400 mb-4">
              ¿Necesitas ayuda? Estamos aquí para apoyarte.
            </p>
            <div className="flex justify-center gap-4">
              <Button variant="outline" asChild>
                <Link href="/docs">
                  <ExternalLink className="w-4 h-4 mr-2" />
                  Ver más documentación
                </Link>
              </Button>
              <Button asChild>
                <Link href="/contact">Contactar Soporte</Link>
              </Button>
            </div>
          </div>
        </div>
      </div>
    </>
  );
}
