"use client";

import React from 'react';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import {
  CheckCircle,
  AlertTriangle,
  Info,
  LightbulbIcon,
  KeyIcon,
  BookOpenIcon,
  ChevronRightIcon
} from 'lucide-react';
import { SemanticIcon, IconWithText } from '@/components/ui/icon';

export default function IconographyGuide() {
  return (
    <div className="space-y-6 max-w-4xl mx-auto p-6">
      <div className="text-center space-y-2">
        <h1 className="text-3xl font-bold">Guía de Iconografía Mejorada</h1>
        <p className="text-lg text-muted-foreground">
          Sistema semántico de iconos para mayor consistencia y mantenibilidad
        </p>
      </div>

      {/* Problemas Actuales */}
      <Card className="border-destructive/20">
        <CardHeader>
          <CardTitle className="flex items-center gap-2 text-destructive">
            <AlertTriangle className="h-5 w-5" />
            Problemas Identificados
          </CardTitle>
        </CardHeader>
        <CardContent className="space-y-3">
          <div className="flex items-center gap-2">
            <Badge variant="destructive">Colores Directos</Badge>
            <code className="text-sm bg-gray-100 dark:bg-gray-800 px-2 py-1 rounded">
              text-info, text-green-500
            </code>
          </div>
          <div className="flex items-center gap-2">
            <Badge variant="destructive">Tamaños Inconsistentes</Badge>
            <code className="text-sm bg-gray-100 dark:bg-gray-800 px-2 py-1 rounded">
              h-4 w-4, h-5 w-5, h-12 w-12
            </code>
          </div>
          <div className="flex items-center gap-2">
            <Badge variant="destructive">Repetición</Badge>
            <span className="text-sm">Código duplicado para iconos similares</span>
          </div>
        </CardContent>
      </Card>

      {/* Soluciones */}
      <Card className="border-success/20">
        <CardHeader>
          <CardTitle className="flex items-center gap-2 text-success">
            <CheckCircle className="h-5 w-5" />
            Soluciones Implementadas
          </CardTitle>
        </CardHeader>
        <CardContent className="space-y-3">
          <div className="flex items-center gap-2">
            <Badge variant="outline" className="bg-success/10 text-success border-success/20">
              Colores Semánticos
            </Badge>
            <code className="text-sm bg-gray-100 dark:bg-gray-800 px-2 py-1 rounded">
              text-primary, text-success, text-muted-foreground
            </code>
          </div>
          <div className="flex items-center gap-2">
            <Badge variant="outline" className="bg-success/10 text-success border-success/20">
              Sistema de Tamaños
            </Badge>
            <code className="text-sm bg-gray-100 dark:bg-gray-800 px-2 py-1 rounded">
              xs, sm, md, lg, xl, 2xl
            </code>
          </div>
          <div className="flex items-center gap-2">
            <Badge variant="outline" className="bg-success/10 text-success border-success/20">
              Componentes Reutilizables
            </Badge>
            <code className="text-sm bg-gray-100 dark:bg-gray-800 px-2 py-1 rounded">
              SemanticIcon, IconWithText
            </code>
          </div>
        </CardContent>
      </Card>

      {/* Ejemplos de Migración */}
      <Card>
        <CardHeader>
          <CardTitle>Ejemplos de Migración</CardTitle>
          <CardDescription>
            Comparación antes y después de aplicar las mejoras
          </CardDescription>
        </CardHeader>
        <CardContent className="space-y-6">
          
          {/* Ejemplo 1: Iconos de Estado */}
          <div>
            <h3 className="font-semibold mb-3">1. Iconos de Estado</h3>
            <div className="grid md:grid-cols-2 gap-4">
              
              <div className="p-4 bg-gray-50 dark:bg-gray-900 rounded-lg">
                <h4 className="text-sm font-medium mb-3 text-muted-foreground">❌ Antes</h4>
                <div className="space-y-2">
                  <div className="flex items-center gap-2">
                    <CheckCircle className="h-5 w-5 text-green-500" />
                    <span>Operación exitosa</span>
                  </div>
                  <div className="flex items-center gap-2">
                    <AlertTriangle className="h-5 w-5 text-yellow-500" />
                    <span>Advertencia</span>
                  </div>
                  <div className="flex items-center gap-2">
                    <Info className="h-5 w-5 text-info" />
                    <span>Información</span>
                  </div>
                </div>
              </div>

              <div className="p-4 bg-gray-50 dark:bg-gray-900 rounded-lg">
                <h4 className="text-sm font-medium mb-3 text-muted-foreground">✅ Después</h4>
                <div className="space-y-2">
                  <div className="flex items-center gap-2">
                    <SemanticIcon icon={CheckCircle} size="md" context="success" />
                    <span>Operación exitosa</span>
                  </div>
                  <div className="flex items-center gap-2">
                    <SemanticIcon icon={AlertTriangle} size="md" context="warning" />
                    <span>Advertencia</span>
                  </div>
                  <div className="flex items-center gap-2">
                    <SemanticIcon icon={Info} size="md" context="info" />
                    <span>Información</span>
                  </div>
                </div>
              </div>
            </div>
          </div>

          {/* Ejemplo 2: Navegación */}
          <div>
            <h3 className="font-semibold mb-3">2. Navegación con Iconos</h3>
            <div className="grid md:grid-cols-2 gap-4">
              
              <div className="p-4 bg-gray-50 dark:bg-gray-900 rounded-lg">
                <h4 className="text-sm font-medium mb-3 text-muted-foreground">❌ Antes</h4>
                <div className="space-y-2">
                  <div className="flex items-center gap-2">
                    <KeyIcon className="h-4 w-4 text-info" />
                    <span>API Keys</span>
                    <ChevronRightIcon className="h-4 w-4 text-gray-400" />
                  </div>
                  <div className="flex items-center gap-2">
                    <BookOpenIcon className="h-4 w-4 text-green-500" />
                    <span>Documentación</span>
                    <ChevronRightIcon className="h-4 w-4 text-gray-400" />
                  </div>
                </div>
              </div>

              <div className="p-4 bg-gray-50 dark:bg-gray-900 rounded-lg">
                <h4 className="text-sm font-medium mb-3 text-muted-foreground">✅ Después</h4>
                <div className="space-y-2">
                  <IconWithText icon={KeyIcon} size="sm" context="primary">
                    <span>API Keys</span>
                    <SemanticIcon icon={ChevronRightIcon} size="sm" context="muted" />
                  </IconWithText>
                  <IconWithText icon={BookOpenIcon} size="sm" context="primary">
                    <span>Documentación</span>
                    <SemanticIcon icon={ChevronRightIcon} size="sm" context="muted" />
                  </IconWithText>
                </div>
              </div>
            </div>
          </div>
        </CardContent>
      </Card>

      {/* Código de Implementación */}
      <Card>
        <CardHeader>
          <CardTitle>Código de Migración</CardTitle>
        </CardHeader>
        <CardContent className="space-y-4">
          <div>
            <h4 className="font-semibold mb-2">Uso Básico</h4>
            <pre className="bg-gray-100 dark:bg-gray-900 p-3 rounded text-sm overflow-x-auto">
              <code>{`// Antes
<CheckCircle className="h-5 w-5 text-green-500" />

// Después
<SemanticIcon icon={CheckCircle} size="md" context="success" />`}</code>
            </pre>
          </div>

          <div>
            <h4 className="font-semibold mb-2">Iconos con Texto</h4>
            <pre className="bg-gray-100 dark:bg-gray-900 p-3 rounded text-sm overflow-x-auto">
              <code>{`// Antes
<div className="flex items-center gap-2">
  <BookOpenIcon className="h-4 w-4 text-info" />
  <span>Documentación</span>
</div>

// Después
<IconWithText icon={BookOpenIcon} size="sm" context="primary">
  Documentación
</IconWithText>`}</code>
            </pre>
          </div>
        </CardContent>
      </Card>

      {/* Beneficios */}
      <Card>
        <CardHeader>
          <CardTitle>Beneficios Obtenidos</CardTitle>
        </CardHeader>
        <CardContent>
          <div className="grid md:grid-cols-2 gap-4">
            <div className="space-y-2">
              <h4 className="font-semibold flex items-center gap-2">
                <SemanticIcon icon={CheckCircle} size="sm" context="success" />
                Consistencia Visual
              </h4>
              <ul className="text-sm text-muted-foreground space-y-1">
                <li>• Tamaños estandarizados</li>
                <li>• Colores coherentes con el tema</li>
                <li>• Adaptación automática al modo oscuro</li>
              </ul>
            </div>

            <div className="space-y-2">
              <h4 className="font-semibold flex items-center gap-2">
                <SemanticIcon icon={CheckCircle} size="sm" context="success" />
                Mantenibilidad
              </h4>
              <ul className="text-sm text-muted-foreground space-y-1">
                <li>• Cambios centralizados</li>
                <li>• Menos repetición de código</li>
                <li>• TypeScript para autocompletado</li>
              </ul>
            </div>
          </div>
        </CardContent>
      </Card>
    </div>
  );
} 
