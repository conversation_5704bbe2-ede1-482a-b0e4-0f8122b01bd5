#!/bin/bash
set -e

# Start health server in background
python /app/health_server.py &
HEALTH_PID=$!

# Wait for health server to start and verify it's running
sleep 5
if ! kill -0 $HEALTH_PID 2>/dev/null; then
    echo "Health server failed to start"
    exit 1
fi

# Test if health server is responding
if curl -f http://localhost:${PORT:-8080}/health >/dev/null 2>&1; then
    echo "Health server is responding"
else
    echo "Health server is not responding, but continuing..."
fi

# Start the appropriate Celery service based on WORKER_TYPE
if [ "$WORKER_TYPE" = "beat" ]; then
    echo "Starting Celery Beat scheduler..."
    exec celery -A src.workers.celery_app beat --loglevel=info
elif [ "$WORKER_TYPE" = "maintenance" ]; then
    echo "Starting Celery Worker for maintenance tasks..."
    exec celery -A src.workers.celery_app worker --loglevel=info --concurrency=1 --max-tasks-per-child=10 --queues=maintenance --hostname=maintenance@%h
else
    echo "Starting default Celery Worker..."
    exec celery -A src.workers.celery_app worker --loglevel=info --concurrency=2 --max-tasks-per-child=10 --optimization=fair
fi 