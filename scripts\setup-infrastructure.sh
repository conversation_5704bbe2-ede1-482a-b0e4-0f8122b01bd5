#!/bin/bash

set -e  # Exit on any error

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# Configuration
PROJECT_ID=${GCP_PROJECT_ID:-$(gcloud config get-value project)}
REGION=${GCP_REGION:-"us-central1"}
ZONE=${GCP_ZONE:-"us-central1-a"}

# Resource names
SQL_INSTANCE_NAME="rayuela-postgres"
DATABASE_NAME="rayuela_production"
REDIS_INSTANCE_NAME="rayuela-redis-production"
VPC_CONNECTOR_NAME="rayuela-vpc-connector"

echo -e "${BLUE}🏗️ Setting up production infrastructure for Rayuela...${NC}"
echo -e "${BLUE}Project: ${PROJECT_ID}${NC}"
echo -e "${BLUE}Region: ${REGION}${NC}"
echo ""

# Function to check if resource exists
check_resource_exists() {
    local resource_type=$1
    local resource_name=$2
    local additional_args=$3
    
    if gcloud $resource_type describe $resource_name $additional_args &>/dev/null; then
        return 0  # exists
    else
        return 1  # doesn't exist
    fi
}

# Function to wait for operation to complete
wait_for_operation() {
    local operation_name=$1
    local operation_type=$2
    
    echo -e "${YELLOW}⏳ Waiting for $operation_type operation to complete...${NC}"
    
    while true; do
        local status=$(gcloud sql operations describe $operation_name --format="value(status)" 2>/dev/null || echo "UNKNOWN")
        
        if [ "$status" = "DONE" ]; then
            echo -e "${GREEN}✅ Operation completed successfully${NC}"
            break
        elif [ "$status" = "UNKNOWN" ]; then
            echo -e "${YELLOW}⏳ Still waiting...${NC}"
        else
            echo -e "${YELLOW}⏳ Status: $status${NC}"
        fi
        
        sleep 10
    done
}

# 1. Enable required APIs
echo -e "${BLUE}📡 Enabling required APIs...${NC}"
gcloud services enable sqladmin.googleapis.com
gcloud services enable redis.googleapis.com
gcloud services enable vpcaccess.googleapis.com
gcloud services enable secretmanager.googleapis.com
gcloud services enable run.googleapis.com
gcloud services enable cloudbuild.googleapis.com
echo -e "${GREEN}✅ APIs enabled${NC}"
echo ""

# 2. Create VPC Connector (if not exists)
echo -e "${BLUE}🌐 Setting up VPC Connector...${NC}"
if check_resource_exists "compute networks vpc-access connectors" $VPC_CONNECTOR_NAME "--region=$REGION"; then
    echo -e "${GREEN}✅ VPC Connector already exists${NC}"
else
    echo -e "${YELLOW}🔧 Creating VPC Connector...${NC}"
    gcloud compute networks vpc-access connectors create $VPC_CONNECTOR_NAME \
        --region=$REGION \
        --subnet-project=$PROJECT_ID \
        --subnet=default \
        --min-instances=2 \
        --max-instances=3 \
        --machine-type=e2-micro
    echo -e "${GREEN}✅ VPC Connector created${NC}"
fi
echo ""

# 3. Create Cloud SQL Instance
echo -e "${BLUE}🗄️ Setting up Cloud SQL PostgreSQL instance...${NC}"
if check_resource_exists "sql instances" $SQL_INSTANCE_NAME; then
    echo -e "${GREEN}✅ Cloud SQL instance already exists${NC}"
    
    # Get the private IP
    POSTGRES_PRIVATE_IP=$(gcloud sql instances describe $SQL_INSTANCE_NAME --format="value(ipAddresses[0].ipAddress)")
    echo -e "${BLUE}📍 Private IP: $POSTGRES_PRIVATE_IP${NC}"
else
    echo -e "${YELLOW}🔧 Creating Cloud SQL instance (this may take 5-10 minutes)...${NC}"
    
    # Create the instance
    gcloud sql instances create $SQL_INSTANCE_NAME \
        --database-version=POSTGRES_15 \
        --tier=db-f1-micro \
        --region=$REGION \
        --network=projects/$PROJECT_ID/global/networks/default \
        --no-assign-ip
    
    echo -e "${GREEN}✅ Cloud SQL instance created${NC}"
    
    # Get the private IP
    POSTGRES_PRIVATE_IP=$(gcloud sql instances describe $SQL_INSTANCE_NAME --format="value(ipAddresses[0].ipAddress)")
    echo -e "${BLUE}📍 Private IP: $POSTGRES_PRIVATE_IP${NC}"
fi
echo ""

# 4. Create Database
echo -e "${BLUE}🗃️ Setting up database...${NC}"
if gcloud sql databases describe $DATABASE_NAME --instance=$SQL_INSTANCE_NAME &>/dev/null; then
    echo -e "${GREEN}✅ Database already exists${NC}"
else
    echo -e "${YELLOW}🔧 Creating database...${NC}"
    gcloud sql databases create $DATABASE_NAME --instance=$SQL_INSTANCE_NAME
    echo -e "${GREEN}✅ Database created${NC}"
fi
echo ""

# 5. Create database user and set password
echo -e "${BLUE}👤 Setting up database user...${NC}"
DB_PASSWORD=$(openssl rand -base64 32)

# Set postgres user password
gcloud sql users set-password postgres \
    --instance=$SQL_INSTANCE_NAME \
    --password="$DB_PASSWORD"

echo -e "${GREEN}✅ Database user configured${NC}"
echo ""

# 6. Create Redis Instance
echo -e "${BLUE}🔴 Setting up Redis instance...${NC}"
if check_resource_exists "redis instances" $REDIS_INSTANCE_NAME "--region=$REGION"; then
    echo -e "${GREEN}✅ Redis instance already exists${NC}"
    
    # Get Redis details
    REDIS_HOST=$(gcloud redis instances describe $REDIS_INSTANCE_NAME --region=$REGION --format="value(host)")
    REDIS_PORT=$(gcloud redis instances describe $REDIS_INSTANCE_NAME --region=$REGION --format="value(port)")
    echo -e "${BLUE}📍 Redis Host: $REDIS_HOST:$REDIS_PORT${NC}"
else
    echo -e "${YELLOW}🔧 Creating Redis instance...${NC}"
    gcloud redis instances create $REDIS_INSTANCE_NAME \
        --size=1 \
        --region=$REGION \
        --network=projects/$PROJECT_ID/global/networks/default \
        --redis-version=redis_7_0
    
    echo -e "${GREEN}✅ Redis instance created${NC}"
    
    # Get Redis details
    REDIS_HOST=$(gcloud redis instances describe $REDIS_INSTANCE_NAME --region=$REGION --format="value(host)")
    REDIS_PORT=$(gcloud redis instances describe $REDIS_INSTANCE_NAME --region=$REGION --format="value(port)")
    echo -e "${BLUE}📍 Redis Host: $REDIS_HOST:$REDIS_PORT${NC}"
fi
echo ""

# 7. Create Secrets in Secret Manager
echo -e "${BLUE}🔐 Setting up secrets...${NC}"

# Generate a secret key for Django
SECRET_KEY=$(python3 -c "import secrets; print(secrets.token_urlsafe(50))")

# Create secrets
secrets=(
    "DB_PASSWORD:$DB_PASSWORD"
    "POSTGRES_PASSWORD:$DB_PASSWORD"
    "SECRET_KEY:$SECRET_KEY"
    "REDIS_PASSWORD:no-password-required"
    "REDIS_URL:redis://$REDIS_HOST:$REDIS_PORT/0"
)

for secret_pair in "${secrets[@]}"; do
    secret_name=$(echo $secret_pair | cut -d: -f1)
    secret_value=$(echo $secret_pair | cut -d: -f2-)
    
    if gcloud secrets describe $secret_name &>/dev/null; then
        echo -e "${YELLOW}🔄 Updating secret: $secret_name${NC}"
        echo -n "$secret_value" | gcloud secrets versions add $secret_name --data-file=-
    else
        echo -e "${YELLOW}🔧 Creating secret: $secret_name${NC}"
        echo -n "$secret_value" | gcloud secrets create $secret_name --data-file=-
    fi
done

echo -e "${GREEN}✅ Secrets configured${NC}"
echo ""

# 8. Update Cloud Build configuration with correct IPs
echo -e "${BLUE}📝 Updating deployment configuration...${NC}"

# Update the cloudbuild file with the correct IPs
if [ -f "cloudbuild-deploy-production.yaml" ]; then
    # Update PostgreSQL server IP (replace any existing IP with correct one)
    sed -i "s/POSTGRES_SERVER=[0-9.]\+/POSTGRES_SERVER=$POSTGRES_PRIVATE_IP/g" cloudbuild-deploy-production.yaml
    
    # Update Redis host IP (replace any existing IP with correct one)
    sed -i "s/REDIS_HOST=[0-9.]\+/REDIS_HOST=$REDIS_HOST/g" cloudbuild-deploy-production.yaml
    
    echo -e "${GREEN}✅ Configuration updated with current IP addresses${NC}"
    echo -e "${BLUE}  • PostgreSQL: $POSTGRES_PRIVATE_IP${NC}"
    echo -e "${BLUE}  • Redis: $REDIS_HOST${NC}"
else
    echo -e "${YELLOW}⚠️ cloudbuild-deploy-production.yaml not found in current directory${NC}"
fi
echo ""

# 9. Summary
echo -e "${GREEN}🎉 Infrastructure setup completed successfully!${NC}"
echo ""
echo -e "${BLUE}📋 Summary:${NC}"
echo -e "${BLUE}  • Cloud SQL Instance: $SQL_INSTANCE_NAME${NC}"
echo -e "${BLUE}  • Database: $DATABASE_NAME${NC}"
echo -e "${BLUE}  • PostgreSQL IP: $POSTGRES_PRIVATE_IP${NC}"
echo -e "${BLUE}  • Redis Instance: $REDIS_INSTANCE_NAME${NC}"
echo -e "${BLUE}  • Redis Host: $REDIS_HOST:$REDIS_PORT${NC}"
echo -e "${BLUE}  • VPC Connector: $VPC_CONNECTOR_NAME${NC}"
echo -e "${BLUE}  • Secrets: Created in Secret Manager${NC}"
echo ""
echo -e "${GREEN}✅ Ready for deployment! Run: scripts/deploy-production.sh --direct${NC}" 