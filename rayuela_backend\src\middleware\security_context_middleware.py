"""
Middleware de seguridad adicional para logging y validación de contexto tenant.
"""

from fastapi import Request
from starlette.middleware.base import BaseHTTPMiddleware
from starlette.responses import Response
from typing import Callable, Optional
import time

from src.core.tenant_context import get_current_tenant_id
from src.utils.base_logger import log_info, log_warning, log_error


class SecurityContextMiddleware(BaseHTTPMiddleware):
    """
    Middleware de seguridad que proporciona logging adicional y validaciones
    para operaciones que podrían afectar el aislamiento entre tenants.
    
    Este middleware NO reemplaza al TenantMiddleware, sino que lo complementa
    con logging de seguridad y validaciones adicionales.
    """

    def __init__(self, app):
        super().__init__(app)
        
        # Rutas que requieren monitoreo especial de seguridad
        self.monitored_paths = [
            "/api/v1/auth/",
            "/api/v1/system-users/",
            "/api/v1/accounts/",
            "/api/v1/admin/"
        ]
        
        # Rutas que NUNCA deben operar sin contexto de tenant
        self.tenant_required_paths = [
            "/api/v1/products/",
            "/api/v1/recommendations/",
            "/api/v1/interactions/",
            "/api/v1/users/",
            "/api/v1/analytics/"
        ]

    async def dispatch(self, request: Request, call_next: Callable) -> Response:
        """
        Procesa la solicitud agregando logging de seguridad y validaciones.
        """
        start_time = time.time()
        path = request.url.path
        method = request.method
        
        # Obtener contexto de tenant al inicio
        initial_tenant_context = get_current_tenant_id()
        
        try:
            # SEGURIDAD: Logging para rutas monitoreadas
            if any(path.startswith(monitored_path) for monitored_path in self.monitored_paths):
                log_info(
                    f"SECURITY_MONITOR: {method} {path} - "
                    f"initial_tenant_context: {initial_tenant_context}"
                )
            
            # SEGURIDAD: Validar que rutas tenant-scoped tengan contexto
            if any(path.startswith(tenant_path) for tenant_path in self.tenant_required_paths):
                await self._validate_tenant_context_required(path, initial_tenant_context)
            
            # Ejecutar la solicitud
            response = await call_next(request)
            
            # Obtener contexto final después de la operación
            final_tenant_context = get_current_tenant_id()
            
            # SEGURIDAD: Detectar cambios de contexto inesperados
            if initial_tenant_context != final_tenant_context:
                await self._log_tenant_context_change(
                    path, method, initial_tenant_context, final_tenant_context
                )
            
            # SEGURIDAD: Logging de finalización para rutas críticas
            if any(path.startswith(monitored_path) for monitored_path in self.monitored_paths):
                processing_time = time.time() - start_time
                log_info(
                    f"SECURITY_COMPLETE: {method} {path} - "
                    f"final_tenant_context: {final_tenant_context}, "
                    f"processing_time: {processing_time:.3f}s, "
                    f"status: {response.status_code}"
                )
            
            return response
            
        except Exception as e:
            # SEGURIDAD: Log de errores con contexto de seguridad
            log_error(
                f"SECURITY_ERROR: {method} {path} - "
                f"tenant_context: {initial_tenant_context}, "
                f"error: {str(e)}"
            )
            raise
    
    async def _validate_tenant_context_required(
        self, path: str, tenant_context: Optional[int]
    ) -> None:
        """
        Valida que las rutas que requieren contexto de tenant lo tengan.
        """
        if tenant_context is None:
            log_warning(
                f"SECURITY_WARNING: Path {path} requires tenant context but none found. "
                f"This may indicate a security vulnerability."
            )
            # En desarrollo podríamos lanzar una excepción, 
            # en producción solo loggeamos para no romper funcionalidad
    
    async def _log_tenant_context_change(
        self,
        path: str,
        method: str,
        initial_context: Optional[int],
        final_context: Optional[int]
    ) -> None:
        """
        Log cambios en el contexto de tenant durante una operación.
        """
        log_info(
            f"SECURITY_CONTEXT_CHANGE: {method} {path} - "
            f"context changed from {initial_context} to {final_context}"
        )
        
        # Casos especiales que esperamos
        expected_changes = [
            # Login: de None a account_id específico
            (None, final_context) if path.startswith("/api/v1/auth/token"),
            # Register: de None a nuevo account_id
            (None, final_context) if path.startswith("/api/v1/auth/register"),
            # Logout: posible limpieza de contexto
            (initial_context, None) if path.startswith("/api/v1/auth/logout")
        ]
        
        is_expected = any(
            (initial_context, final_context) == change 
            for change in expected_changes if change
        )
        
        if not is_expected:
            log_warning(
                f"SECURITY_UNEXPECTED_CONTEXT_CHANGE: {method} {path} - "
                f"Unexpected tenant context change: {initial_context} -> {final_context}"
            )


class TenantContextValidator:
    """
    Utilidad para validar contexto de tenant en operaciones críticas.
    """
    
    @staticmethod
    def validate_tenant_operation(operation_name: str, required_tenant_id: Optional[int] = None):
        """
        Valida que una operación tenga el contexto de tenant apropiado.
        
        Args:
            operation_name: Nombre descriptivo de la operación
            required_tenant_id: ID específico de tenant requerido (opcional)
        """
        current_tenant = get_current_tenant_id()
        
        if current_tenant is None:
            log_warning(
                f"TENANT_VALIDATION_WARNING: Operation '{operation_name}' "
                f"executing without tenant context"
            )
            return False
        
        if required_tenant_id and current_tenant != required_tenant_id:
            log_error(
                f"TENANT_VALIDATION_ERROR: Operation '{operation_name}' "
                f"requires tenant {required_tenant_id} but current context is {current_tenant}"
            )
            return False
        
        log_info(f"TENANT_VALIDATION_OK: Operation '{operation_name}' with tenant {current_tenant}")
        return True
    
    @staticmethod
    def log_global_operation(operation_name: str, justification: str):
        """
        Log operaciones globales que intencionalmente no usan contexto de tenant.
        
        Args:
            operation_name: Nombre de la operación
            justification: Justificación de por qué es global
        """
        current_tenant = get_current_tenant_id()
        log_info(
            f"GLOBAL_OPERATION: '{operation_name}' - "
            f"current_context: {current_tenant}, "
            f"justification: {justification}"
        ) 