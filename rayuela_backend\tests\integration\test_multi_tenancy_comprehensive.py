"""
Tests comprehensivos de integración para validación de multi-tenancy.
Cubre TODAS las operaciones (CRUD, listados, búsquedas) en TODOS los modelos tenant-scoped.
"""

import pytest
import json
from typing import Dict, Any, List, Optional
from fastapi.testclient import TestClient
from sqlalchemy.ext.asyncio import AsyncSession
from sqlalchemy.future import select
from sqlalchemy import text, func
import uuid

from src.db.session import DatabaseConnectionManager, get_db
from src.db.models import (
    Account,
    Product,
    EndUser,
    Interaction,
    ModelMetadata,
    AuditLog,
    TrainingJob,
    BatchIngestionJob,
    SystemUser,
    Search,
    Recommendation,
    Notification,
    AccountUsageMetrics,
    Subscription,
)
from src.core.config import settings
from src.utils.base_logger import log_info, log_error
from src.core.deps import get_current_account
from src.db.repositories.base import BaseRepository
from src.middleware.tenant import get_current_tenant_id


class TestMultiTenancyComprehensive:
    """Tests comprehensivos para validación completa de multi-tenancy."""

    @pytest.fixture
    async def db_session(self) -> AsyncSession:
        """Fixture para sesión de base de datos."""
        connection_manager = await DatabaseConnectionManager.get_instance()
        session = await connection_manager.get_session()
        try:
            yield session
        finally:
            await session.close()

    @pytest.fixture
    async def multiple_test_accounts(self, db_session: AsyncSession) -> List[Account]:
        """Crear múltiples cuentas de prueba para validar aislamiento."""
        accounts = []
        for i in range(3):  # 3 cuentas para testing más robusto
            account = Account(
                name=f"Multi-Tenant Test Account {i}",
                email=f"mt_test_account_{i}@example.com",
                status="active",
                api_key_hash=f"hash_mt_{i}_{uuid.uuid4().hex[:8]}",
                api_key_prefix=f"mt_prefix_{i}",
                api_key_last_chars=f"mt_last_{i}",
            )
            db_session.add(account)
            await db_session.commit()
            await db_session.refresh(account)
            accounts.append(account)
        return accounts

    @pytest.fixture
    async def comprehensive_test_data(
        self, db_session: AsyncSession, multiple_test_accounts: List[Account]
    ) -> Dict[str, Any]:
        """Crear datos de prueba para todos los modelos tenant-scoped."""
        test_data = {"accounts": multiple_test_accounts}

        # Para cada cuenta, crear datos en todos los modelos tenant-scoped
        for i, account in enumerate(multiple_test_accounts):
            account_data = {
                "products": [],
                "end_users": [],
                "interactions": [],
                "system_users": [],
                "searches": [],
                "recommendations": [],
                "model_metadata": [],
                "audit_logs": [],
                "training_jobs": [],
                "batch_jobs": [],
                "notifications": [],
                "usage_metrics": [],
                "subscriptions": [],
            }

            # Crear Products
            for j in range(2):
                product = Product(
                    account_id=account.account_id,
                    name=f"Producto {i}-{j}",
                    description=f"Descripción del producto {i}-{j}",
                    price=10.99 + i + j,
                    category=f"categoria_{i}_{j}",
                    inventory_count=100 + i * 10 + j,
                )
                db_session.add(product)
                await db_session.flush()
                account_data["products"].append(product)

            # Crear EndUsers
            for j in range(2):
                end_user = EndUser(
                    account_id=account.account_id,
                    external_id=f"external_user_{i}_{j}",
                    is_active=True,
                )
                db_session.add(end_user)
                await db_session.flush()
                account_data["end_users"].append(end_user)

            # Crear SystemUsers
            for j in range(1):  # Solo uno por cuenta
                system_user = SystemUser(
                    account_id=account.account_id,
                    email=f"sysuser_{i}_{j}@account{i}.com",
                    full_name=f"System User {i}-{j}",
                    status="active",
                )
                db_session.add(system_user)
                await db_session.flush()
                account_data["system_users"].append(system_user)

            # Crear Interactions
            for product in account_data["products"]:
                for end_user in account_data["end_users"]:
                    interaction = Interaction(
                        account_id=account.account_id,
                        end_user_id=end_user.user_id,
                        product_id=product.id,
                        interaction_type="view",
                        value=1.0 + i * 0.1,
                    )
                    db_session.add(interaction)
                    await db_session.flush()
                    account_data["interactions"].append(interaction)

            # Crear Searches
            for j in range(2):
                search = Search(
                    account_id=account.account_id,
                    search_query=f"búsqueda {i}-{j}",
                    results_count=10 + j,
                    response_time_ms=100 + i * 10 + j,
                )
                db_session.add(search)
                await db_session.flush()
                account_data["searches"].append(search)

            # Crear ModelMetadata
            for j in range(1):
                model_metadata = ModelMetadata(
                    account_id=account.account_id,
                    model_name=f"modelo_{i}_{j}",
                    model_type="hybrid",
                    version=f"v1.{i}.{j}",
                    description=f"Modelo de ML para cuenta {i}",
                )
                db_session.add(model_metadata)
                await db_session.flush()
                account_data["model_metadata"].append(model_metadata)

            # Crear AuditLogs
            for j in range(2):
                audit_log = AuditLog(
                    account_id=account.account_id,
                    action=f"test_action_{i}_{j}",
                    entity_type="product",
                    entity_id=(
                        account_data["products"][0].id
                        if account_data["products"]
                        else 0
                    ),
                    performed_by=f"test_user_{i}_{j}",
                    details=f"Log de auditoría {i}-{j}",
                )
                db_session.add(audit_log)
                await db_session.flush()
                account_data["audit_logs"].append(audit_log)

            # Crear TrainingJobs
            for j in range(1):
                training_job = TrainingJob(
                    account_id=account.account_id,
                    job_name=f"training_job_{i}_{j}",
                    status="completed",
                    model_type="hybrid",
                    parameters={"learning_rate": 0.01 + i * 0.001},
                )
                db_session.add(training_job)
                await db_session.flush()
                account_data["training_jobs"].append(training_job)

            # Crear BatchIngestionJobs
            for j in range(1):
                batch_job = BatchIngestionJob(
                    account_id=account.account_id,
                    job_name=f"batch_job_{i}_{j}",
                    status="completed",
                    file_path=f"/tmp/batch_{i}_{j}.csv",
                    records_processed=100 + i * 10,
                )
                db_session.add(batch_job)
                await db_session.flush()
                account_data["batch_jobs"].append(batch_job)

            # Crear Notifications
            for j in range(2):
                notification = Notification(
                    account_id=account.account_id,
                    title=f"Notificación {i}-{j}",
                    message=f"Mensaje de notificación para cuenta {i}, item {j}",
                    notification_type="info",
                    is_read=False,
                )
                db_session.add(notification)
                await db_session.flush()
                account_data["notifications"].append(notification)

            # Crear Subscriptions
            subscription = Subscription(
                account_id=account.account_id,
                plan="basic",
                is_active=True,
                max_api_calls=1000 + i * 100,
                max_storage_gb=10 + i,
            )
            db_session.add(subscription)
            await db_session.flush()
            account_data["subscriptions"].append(subscription)

            await db_session.commit()
            test_data[f"account_{i}_data"] = account_data

        return test_data

    async def test_crud_products_isolation(
        self, db_session: AsyncSession, comprehensive_test_data: Dict[str, Any]
    ):
        """Test CRUD operations en Products con aislamiento de tenants."""
        accounts = comprehensive_test_data["accounts"]
        account_0_data = comprehensive_test_data["account_0_data"]
        account_1_data = comprehensive_test_data["account_1_data"]

        # Test READ: Verificar que cada cuenta solo ve sus propios productos
        from src.db.repositories.product import ProductRepository

        # Repository para account 0
        repo_0 = ProductRepository(db_session, account_id=accounts[0].account_id)
        products_0 = await repo_0.get_all()
        assert len(products_0) == 2  # Solo los productos de account 0
        assert all(p.account_id == accounts[0].account_id for p in products_0)

        # Repository para account 1
        repo_1 = ProductRepository(db_session, account_id=accounts[1].account_id)
        products_1 = await repo_1.get_all()
        assert len(products_1) == 2  # Solo los productos de account 1
        assert all(p.account_id == accounts[1].account_id for p in products_1)

        # Test CREATE: Crear nuevo producto para account 0
        new_product_data = {
            "name": "Nuevo Producto Test",
            "description": "Producto creado en test",
            "price": 99.99,
            "category": "test",
            "inventory_count": 50,
        }
        new_product = await repo_0.create(new_product_data)
        assert new_product.account_id == accounts[0].account_id

        # Verificar que account 1 no puede ver el nuevo producto de account 0
        all_products_1 = await repo_1.get_all()
        product_ids_1 = [p.id for p in all_products_1]
        assert new_product.id not in product_ids_1

        # Test UPDATE: Actualizar producto de account 0
        update_data = {"name": "Producto Actualizado"}
        updated_product = await repo_0.update(new_product.id, update_data)
        assert updated_product.name == "Producto Actualizado"
        assert updated_product.account_id == accounts[0].account_id

        # Verificar que account 1 no puede actualizar productos de account 0
        with pytest.raises(Exception):  # Debería fallar por no encontrar el producto
            await repo_1.update(new_product.id, {"name": "Intento de hack"})

        # Test DELETE: Eliminar producto de account 0
        delete_result = await repo_0.soft_delete(new_product.id)
        assert delete_result is True

        # Verificar que el producto fue eliminado solo para account 0
        deleted_product = await repo_0.get_by_id(new_product.id)
        assert deleted_product is None or not deleted_product.is_active

    async def test_crud_end_users_isolation(
        self, db_session: AsyncSession, comprehensive_test_data: Dict[str, Any]
    ):
        """Test CRUD operations en EndUsers con aislamiento de tenants."""
        accounts = comprehensive_test_data["accounts"]

        from src.db.repositories.end_user import EndUserRepository

        # Repository para account 0 y 1
        repo_0 = EndUserRepository(db_session, account_id=accounts[0].account_id)
        repo_1 = EndUserRepository(db_session, account_id=accounts[1].account_id)

        # Test READ: Verificar aislamiento
        users_0 = await repo_0.get_all()
        users_1 = await repo_1.get_all()

        assert len(users_0) == 2
        assert len(users_1) == 2
        assert all(u.account_id == accounts[0].account_id for u in users_0)
        assert all(u.account_id == accounts[1].account_id for u in users_1)

        # Verificar que los IDs no se solapan
        user_ids_0 = {u.id for u in users_0}
        user_ids_1 = {u.id for u in users_1}
        assert user_ids_0.isdisjoint(user_ids_1)

        # Test CREATE
        new_user_data = {"external_id": "new_test_user", "is_active": True}
        new_user = await repo_0.create(new_user_data)
        assert new_user.account_id == accounts[0].account_id

        # Test cross-tenant access prevention
        with pytest.raises(Exception):
            await repo_1.get_by_id(new_user.id)

    async def test_crud_interactions_isolation(
        self, db_session: AsyncSession, comprehensive_test_data: Dict[str, Any]
    ):
        """Test CRUD operations en Interactions con aislamiento de tenants."""
        accounts = comprehensive_test_data["accounts"]

        from src.db.repositories.interaction import InteractionRepository

        repo_0 = InteractionRepository(db_session, account_id=accounts[0].account_id)
        repo_1 = InteractionRepository(db_session, account_id=accounts[1].account_id)

        # Test READ
        interactions_0 = await repo_0.get_all()
        interactions_1 = await repo_1.get_all()

        # Cada cuenta debería tener 4 interactions (2 products * 2 users)
        assert len(interactions_0) == 4
        assert len(interactions_1) == 4
        assert all(i.account_id == accounts[0].account_id for i in interactions_0)
        assert all(i.account_id == accounts[1].account_id for i in interactions_1)

        # Test filters by interaction_type
        view_interactions_0 = await repo_0.get_by_filters({"interaction_type": "view"})
        assert len(view_interactions_0) == 4  # Todas son 'view'
        assert all(i.interaction_type == "view" for i in view_interactions_0)

    async def test_crud_model_metadata_isolation(
        self, db_session: AsyncSession, comprehensive_test_data: Dict[str, Any]
    ):
        """Test CRUD operations en ModelMetadata con aislamiento de tenants."""
        accounts = comprehensive_test_data["accounts"]

        # Usar BaseRepository directamente para ModelMetadata
        from src.db.models import ModelMetadata

        repo_0 = BaseRepository(
            db_session, account_id=accounts[0].account_id, model=ModelMetadata
        )
        repo_1 = BaseRepository(
            db_session, account_id=accounts[1].account_id, model=ModelMetadata
        )

        # Test READ
        models_0 = await repo_0.get_all()
        models_1 = await repo_1.get_all()

        assert len(models_0) == 1
        assert len(models_1) == 1
        assert all(m.account_id == accounts[0].account_id for m in models_0)
        assert all(m.account_id == accounts[1].account_id for m in models_1)

        # Test CREATE
        new_model_data = {
            "model_name": "test_model_new",
            "model_type": "collaborative",
            "version": "v2.0.0",
            "description": "Nuevo modelo de test",
        }
        new_model = await repo_0.create(new_model_data)
        assert new_model.account_id == accounts[0].account_id

        # Test cross-tenant isolation
        all_models_1 = await repo_1.get_all()
        model_ids_1 = [m.id for m in all_models_1]
        assert new_model.id not in model_ids_1

    async def test_crud_audit_logs_isolation(
        self, db_session: AsyncSession, comprehensive_test_data: Dict[str, Any]
    ):
        """Test CRUD operations en AuditLogs con aislamiento de tenants."""
        accounts = comprehensive_test_data["accounts"]

        from src.db.models import AuditLog

        repo_0 = BaseRepository(
            db_session, account_id=accounts[0].account_id, model=AuditLog
        )
        repo_1 = BaseRepository(
            db_session, account_id=accounts[1].account_id, model=AuditLog
        )

        # Test READ
        logs_0 = await repo_0.get_all()
        logs_1 = await repo_1.get_all()

        assert len(logs_0) == 2
        assert len(logs_1) == 2
        assert all(l.account_id == accounts[0].account_id for l in logs_0)
        assert all(l.account_id == accounts[1].account_id for l in logs_1)

        # Test search by filters
        action_logs_0 = await repo_0.get_by_filters({"action": "test_action_0_0"})
        assert len(action_logs_0) == 1
        assert action_logs_0[0].action == "test_action_0_0"

    async def test_crud_training_jobs_isolation(
        self, db_session: AsyncSession, comprehensive_test_data: Dict[str, Any]
    ):
        """Test CRUD operations en TrainingJobs con aislamiento de tenants."""
        accounts = comprehensive_test_data["accounts"]

        from src.db.models import TrainingJob

        repo_0 = BaseRepository(
            db_session, account_id=accounts[0].account_id, model=TrainingJob
        )
        repo_1 = BaseRepository(
            db_session, account_id=accounts[1].account_id, model=TrainingJob
        )

        # Test READ
        jobs_0 = await repo_0.get_all()
        jobs_1 = await repo_1.get_all()

        assert len(jobs_0) == 1
        assert len(jobs_1) == 1
        assert all(j.account_id == accounts[0].account_id for j in jobs_0)
        assert all(j.account_id == accounts[1].account_id for j in jobs_1)

        # Test UPDATE
        job_0 = jobs_0[0]
        update_data = {"status": "failed"}
        updated_job = await repo_0.update(job_0.id, update_data)
        assert updated_job.status == "failed"

        # Verificar que repo_1 no puede actualizar job de account_0
        with pytest.raises(Exception):
            await repo_1.update(job_0.id, {"status": "hacked"})

    async def test_crud_notifications_isolation(
        self, db_session: AsyncSession, comprehensive_test_data: Dict[str, Any]
    ):
        """Test CRUD operations en Notifications con aislamiento de tenants."""
        accounts = comprehensive_test_data["accounts"]

        from src.db.models import Notification

        repo_0 = BaseRepository(
            db_session, account_id=accounts[0].account_id, model=Notification
        )
        repo_1 = BaseRepository(
            db_session, account_id=accounts[1].account_id, model=Notification
        )

        # Test READ
        notifications_0 = await repo_0.get_all()
        notifications_1 = await repo_1.get_all()

        assert len(notifications_0) == 2
        assert len(notifications_1) == 2

        # Test filters
        unread_notifications_0 = await repo_0.get_by_filters({"is_read": False})
        assert len(unread_notifications_0) == 2  # Todas son no leídas

        # Test UPDATE - marcar como leída
        notification_0 = notifications_0[0]
        updated_notification = await repo_0.update(notification_0.id, {"is_read": True})
        assert updated_notification.is_read is True

        # Verificar aislamiento
        all_notifications_1 = await repo_1.get_all()
        notification_ids_1 = [n.id for n in all_notifications_1]
        assert notification_0.id not in notification_ids_1

    async def test_crud_subscriptions_isolation(
        self, db_session: AsyncSession, comprehensive_test_data: Dict[str, Any]
    ):
        """Test CRUD operations en Subscriptions con aislamiento de tenants."""
        accounts = comprehensive_test_data["accounts"]

        from src.db.models import Subscription

        repo_0 = BaseRepository(
            db_session, account_id=accounts[0].account_id, model=Subscription
        )
        repo_1 = BaseRepository(
            db_session, account_id=accounts[1].account_id, model=Subscription
        )

        # Test READ
        subscriptions_0 = await repo_0.get_all()
        subscriptions_1 = await repo_1.get_all()

        assert len(subscriptions_0) == 1
        assert len(subscriptions_1) == 1
        assert subscriptions_0[0].account_id == accounts[0].account_id
        assert subscriptions_1[0].account_id == accounts[1].account_id

        # Test UPDATE
        subscription_0 = subscriptions_0[0]
        update_data = {"max_api_calls": 2000}
        updated_subscription = await repo_0.update(subscription_0.id, update_data)
        assert updated_subscription.max_api_calls == 2000

        # Verificar que la subscription de account_1 no cambió
        subscription_1 = subscriptions_1[0]
        assert subscription_1.max_api_calls != 2000

    async def test_bulk_operations_isolation(
        self, db_session: AsyncSession, comprehensive_test_data: Dict[str, Any]
    ):
        """Test operaciones en lote con aislamiento de tenants."""
        accounts = comprehensive_test_data["accounts"]

        from src.db.repositories.product import ProductRepository

        repo_0 = ProductRepository(db_session, account_id=accounts[0].account_id)
        repo_1 = ProductRepository(db_session, account_id=accounts[1].account_id)

        # Test bulk create para account_0
        bulk_products = [
            {
                "name": f"Bulk Product {i}",
                "description": f"Bulk description {i}",
                "price": 10.0 + i,
                "category": "bulk",
                "inventory_count": 100 + i,
            }
            for i in range(3)
        ]

        created_products = await repo_0.bulk_create(bulk_products)
        assert len(created_products) == 3
        assert all(p.account_id == accounts[0].account_id for p in created_products)

        # Verificar que account_1 no puede ver los productos creados en bulk
        all_products_1 = await repo_1.get_all()
        created_product_ids = {p.id for p in created_products}
        existing_product_ids_1 = {p.id for p in all_products_1}
        assert created_product_ids.isdisjoint(existing_product_ids_1)

    async def test_cross_tenant_access_prevention(
        self, db_session: AsyncSession, comprehensive_test_data: Dict[str, Any]
    ):
        """Test prevención de acceso cruzado entre tenants."""
        accounts = comprehensive_test_data["accounts"]
        account_0_data = comprehensive_test_data["account_0_data"]
        account_1_data = comprehensive_test_data["account_1_data"]

        from src.db.repositories.product import ProductRepository
        from src.db.repositories.end_user import EndUserRepository

        # Intentar acceder a productos de account_1 usando repository de account_0
        repo_0 = ProductRepository(db_session, account_id=accounts[0].account_id)

        # Obtener ID de producto de account_1
        product_1_id = account_1_data["products"][0].id

        # Intentar obtener producto de account_1 con repository de account_0
        accessed_product = await repo_0.get_by_id(product_1_id)
        assert accessed_product is None  # No debería poder acceder

        # Mismo test para end_users
        user_repo_0 = EndUserRepository(db_session, account_id=accounts[0].account_id)
        user_1_id = account_1_data["end_users"][0].id

        accessed_user = await user_repo_0.get_by_id(user_1_id)
        assert accessed_user is None  # No debería poder acceder

    async def test_repository_without_account_id_error(self, db_session: AsyncSession):
        """Test que los repositories tenant-scoped fallan sin account_id."""
        from src.db.repositories.product import ProductRepository

        # Intentar crear repository sin account_id para modelo tenant-scoped
        with pytest.raises(ValueError, match="Account ID is required"):
            repo = ProductRepository(db_session, account_id=None)
            await repo.get_all()  # Esto debería fallar

    async def test_pagination_respects_tenant_boundaries(
        self, db_session: AsyncSession, comprehensive_test_data: Dict[str, Any]
    ):
        """Test que la paginación respeta los límites de tenant."""
        accounts = comprehensive_test_data["accounts"]

        from src.db.repositories.product import ProductRepository

        repo_0 = ProductRepository(db_session, account_id=accounts[0].account_id)
        repo_1 = ProductRepository(db_session, account_id=accounts[1].account_id)

        # Test paginación para account_0
        page_1 = await repo_0.get_all(skip=0, limit=1)
        page_2 = await repo_0.get_all(skip=1, limit=1)

        assert len(page_1) == 1
        assert len(page_2) == 1
        assert page_1[0].id != page_2[0].id
        assert page_1[0].account_id == accounts[0].account_id
        assert page_2[0].account_id == accounts[0].account_id

        # Verificar que la paginación no cruza límites de tenant
        all_products_1 = await repo_1.get_all()
        product_ids_1 = {p.id for p in all_products_1}

        assert page_1[0].id not in product_ids_1
        assert page_2[0].id not in product_ids_1
