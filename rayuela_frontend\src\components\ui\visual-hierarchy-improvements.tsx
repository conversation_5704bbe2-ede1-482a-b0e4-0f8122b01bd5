import * as React from "react"
import { <PERSON>, CardContent, CardDescription, CardHeader, CardTitle } from "./card"
import { cn } from "@/lib/utils"

// Componente para agrupar secciones con fondo sutil
interface SectionGroupProps extends React.HTMLAttributes<HTMLDivElement> {
  children: React.ReactNode
  variant?: 'default' | 'subtle' | 'elevated'
}

export function SectionGroup({ 
  children, 
  variant = 'default', 
  className, 
  ...props 
}: SectionGroupProps) {
  const variants = {
    default: "",
    subtle: "bg-card/30 dark:bg-card/20 border border-border/50 rounded-lg p-6",
    elevated: "bg-card border border-border shadow-sm rounded-lg p-6"
  }

  return (
    <div 
      className={cn(variants[variant], className)} 
      {...props}
    >
      {children}
    </div>
  )
}

// Divider visual sutil para separar secciones
interface SectionDividerProps extends React.HTMLAttributes<HTMLDivElement> {
  variant?: 'line' | 'space' | 'gradient'
  spacing?: 'sm' | 'md' | 'lg'
}

export function SectionDivider({ 
  variant = 'line', 
  spacing = 'md',
  className, 
  ...props 
}: SectionDividerProps) {
  const spacingClasses = {
    sm: 'my-4',
    md: 'my-6', 
    lg: 'my-8'
  }

  const variants = {
    line: "border-t border-border/50",
    space: "h-px",
    gradient: "h-px bg-gradient-to-r from-transparent via-border/50 to-transparent"
  }

  return (
    <div 
      className={cn(
        spacingClasses[spacing],
        variants[variant],
        className
      )} 
      {...props}
    />
  )
}

// Wrapper para páginas densas con mejor jerarquía visual
interface DensePageLayoutProps {
  children: React.ReactNode
  title: string
  description?: string
  actions?: React.ReactNode
}

export function DensePageLayout({ 
  children, 
  title, 
  description, 
  actions 
}: DensePageLayoutProps) {
  return (
    <div className="container mx-auto py-8 space-y-8">
      {/* Header mejorado con agrupación visual */}
      <SectionGroup variant="subtle">
        <div className="flex items-start justify-between">
          <div className="space-y-2">
            <h1 className="text-3xl font-bold tracking-tight">{title}</h1>
            {description && (
              <p className="text-muted-foreground text-lg">{description}</p>
            )}
          </div>
          {actions && (
            <div className="flex items-center gap-2">
              {actions}
            </div>
          )}
        </div>
      </SectionGroup>

      <SectionDivider variant="gradient" />

      {/* Contenido principal */}
      <div className="space-y-6">
        {children}
      </div>
    </div>
  )
}

// Card mejorada para contenido denso
interface DenseCardProps extends React.HTMLAttributes<HTMLDivElement> {
  title: string
  description?: string
  icon?: React.ReactNode
  children: React.ReactNode
  headerActions?: React.ReactNode
}

export function DenseCard({ 
  title, 
  description, 
  icon, 
  children, 
  headerActions,
  className,
  ...props 
}: DenseCardProps) {
  return (
    <Card 
      className={cn(
        "shadow-sm border-border/50 overflow-hidden",
        className
      )} 
      {...props}
    >
      <CardHeader className="border-b border-border/20 bg-muted/10">
        <div className="flex items-start justify-between">
          <div className="space-y-2">
            <CardTitle className="flex items-center gap-2 text-xl">
              {icon && <span className="text-primary">{icon}</span>}
              {title}
            </CardTitle>
            {description && (
              <CardDescription className="text-base">
                {description}
              </CardDescription>
            )}
          </div>
          {headerActions && (
            <div className="flex items-center gap-2">
              {headerActions}
            </div>
          )}
        </div>
      </CardHeader>
      <CardContent className="p-0">
        {children}
      </CardContent>
    </Card>
  )
}

// Tabla mejorada para contenido denso
interface DenseTableRowProps extends React.HTMLAttributes<HTMLTableRowElement> {
  children: React.ReactNode
  index: number
}

export function DenseTableRow({ 
  children, 
  index, 
  className, 
  ...props 
}: DenseTableRowProps) {
  return (
    <tr 
      className={cn(
        "border-b border-border/20 hover:bg-muted/30 transition-colors",
        index % 2 === 0 ? 'bg-background' : 'bg-muted/5',
        className
      )} 
      {...props}
    >
      {children}
    </tr>
  )
}

export { SectionGroup, SectionDivider, DensePageLayout, DenseCard, DenseTableRow } 
