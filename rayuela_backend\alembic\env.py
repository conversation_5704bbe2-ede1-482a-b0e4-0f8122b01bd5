import asyncio
from logging.config import fileConfig
import os
import sys

from sqlalchemy import pool, engine_from_config
from sqlalchemy.ext.asyncio import async_engine_from_config

from alembic import context


# --- A<PERSON><PERSON> src al PYTHONPATH ---
# Esto permite a Alembic encontrar tus modelos y configuración
current_dir = os.path.dirname(os.path.abspath(__file__))
project_root = os.path.dirname(current_dir)  # Sube un nivel desde alembic/
sys.path.insert(0, project_root)
# ---------------------------------

# --- Importar tu Base y configuración ---
from src.db.base import Base  # Asegúrate que Base esté definida aquí
from src.core.config import settings  # Importa tu configuración

# Importa tus modelos para que Base.metadata los conozca
# Es crucial importar TODOS los archivos de modelos aquí
from src.db import models  # O importa específicamente cada modelo

# ---------------------------------------

# this is the Alembic Config object, which provides
# access to the values within the .ini file in use.
config = context.config

# --- Configurar la URL de la base de datos desde settings ---
# Sobrescribe la URL del .ini con la de tu objeto settings
config.set_main_option("sqlalchemy.url", settings.database_url)
# ----------------------------------------------------------


# Interpret the config file for Python logging.
# This line sets up loggers basically.
if config.config_file_name is not None:
    fileConfig(config.config_file_name)

# add your model's MetaData object here
# for 'autogenerate' support
# from myapp import mymodel
# target_metadata = mymodel.Base.metadata
target_metadata = Base.metadata

# other values from the config, defined by the needs of env.py,
# can be acquired:
# my_important_option = config.get_main_option("my_important_option")
# ... etc.


def run_migrations_offline() -> None:
    """Run migrations in 'offline' mode.

    This configures the context with just a URL
    and not an Engine, though an Engine is acceptable
    here as well.  By skipping the Engine creation
    we don't even need a DBAPI to be available.

    Calls to context.execute() here emit the given string to the
    script output.

    """
    url = config.get_main_option("sqlalchemy.url")
    context.configure(
        url=url,
        target_metadata=target_metadata,
        literal_binds=True,
        dialect_opts={"paramstyle": "named"},
    )

    with context.begin_transaction():
        context.run_migrations()


def do_run_migrations(connection):
    context.configure(connection=connection, target_metadata=target_metadata)

    with context.begin_transaction():
        context.run_migrations()


def run_migrations_online_sync() -> None:
    """Run migrations in 'online' mode using synchronous connection.
    
    This is more reliable for Cloud Build environments.
    """
    # Get configuration section 
    configuration = config.get_section(config.config_ini_section)
    
    # Override the URL to use psycopg2 instead of asyncpg for sync operations
    sync_url = settings.database_url.replace('postgresql+asyncpg://', 'postgresql://')
    configuration['sqlalchemy.url'] = sync_url
    
    # Use synchronous engine for better reliability in Cloud Build
    connectable = engine_from_config(
        configuration,
        prefix="sqlalchemy.",
        poolclass=pool.NullPool,  # Usar NullPool para migraciones
        connect_args={
            'options': '-c statement_timeout=0 -c lock_timeout=0 -c application_name=alembic_migration'
        }
    )

    with connectable.connect() as connection:
        context.configure(
            connection=connection, 
            target_metadata=target_metadata
        )

        with context.begin_transaction():
            context.run_migrations()

async def run_migrations_online() -> None:
    """Run migrations in 'online' mode using async approach.

    This is a fallback when synchronous approach fails.
    """
    # Get configuration section 
    configuration = config.get_section(config.config_ini_section)
    
    # Use simplified configuration for better reliability in Cloud Build
    connectable = async_engine_from_config(
        configuration,
        prefix="sqlalchemy.",
        poolclass=pool.NullPool,  # Usar NullPool para migraciones
        connect_args={
            'server_settings': {
                'jit': 'off',
                'statement_timeout': '0',
                'lock_timeout': '0',
                'application_name': 'alembic_migration'
            },
            'command_timeout': 300
        }
    )

    try:
        # Create connection with extended timeout for Cloud Build environment
        connection = await asyncio.wait_for(connectable.connect(), timeout=600)
        
        try:
            await connection.run_sync(do_run_migrations)
        finally:
            await connection.close()
    except asyncio.TimeoutError:
        print("❌ Migration connection timed out after 600 seconds")
        raise
    finally:
        await connectable.dispose()


if context.is_offline_mode():
    run_migrations_offline()
else:
    # Use synchronous approach for better reliability in Cloud Build
    try:
        run_migrations_online_sync()
    except Exception as e:
        print(f"⚠️ Synchronous migration failed: {e}")
        print("🔄 Falling back to async approach...")
        asyncio.run(run_migrations_online())
