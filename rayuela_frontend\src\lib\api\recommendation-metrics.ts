// src/lib/api/recommendation-metrics.ts

import { 
  getRay<PERSON><PERSON>,
  GetRecommendationPerformanceApiV1AnalyticsAnalyticsRecommendationPerformanceGet200,
  GetRecommendationPerformanceApiV1AnalyticsAnalyticsRecommendationPerformanceGetParams,
  GetConfidenceMetricsApiV1RecommendationsConfidenceMetricsGet200
} from '@/lib/generated/rayuelaAPI';
import { ApiError } from '@/lib/api';

// Create API client instance
const api = getRayuela();

// Type definitions for backward compatibility
export type RecommendationPerformanceMetrics = GetRecommendationPerformanceApiV1AnalyticsAnalyticsRecommendationPerformanceGet200;
export type ConfidenceMetrics = GetConfidenceMetricsApiV1RecommendationsConfidenceMetricsGet200;

// Legacy interfaces maintained for backward compatibility with existing components
export interface ModelMetrics {
  model_id: number;
  model_type: string;
  version: string;
  created_at: string;
  metrics: {
    precision: number;
    recall: number;
    ndcg: number;
    map: number;
    catalog_coverage: number;
    diversity: number;
    novelty: number;
    serendipity: number;
  };
}

export interface ResourceMetrics {
  memory_usage_mb: {
    avg_value: number;
    min_value: number;
    max_value: number;
  };
  cpu_percent: {
    avg_value: number;
    min_value: number;
    max_value: number;
  };
  evaluation_time_seconds: {
    avg_value: number;
    min_value: number;
    max_value: number;
  };
  collab_training_time: {
    avg_value: number;
    min_value: number;
    max_value: number;
  };
  content_training_time: {
    avg_value: number;
    min_value: number;
    max_value: number;
  };
}

export interface MetricsSummary {
  precision: number;
  recall: number;
  ndcg: number;
  map: number;
  catalog_coverage: number;
  diversity: number;
  novelty: number;
  serendipity: number;
}

export interface ConfidenceDistribution {
  low: number;
  medium: number;
  high: number;
  avg: number;
}

export interface ConfidenceTrendPoint {
  date: string;
  avg_confidence: number;
  count: number;
}

/**
 * Obtiene las métricas de rendimiento de recomendaciones
 * @param token Token JWT del usuario (deprecated - authentication handled by client)
 * @param apiKey API Key de la cuenta (deprecated - authentication handled by client)
 * @param modelId ID del modelo específico (opcional)
 * @param metricType Tipo de métrica a filtrar (opcional)
 * @returns Métricas de rendimiento de recomendaciones
 */
export async function getRecommendationPerformance(
  token: string, // Kept for backward compatibility but not used
  apiKey: string, // Kept for backward compatibility but not used
  modelId?: number,
  metricType?: string
): Promise<RecommendationPerformanceMetrics> {
  try {
    const params: GetRecommendationPerformanceApiV1AnalyticsAnalyticsRecommendationPerformanceGetParams = {};
    
    if (modelId) {
      params.model_id = modelId;
    }
    
    if (metricType) {
      params.metric_type = metricType;
    }

    return await api.getRecommendationPerformanceApiV1AnalyticsAnalyticsRecommendationPerformanceGet(params);
  } catch (error: any) {
    throw new ApiError(
      error.message || 'Error al obtener métricas de rendimiento de recomendaciones',
      error.status || 500,
      error.body
    );
  }
}

/**
 * Obtiene las métricas de confianza para las recomendaciones
 * @param token Token JWT del usuario (deprecated - authentication handled by client)
 * @param apiKey API Key de la cuenta (deprecated - authentication handled by client)
 * @returns Métricas de confianza para las recomendaciones
 */
export async function getConfidenceMetrics(
  token: string, // Kept for backward compatibility but not used
  apiKey: string // Kept for backward compatibility but not used
): Promise<ConfidenceMetrics> {
  try {
    return await api.getConfidenceMetricsApiV1RecommendationsConfidenceMetricsGet();
  } catch (error: any) {
    throw new ApiError(
      error.message || 'Error al obtener métricas de confianza',
      error.status || 500,
      error.body
    );
  }
}
