"""
Utilidades de logging de seguridad para operaciones tenant-scoped.
"""

from typing import Optional, Callable, Any
from functools import wraps
import time

from src.core.tenant_context import get_current_tenant_id
from src.utils.base_logger import log_info, log_warning, log_error


class TenantSecurityLogger:
    """
    Logger especializado para operaciones de seguridad relacionadas con multi-tenancy.
    """
    
    @staticmethod
    def log_global_operation(operation_name: str, justification: str, **kwargs):
        """
        Log operaciones globales que intencionalmente no usan contexto de tenant.
        
        Args:
            operation_name: Nombre de la operación
            justification: Justificación de por qué es global
            **kwargs: Datos adicionales para logging (serán sanitizados)
        """
        current_tenant = get_current_tenant_id()
        
        # Sanitizar datos sensibles
        sanitized_kwargs = {}
        for key, value in kwargs.items():
            if key.lower() in ['email', 'password', 'token']:
                if isinstance(value, str) and len(value) > 3:
                    sanitized_kwargs[key] = f"{value[:3]}***"
                else:
                    sanitized_kwargs[key] = "***"
            else:
                sanitized_kwargs[key] = value
        
        log_info(
            f"GLOBAL_OPERATION: '{operation_name}' - "
            f"current_context: {current_tenant}, "
            f"justification: {justification}, "
            f"data: {sanitized_kwargs}"
        )
    
    @staticmethod
    def log_tenant_operation(operation_name: str, expected_tenant: Optional[int] = None, **kwargs):
        """
        Log operaciones que requieren contexto de tenant.
        
        Args:
            operation_name: Nombre de la operación
            expected_tenant: Tenant esperado (opcional)
            **kwargs: Datos adicionales para logging
        """
        current_tenant = get_current_tenant_id()
        
        # Sanitizar datos sensibles
        sanitized_kwargs = {}
        for key, value in kwargs.items():
            if key.lower() in ['email', 'password', 'token']:
                if isinstance(value, str) and len(value) > 3:
                    sanitized_kwargs[key] = f"{value[:3]}***"
                else:
                    sanitized_kwargs[key] = "***"
            else:
                sanitized_kwargs[key] = value
        
        status = "OK" if current_tenant else "MISSING_CONTEXT"
        if expected_tenant and current_tenant != expected_tenant:
            status = "CONTEXT_MISMATCH"
        
        log_level = log_info if status == "OK" else log_warning
        
        log_level(
            f"TENANT_OPERATION: '{operation_name}' - "
            f"status: {status}, "
            f"current_context: {current_tenant}, "
            f"expected_context: {expected_tenant}, "
            f"data: {sanitized_kwargs}"
        )
    
    @staticmethod
    def log_context_change(
        operation_name: str,
        initial_context: Optional[int],
        final_context: Optional[int],
        expected: bool = True
    ):
        """
        Log cambios en el contexto de tenant.
        
        Args:
            operation_name: Nombre de la operación que causó el cambio
            initial_context: Contexto inicial
            final_context: Contexto final
            expected: Si el cambio era esperado
        """
        log_level = log_info if expected else log_warning
        status = "EXPECTED" if expected else "UNEXPECTED"
        
        log_level(
            f"CONTEXT_CHANGE: '{operation_name}' - "
            f"status: {status}, "
            f"change: {initial_context} -> {final_context}"
        )


def log_tenant_security(operation_name: str, global_operation: bool = False, justification: str = ""):
    """
    Decorator para logging automático de operaciones con contexto de seguridad.
    
    Args:
        operation_name: Nombre descriptivo de la operación
        global_operation: Si es una operación global intencionada
        justification: Justificación para operaciones globales
    """
    def decorator(func: Callable) -> Callable:
        @wraps(func)
        async def async_wrapper(*args, **kwargs):
            initial_context = get_current_tenant_id()
            start_time = time.time()
            
            try:
                if global_operation:
                    TenantSecurityLogger.log_global_operation(
                        operation_name, justification, function=func.__name__
                    )
                else:
                    TenantSecurityLogger.log_tenant_operation(
                        operation_name, function=func.__name__
                    )
                
                result = await func(*args, **kwargs)
                
                final_context = get_current_tenant_id()
                processing_time = time.time() - start_time
                
                # Log cambios de contexto
                if initial_context != final_context:
                    expected = global_operation and final_context is not None
                    TenantSecurityLogger.log_context_change(
                        operation_name, initial_context, final_context, expected
                    )
                
                log_info(
                    f"OPERATION_COMPLETE: '{operation_name}' - "
                    f"processing_time: {processing_time:.3f}s, "
                    f"success: True"
                )
                
                return result
                
            except Exception as e:
                log_error(
                    f"OPERATION_ERROR: '{operation_name}' - "
                    f"error: {str(e)}, "
                    f"tenant_context: {initial_context}"
                )
                raise
        
        @wraps(func)
        def sync_wrapper(*args, **kwargs):
            initial_context = get_current_tenant_id()
            start_time = time.time()
            
            try:
                if global_operation:
                    TenantSecurityLogger.log_global_operation(
                        operation_name, justification, function=func.__name__
                    )
                else:
                    TenantSecurityLogger.log_tenant_operation(
                        operation_name, function=func.__name__
                    )
                
                result = func(*args, **kwargs)
                
                final_context = get_current_tenant_id()
                processing_time = time.time() - start_time
                
                # Log cambios de contexto
                if initial_context != final_context:
                    expected = global_operation and final_context is not None
                    TenantSecurityLogger.log_context_change(
                        operation_name, initial_context, final_context, expected
                    )
                
                log_info(
                    f"OPERATION_COMPLETE: '{operation_name}' - "
                    f"processing_time: {processing_time:.3f}s, "
                    f"success: True"
                )
                
                return result
                
            except Exception as e:
                log_error(
                    f"OPERATION_ERROR: '{operation_name}' - "
                    f"error: {str(e)}, "
                    f"tenant_context: {initial_context}"
                )
                raise
        
        # Retornar wrapper apropiado según si la función es async
        import asyncio
        if asyncio.iscoroutinefunction(func):
            return async_wrapper
        else:
            return sync_wrapper
    
    return decorator


# Utilidades adicionales para validación
def validate_tenant_context(operation_name: str, required_tenant: Optional[int] = None) -> bool:
    """
    Valida que el contexto de tenant sea apropiado para una operación.
    
    Args:
        operation_name: Nombre de la operación
        required_tenant: Tenant específico requerido
    
    Returns:
        True si el contexto es válido, False si no
    """
    current_tenant = get_current_tenant_id()
    
    if current_tenant is None:
        log_warning(
            f"VALIDATION_FAILED: '{operation_name}' - "
            f"no tenant context found"
        )
        return False
    
    if required_tenant and current_tenant != required_tenant:
        log_error(
            f"VALIDATION_FAILED: '{operation_name}' - "
            f"required tenant {required_tenant}, got {current_tenant}"
        )
        return False
    
    return True 