#!/bin/bash

# Configure Cloud SQL for Private IP Only
# Disables public IP access and ensures only private network connectivity

set -e

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# Configuration
PROJECT_ID="${GCP_PROJECT_ID:-$(gcloud config get-value project)}"
REGION="${GCP_REGION:-us-central1}"
VPC_NETWORK="default"

echo -e "${BLUE}🔒 Configurando Cloud SQL para Acceso Privado Únicamente${NC}"
echo "========================================================"
echo "📋 Proyecto: $PROJECT_ID"
echo "🌍 Región: $REGION"
echo "🌐 Red VPC: $VPC_NETWORK"
echo ""

# Function to enable required APIs
enable_apis() {
    echo -e "${BLUE}🔌 Habilitando APIs requeridas...${NC}"
    
    REQUIRED_APIS=(
        "sqladmin.googleapis.com"
        "servicenetworking.googleapis.com"
        "compute.googleapis.com"
    )
    
    for api in "${REQUIRED_APIS[@]}"; do
        echo "📡 Habilitando $api..."
        gcloud services enable $api --project=$PROJECT_ID
        
        if [ $? -eq 0 ]; then
            echo -e "${GREEN}✅ $api habilitada${NC}"
        else
            echo -e "${RED}❌ Error habilitando $api${NC}"
            exit 1
        fi
    done
    
    echo ""
}

# Function to setup private service access
setup_private_service_access() {
    echo -e "${BLUE}🔗 Configurando Private Service Access...${NC}"
    
    # Allocate IP range for private service access
    echo "📡 Asignando rango IP para servicios privados..."
    
    # Check if range already exists
    if gcloud compute addresses describe google-managed-services-$VPC_NETWORK \
        --global --project=$PROJECT_ID &>/dev/null; then
        echo -e "${GREEN}✅ Rango IP ya existe${NC}"
    else
        echo "🔧 Creando rango IP para servicios de Google..."
        gcloud compute addresses create google-managed-services-$VPC_NETWORK \
            --global \
            --purpose=VPC_PEERING \
            --prefix-length=16 \
            --network=$VPC_NETWORK \
            --project=$PROJECT_ID
        
        if [ $? -eq 0 ]; then
            echo -e "${GREEN}✅ Rango IP creado${NC}"
        else
            echo -e "${RED}❌ Error creando rango IP${NC}"
            exit 1
        fi
    fi
    
    # Create private connection
    echo "🔗 Configurando conexión privada..."
    
    if gcloud services vpc-peerings list \
        --network=$VPC_NETWORK --project=$PROJECT_ID \
        --format="value(name)" | grep -q "servicenetworking"; then
        echo -e "${GREEN}✅ Conexión privada ya existe${NC}"
    else
        gcloud services vpc-peerings connect \
            --service=servicenetworking.googleapis.com \
            --ranges=google-managed-services-$VPC_NETWORK \
            --network=$VPC_NETWORK \
            --project=$PROJECT_ID
        
        if [ $? -eq 0 ]; then
            echo -e "${GREEN}✅ Conexión privada configurada${NC}"
        else
            echo -e "${RED}❌ Error configurando conexión privada${NC}"
            exit 1
        fi
    fi
    
    echo ""
}

# Function to list and configure SQL instances
configure_sql_instances() {
    echo -e "${BLUE}🗄️ Configurando Instancias de Cloud SQL...${NC}"
    
    # Get all SQL instances
    INSTANCES=$(gcloud sql instances list --project=$PROJECT_ID --format="value(name)" 2>/dev/null)
    
    if [ -z "$INSTANCES" ]; then
        echo -e "${YELLOW}⚠️ No se encontraron instancias de Cloud SQL${NC}"
        echo "💡 Cuando crees una instancia de Cloud SQL, usa este comando:"
        echo ""
        echo "gcloud sql instances create rayuela-db \\"
        echo "  --database-version=POSTGRES_14 \\"
        echo "  --tier=db-f1-micro \\"
        echo "  --region=$REGION \\"
        echo "  --network=$VPC_NETWORK \\"
        echo "  --no-assign-ip \\"
        echo "  --project=$PROJECT_ID"
        echo ""
        return 0
    fi
    
    echo "📋 Instancias encontradas: $INSTANCES"
    echo ""
    
    for instance in $INSTANCES; do
        echo -e "${BLUE}🔧 Configurando instancia: $instance${NC}"
        
        # Get current configuration
        CURRENT_CONFIG=$(gcloud sql instances describe $instance --project=$PROJECT_ID \
            --format="value(settings.ipConfiguration.ipv4Enabled,settings.ipConfiguration.privateNetwork)")
        
        IPV4_ENABLED=$(echo "$CURRENT_CONFIG" | cut -d$'\t' -f1)
        PRIVATE_NETWORK=$(echo "$CURRENT_CONFIG" | cut -d$'\t' -f2)
        
        echo "📊 Configuración actual:"
        echo "   IP Pública habilitada: $IPV4_ENABLED"
        echo "   Red privada: $PRIVATE_NETWORK"
        echo ""
        
        # Check if needs configuration
        NEEDS_UPDATE=false
        
        if [ "$IPV4_ENABLED" = "True" ]; then
            echo -e "${YELLOW}⚠️ IP pública está habilitada - se deshabilitará${NC}"
            NEEDS_UPDATE=true
        fi
        
        if [ -z "$PRIVATE_NETWORK" ] || [ "$PRIVATE_NETWORK" = "None" ]; then
            echo -e "${YELLOW}⚠️ Red privada no configurada - se configurará${NC}"
            NEEDS_UPDATE=true
        fi
        
        if [ "$NEEDS_UPDATE" = true ]; then
            echo "🔧 Actualizando configuración..."
            
            # Build the patch command
            PATCH_CMD="gcloud sql instances patch $instance --project=$PROJECT_ID"
            
            # Add private network if not configured
            if [ -z "$PRIVATE_NETWORK" ] || [ "$PRIVATE_NETWORK" = "None" ]; then
                PATCH_CMD="$PATCH_CMD --network=projects/$PROJECT_ID/global/networks/$VPC_NETWORK"
            fi
            
            # Disable public IP
            PATCH_CMD="$PATCH_CMD --no-assign-ip"
            
            echo "📝 Ejecutando: $PATCH_CMD"
            echo ""
            
            # Ask for confirmation
            echo -e "${YELLOW}⚠️ ADVERTENCIA: Esta operación deshabilitará el acceso público a la base de datos.${NC}"
            echo -e "${YELLOW}   Solo será accesible desde la red VPC privada.${NC}"
            echo ""
            read -p "¿Continuar? (y/N): " -n 1 -r
            echo ""
            
            if [[ $REPLY =~ ^[Yy]$ ]]; then
                eval $PATCH_CMD
                
                if [ $? -eq 0 ]; then
                    echo -e "${GREEN}✅ Instancia $instance configurada para acceso privado${NC}"
                else
                    echo -e "${RED}❌ Error configurando instancia $instance${NC}"
                    echo "💡 La instancia podría estar en uso. Inténtalo más tarde."
                fi
            else
                echo -e "${YELLOW}⏭️ Configuración de $instance omitida${NC}"
            fi
        else
            echo -e "${GREEN}✅ Instancia $instance ya está configurada correctamente${NC}"
        fi
        
        echo ""
    done
}

# Function to verify configuration
verify_configuration() {
    echo -e "${BLUE}🔍 Verificando Configuración Final...${NC}"
    
    INSTANCES=$(gcloud sql instances list --project=$PROJECT_ID --format="value(name)" 2>/dev/null)
    
    if [ -z "$INSTANCES" ]; then
        echo -e "${YELLOW}⚠️ No hay instancias para verificar${NC}"
        return 0
    fi
    
    echo "📋 Estado de seguridad de las instancias:"
    echo ""
    
    for instance in $INSTANCES; do
        echo "🔍 Instancia: $instance"
        
        # Get private IP
        PRIVATE_IP=$(gcloud sql instances describe $instance --project=$PROJECT_ID \
            --format="value(ipAddresses[].ipAddress)" \
            --filter="ipAddresses.type=PRIVATE" 2>/dev/null)
        
        # Get public IP
        PUBLIC_IP=$(gcloud sql instances describe $instance --project=$PROJECT_ID \
            --format="value(ipAddresses[].ipAddress)" \
            --filter="ipAddresses.type=PRIMARY" 2>/dev/null)
        
        # Get network
        NETWORK=$(gcloud sql instances describe $instance --project=$PROJECT_ID \
            --format="value(settings.ipConfiguration.privateNetwork)" 2>/dev/null)
        
        if [ -n "$PRIVATE_IP" ]; then
            echo -e "${GREEN}✅ IP Privada: $PRIVATE_IP${NC}"
        else
            echo -e "${RED}❌ No tiene IP privada${NC}"
        fi
        
        if [ -n "$PUBLIC_IP" ]; then
            echo -e "${RED}❌ CRÍTICO: Aún tiene IP pública: $PUBLIC_IP${NC}"
        else
            echo -e "${GREEN}✅ Sin IP pública${NC}"
        fi
        
        if [ -n "$NETWORK" ]; then
            echo -e "${GREEN}✅ Red privada: $NETWORK${NC}"
        else
            echo -e "${RED}❌ No tiene red privada configurada${NC}"
        fi
        
        echo ""
    done
}

# Function to provide next steps
provide_next_steps() {
    echo -e "${BLUE}🚀 Próximos Pasos${NC}"
    echo "================="
    echo ""
    
    echo -e "${YELLOW}📋 Para completar la configuración de seguridad:${NC}"
    echo ""
    
    echo "1. 🔗 Asegurar VPC Connector (si no está hecho):"
    echo "   ./scripts/setup-vpc-connector.sh"
    echo ""
    
    echo "2. 🔒 Configurar Redis para red privada:"
    echo "   ./scripts/configure-redis-private.sh"
    echo ""
    
    echo "3. 🔐 Actualizar secretos de conexión a la base de datos:"
    echo "   # Usar la IP privada en lugar de la pública"
    echo "   gcloud secrets versions add POSTGRES_SERVER --data-file=- <<< \"\$PRIVATE_IP\""
    echo ""
    
    echo "4. 🚀 Desplegar servicios:"
    echo "   gcloud builds submit --config cloudbuild-deploy-production.yaml"
    echo ""
    
    echo "5. 🔍 Verificar configuración completa:"
    echo "   ./scripts/verify-vpc-security.sh"
    echo ""
    
    echo -e "${GREEN}💡 Importante: Actualiza las variables de entorno/secretos con la IP privada${NC}"
}

# Main execution
echo "🚀 Iniciando configuración de Cloud SQL privado..."
echo ""

enable_apis
setup_private_service_access
configure_sql_instances
verify_configuration

echo ""
echo "======================================================"
echo -e "${GREEN}🎉 Configuración de Cloud SQL completada!${NC}"
echo "======================================================"

provide_next_steps

echo ""
echo -e "${YELLOW}📝 Nota: Recuerda actualizar los secretos de conexión para usar la IP privada${NC}" 