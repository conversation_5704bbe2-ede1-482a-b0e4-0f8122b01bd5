#!/bin/bash

# 🔍 Script para verificar el estado de la infraestructura

set -e

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# Configuration
PROJECT_ID=${GCP_PROJECT_ID:-$(gcloud config get-value project)}
REGION=${GCP_REGION:-"us-central1"}

# Resource names
SQL_INSTANCE_NAME="rayuela-production-db"
DATABASE_NAME="rayuela_production"
REDIS_INSTANCE_NAME="rayuela-redis-production"
VPC_CONNECTOR_NAME="rayuela-vpc-connector"

echo -e "${BLUE}🔍 Checking infrastructure status for Rayuela...${NC}"
echo -e "${BLUE}Project: ${PROJECT_ID}${NC}"
echo -e "${BLUE}Region: ${REGION}${NC}"
echo ""

# Function to check if resource exists
check_resource() {
    local resource_type=$1
    local resource_name=$2
    local additional_args=$3
    local display_name=$4
    
    echo -n "  • $display_name: "
    
    if gcloud $resource_type describe $resource_name $additional_args &>/dev/null; then
        echo -e "${GREEN}✅ EXISTS${NC}"
        return 0
    else
        echo -e "${RED}❌ MISSING${NC}"
        return 1
    fi
}

# Function to check secret
check_secret() {
    local secret_name=$1
    
    echo -n "  • Secret $secret_name: "
    
    if gcloud secrets describe $secret_name &>/dev/null; then
        echo -e "${GREEN}✅ EXISTS${NC}"
        return 0
    else
        echo -e "${RED}❌ MISSING${NC}"
        return 1
    fi
}

echo -e "${BLUE}📋 Infrastructure Status:${NC}"
echo ""

# Check VPC Connector
check_resource "compute networks vpc-access connectors" $VPC_CONNECTOR_NAME "--region=$REGION" "VPC Connector"

# Check Cloud SQL Instance
check_resource "sql instances" $SQL_INSTANCE_NAME "" "Cloud SQL Instance"

# Check Database (only if SQL instance exists)
if gcloud sql instances describe $SQL_INSTANCE_NAME &>/dev/null; then
    echo -n "  • Database $DATABASE_NAME: "
    if gcloud sql databases describe $DATABASE_NAME --instance=$SQL_INSTANCE_NAME &>/dev/null; then
        echo -e "${GREEN}✅ EXISTS${NC}"
    else
        echo -e "${RED}❌ MISSING${NC}"
    fi
fi

# Check Redis Instance
check_resource "redis instances" $REDIS_INSTANCE_NAME "--region=$REGION" "Redis Instance"

# Check Secrets
echo ""
echo -e "${BLUE}🔐 Secrets Status:${NC}"
check_secret "DB_PASSWORD"
check_secret "POSTGRES_PASSWORD"
check_secret "SECRET_KEY"
check_secret "REDIS_PASSWORD"
check_secret "REDIS_URL"

echo ""

# Get current IPs if resources exist
if gcloud sql instances describe $SQL_INSTANCE_NAME &>/dev/null; then
    POSTGRES_IP=$(gcloud sql instances describe $SQL_INSTANCE_NAME --format="value(ipAddresses[0].ipAddress)" 2>/dev/null)
    echo -e "${BLUE}📍 PostgreSQL Private IP: ${POSTGRES_IP}${NC}"
fi

if gcloud redis instances describe $REDIS_INSTANCE_NAME --region=$REGION &>/dev/null; then
    REDIS_HOST=$(gcloud redis instances describe $REDIS_INSTANCE_NAME --region=$REGION --format="value(host)" 2>/dev/null)
    REDIS_PORT=$(gcloud redis instances describe $REDIS_INSTANCE_NAME --region=$REGION --format="value(port)" 2>/dev/null)
    echo -e "${BLUE}📍 Redis Host: ${REDIS_HOST}:${REDIS_PORT}${NC}"
fi

echo ""
echo -e "${GREEN}✅ Infrastructure check completed${NC}" 