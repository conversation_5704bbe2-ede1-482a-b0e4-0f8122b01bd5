#!/usr/bin/env node

/**
 * Script para sincronizar especificación OpenAPI
 * Uso: npm run sync-api [--watch] [--force]
 */

const { spawn } = require('child_process');
const fs = require('fs');
const path = require('path');

const args = process.argv.slice(2);
const isWatch = args.includes('--watch');
const isForce = args.includes('--force');

console.log('🔄 Rayuela API Sync');
console.log('=====================================');

// Verificar si el backend está corriendo
async function checkBackend() {
  try {
    const response = await fetch('http://localhost:8001/health');
    return response.ok;
  } catch {
    return false;
  }
}

function runCommand(command, args = []) {
  return new Promise((resolve, reject) => {
    const child = spawn(command, args, { 
      stdio: 'inherit', 
      shell: true 
    });
    
    child.on('close', (code) => {
      if (code === 0) {
        resolve();
      } else {
        reject(new Error(`Command failed with code ${code}`));
      }
    });
  });
}

async function syncAPI() {
  try {
    const backendRunning = await checkBackend();
    
    if (!backendRunning && !isForce) {
      console.log('⚠️  Backend no está corriendo en localhost:8001');
      console.log('💡 Opciones:');
      console.log('   1. Iniciar backend: cd rayuela_backend && python main.py');
      console.log('   2. Forzar sincronización: npm run sync-api -- --force');
      process.exit(1);
    }

    if (backendRunning) {
      console.log('✅ Backend detectado - usando URL directa');
      await runCommand('npm', ['run', 'generate-api:dev']);
    } else {
      console.log('📁 Usando archivo estático con --force');
      await runCommand('npm', ['run', 'generate-api:force']);
    }

    console.log('🎉 API sincronizada exitosamente');
    
  } catch (error) {
    console.error('❌ Error sincronizando API:', error.message);
    process.exit(1);
  }
}

if (isWatch) {
  console.log('👀 Modo watch activado - reactivando en cambios...');
  // Para watch mode, podríamos implementar file watching
  // Por ahora, ejecutamos una vez
  syncAPI();
} else {
  syncAPI();
} 