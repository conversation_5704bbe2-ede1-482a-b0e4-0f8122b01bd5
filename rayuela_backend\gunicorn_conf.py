"""
Gunicorn configuration file for production deployment.
"""

import os
import multiprocessing

# Server socket
# Cloud Run sets PORT environment variable
# Use API_HOST for host binding (0.0.0.0 to listen on all interfaces)
bind = os.getenv("API_HOST", "0.0.0.0") + ":" + os.getenv("PORT", "8080")
backlog = 2048

# Worker processes
# Optimizado para máxima eficiencia en costos con Cloud SQL (db-f1-micro)
cpu_count = multiprocessing.cpu_count()
# Reducido a 1 worker para minimizar conexiones de BD y costos de instancia
default_workers = int(os.getenv("GUNICORN_WORKERS", "1"))
workers = default_workers
worker_class = "uvicorn.workers.UvicornWorker"
# Optimizado para startup: con 1 worker y pool_size=5 + max_overflow=2 = 7 conexiones físicas
# Permite usar la instancia Cloud SQL más económica (db-f1-micro con 25 conexiones máx)
worker_connections = int(os.getenv("GUNICORN_WORKER_CONNECTIONS", "10"))

# Timeout ajustado para evitar workers bloqueados
# Aumentamos el timeout para permitir tiempo de carga de secretos en producción
timeout = int(os.getenv("GUNICORN_TIMEOUT", "120"))
# Añadimos un tiempo de gracia para finalizar trabajos en curso
graceful_timeout = 60

# Keep-alive para mejor reutilización de conexiones
keepalive = 5

# Process naming
proc_name = "rayuela-api"

# Server mechanics
daemon = False
pidfile = None
umask = 0
user = None
group = None
tmp_upload_dir = None

# Logging
errorlog = "-"
loglevel = os.getenv("LOG_LEVEL", "info")
accesslog = "-"

# Secure access log format for production
# Excludes query parameters and sensitive headers to prevent data exposure
if os.getenv("ENV") == "production":
    # Production format: excludes query parameters and limits header exposure
    access_log_format = '%(h)s %(l)s %(u)s %(t)s "%(m)s %(U)s %(H)s" %(s)s %(b)s %(L)s'
else:
    # Development format: includes more details for debugging
    access_log_format = '%(h)s %(l)s %(u)s %(t)s "%(r)s" %(s)s %(b)s "%(f)s" "%(a)s" %(L)s'

# Límites de recursos para prevenir fugas de memoria
max_requests = 200
max_requests_jitter = 50

# Server hooks
def on_starting(server):
    """Log when server starts."""
    env = os.getenv("ENV", "development")
    server.log.info(f"Starting Rayuela in {env} environment")
    if env == "production":
        server.log.info("Production logging: sensitive data sanitization enabled")


def on_exit(server):
    """Log when server exits."""
    server.log.info("Shutting down Rayuela")


# Security Note:
# In production, the access log format excludes query parameters (%(q)s) and
# limits header information to prevent exposure of sensitive data like API keys,
# authentication tokens, or PII that might be passed in URLs or headers.
#
# Format explanation:
# %(h)s - Remote host
# %(l)s - Remote logname (usually '-')
# %(u)s - Remote user (usually '-')
# %(t)s - Time of request
# %(m)s - Request method (GET, POST, etc.)
# %(U)s - URL path without query string
# %(H)s - Protocol (HTTP/1.1, etc.)
# %(s)s - Status code
# %(b)s - Response size
# %(L)s - Request time in microseconds
