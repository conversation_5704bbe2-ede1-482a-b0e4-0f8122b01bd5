/**
 * New API client using generated code from OpenAPI spec
 * This replaces the manual api.ts file with generated, type-safe functions
 */

// Import generated API functions and types
import {
    getRayuela,
    // Types
    Token,
    RegisterRequest,
    SystemUserResponse,
    AccountResponse,
    UsageStats,
    PlanInfo,
    ApiKeyResponse,
    NewApiKeyResponse,
    ApiKeyListResponse,
    ApiKeyCreate,
    ApiKeyUpdate,
    GetUsageHistoryApiV1UsageHistoryGet200Item
} from './generated/rayuelaAPI';

// Re-export types for backward compatibility
export type ApiKey = ApiKeyResponse;
export type AccountInfo = AccountResponse;
export type UsageHistoryPoint = GetUsageHistoryApiV1UsageHistoryGet200Item;

// Create a simple ApiError class for backward compatibility
export class ApiError extends Error {
    public status: number;
    public body: any;

    constructor(message: string, status: number = 500, body?: any) {
        super(message);
        this.name = 'ApiError';
        this.status = status;
        this.body = body;
    }
}

// Create a simple API client instance
const api = getRayuela();

// --- Authentication Functions ---

export const loginUser = async (credentials: { email: string; password: string }): Promise<Token> => {
    try {
        return await api.loginApiV1AuthTokenPost(credentials);
    } catch (error: any) {
        throw new ApiError(error.message || 'Login failed', error.status || 500, error.body);
    }
};

export const registerUser = async (
    accountName: string,
    email: string,
    password: string
): Promise<Token> => {
    try {
        const registerRequest: RegisterRequest = {
            accountName: accountName,
            email,
            password
        };
        return await api.registerApiV1AuthRegisterPost(registerRequest);
    } catch (error: any) {
        throw new ApiError(error.message || 'Registration failed', error.status || 500, error.body);
    }
};

export const logout = async (): Promise<any> => {
    return api.logoutApiV1AuthLogoutPost();
};

export const requestEmailVerification = async (): Promise<any> => {
    return api.sendVerificationEmailApiV1AuthSendVerificationEmailPost();
};

export const verifyEmail = async (token: string): Promise<any> => {
    return api.verifyEmailApiV1AuthVerifyEmailGet({ token });
};

// --- User Functions ---

export const getCurrentUser = async (): Promise<SystemUserResponse> => {
    try {
        return await api.getCurrentUserInfoApiV1SystemUsersMeGet();
    } catch (error: any) {
        throw new ApiError(error.message || 'Failed to get current user', error.status || 500, error.body);
    }
};

// Alias for compatibility
export const getMe = getCurrentUser;

// --- Account Functions ---

export const getCurrentAccount = async (token?: string, apiKey?: string): Promise<AccountResponse> => {
    try {
        // For compatibility with legacy calls that pass token and apiKey
        // In the new implementation, authentication is handled by the client configuration
        return await api.getAccountInfoApiV1AccountsCurrentGet();
    } catch (error: any) {
        throw new ApiError(error.message || 'Failed to get current account', error.status || 500, error.body);
    }
};

// Alias for compatibility
export const getMyAccount = getCurrentAccount;

export const getAccountUsage = async (): Promise<UsageStats> => {
    try {
        return await api.getApiUsageApiV1AccountsUsageGet();
    } catch (error: any) {
        throw new ApiError(error.message || 'Failed to get account usage', error.status || 500, error.body);
    }
};

export const getPlans = async (): Promise<{ [key: string]: PlanInfo }> => {
    try {
        return await api.getAvailablePlansApiV1PlansGet();
    } catch (error: any) {
        throw new ApiError(error.message || 'Failed to get plans', error.status || 500, error.body);
    }
};

// Alias for compatibility
export const getAvailablePlans = getPlans;

// --- Usage Functions ---

export const getUsageHistory = async (
    startDate?: string,
    endDate?: string
): Promise<any[]> => {
    const params: any = {};
    if (startDate) params.start_date = startDate;
    if (endDate) params.end_date = endDate;

    return api.getUsageHistoryApiV1UsageHistoryGet(params);
};

// --- API Key Functions ---

export const getApiKeys = async (): Promise<ApiKeyListResponse> => {
    try {
        return await api.listApiKeysApiV1ApiKeysGet();
    } catch (error: any) {
        throw new ApiError(error.message || 'Failed to get API keys', error.status || 500, error.body);
    }
};

// Alias for compatibility
export const listApiKeys = getApiKeys;

export const createApiKey = async (apiKeyData: { name: string; permissions?: string[] }): Promise<NewApiKeyResponse> => {
    try {
        const createRequest: ApiKeyCreate = {
            name: apiKeyData.name
        };
        return await api.createApiKeyApiV1ApiKeysPost(createRequest);
    } catch (error: any) {
        throw new ApiError(error.message || 'Failed to create API key', error.status || 500, error.body);
    }
};

export const deleteApiKey = async (keyId: string): Promise<void> => {
    try {
        await api.revokeSpecificApiKeyApiV1ApiKeysApiKeyIdDelete(Number(keyId));
    } catch (error: any) {
        throw new ApiError(error.message || 'Failed to delete API key', error.status || 500, error.body);
    }
};

export const updateApiKey = async (keyId: string, updateData: { name?: string; permissions?: string[] }): Promise<ApiKeyResponse> => {
    try {
        const updateRequest: ApiKeyUpdate = {
            name: updateData.name
        };
        return await api.updateApiKeyApiV1ApiKeysApiKeyIdPut(Number(keyId), updateRequest);
    } catch (error: any) {
        throw new ApiError(error.message || 'Failed to update API key', error.status || 500, error.body);
    }
};

export const revokeApiKey = async (apiKeyId: number): Promise<void> => {
    return api.revokeSpecificApiKeyApiV1ApiKeysApiKeyIdDelete(apiKeyId);
};

export const getApiKey = async (): Promise<ApiKeyResponse> => {
    return api.getCurrentApiKeyApiV1ApiKeysCurrentGet();
};

export const revokeAllApiKeys = async (): Promise<void> => {
    return api.revokeApiKeyApiV1ApiKeysDelete();
};

// --- Billing Functions ---

export const createCheckoutSession = async (priceId: string): Promise<any> => {
    const request = { price_id: priceId };
    return api.createCheckoutSessionApiV1BillingCreateCheckoutSessionPost(request);
};

export const createBillingPortalSession = async (): Promise<any> => {
    return api.createPortalSessionApiV1BillingCreatePortalSessionPost({});
};

// --- Health Functions ---

export const healthCheck = async (): Promise<{ status: string }> => {
    try {
        return await api.healthCheckHealthGet();
    } catch (error: any) {
        throw new ApiError(error.message || 'Health check failed', error.status || 500, error.body);
    }
};

// Export more types for compatibility
export type {
    Token,
    RegisterRequest,
    SystemUserResponse,
    AccountResponse,
    UsageStats,
    PlanInfo,
    ApiKeyResponse,
    NewApiKeyResponse,
    ApiKeyListResponse,
    ApiKeyCreate,
    ApiKeyUpdate
}; 