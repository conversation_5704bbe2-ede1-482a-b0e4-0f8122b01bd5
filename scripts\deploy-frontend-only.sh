#!/bin/bash

# Script para desplegar solo el frontend de Rayuela
# Author: Assistant
# Version: 1.0

set -e

# Colores para output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# Función para logging
log() {
    echo -e "${BLUE}[$(date +'%Y-%m-%d %H:%M:%S')]${NC} $1"
}

error() {
    echo -e "${RED}[ERROR]${NC} $1" >&2
}

success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

# Verificar que estamos en el directorio correcto
if [[ ! -f "cloudbuild-deploy-frontend-only.yaml" ]]; then
    error "No se encontró cloudbuild-deploy-frontend-only.yaml"
    error "Asegúrate de ejecutar este script desde el directorio raíz del proyecto"
    exit 1
fi

# Verificar que tenemos acceso a gcloud
if ! command -v gcloud &> /dev/null; then
    error "gcloud CLI no está instalado o no está en el PATH"
    exit 1
fi

# Verificar autenticación
if ! gcloud auth list --filter=status:ACTIVE --format="value(account)" | grep -q .; then
    error "No hay ninguna cuenta autenticada en gcloud"
    error "Ejecuta: gcloud auth login"
    exit 1
fi

# Obtener PROJECT_ID actual
PROJECT_ID=$(gcloud config get-value project 2>/dev/null)
if [[ -z "$PROJECT_ID" ]]; then
    error "No se pudo obtener PROJECT_ID"
    error "Ejecuta: gcloud config set project TU_PROJECT_ID"
    exit 1
fi

# Información del despliegue
log "🚀 Iniciando despliegue del frontend únicamente"
log "📋 Proyecto: $PROJECT_ID"
log "🌍 Región: us-central1"

# Verificar si hay cambios sin commit
if [[ -n $(git status --porcelain) ]]; then
    warning "⚠️  Hay cambios sin commit en el repositorio"
    log "Continuando automáticamente con el despliegue..."
fi

# Mostrar el commit actual
CURRENT_COMMIT=$(git rev-parse --short HEAD)
log "📝 Commit actual: $CURRENT_COMMIT"
log "📝 Mensaje: $(git log -1 --pretty=format:'%s')"

# Confirmar despliegue
echo
warning "⚠️  Estás a punto de desplegar SOLO EL FRONTEND a producción"
log "Confirmando automáticamente el despliegue..."

# Ejecutar el build
log "🔨 Iniciando Cloud Build para el frontend..."

# Ejecutar gcloud builds submit
gcloud builds submit \
    --config=cloudbuild-deploy-frontend-only.yaml \
    --region=us-central1 \
    --project="$PROJECT_ID" \
    .

# Verificar si el build fue exitoso
if [[ $? -eq 0 ]]; then
    success "✅ ¡Despliegue del frontend completado exitosamente!"
    
    # Obtener la URL del frontend
    FRONTEND_URL=$(gcloud run services describe rayuela-frontend --region=us-central1 --format="value(status.url)" 2>/dev/null)
    if [[ -n "$FRONTEND_URL" ]]; then
        success "🌐 Frontend disponible en: $FRONTEND_URL"
    fi
    
    log "📊 Puedes monitorear el servicio en:"
    log "   https://console.cloud.google.com/run/detail/us-central1/rayuela-frontend"
    
else
    error "❌ El despliegue del frontend ha fallado"
    log "🔍 Revisa los logs en: https://console.cloud.google.com/cloud-build/builds"
    exit 1
fi 