"""add_global_email_uniqueness_system_users

Revision ID: 20250105_000000
Revises: 20250614_130000
Create Date: 2025-01-05 12:00:00.000000

"""
from alembic import op
import sqlalchemy as sa


# revision identifiers, used by Alembic.
revision = '20250105_000000'
down_revision = '20250614_130000'
branch_labels = None
depends_on = None


def upgrade():
    # Drop the old compound unique index (account_id, email)
    op.drop_index('idx_system_user_email', table_name='system_users')
    
    # Create new global unique index on email only
    op.create_index('idx_system_user_email_global', 'system_users', ['email'], unique=True)


def downgrade():
    # Drop the global unique index
    op.drop_index('idx_system_user_email_global', table_name='system_users')
    
    # Recreate the old compound unique index
    op.create_index('idx_system_user_email', 'system_users', ['account_id', 'email'], unique=True) 