"""
Celery application configuration for background tasks.
"""

from celery import Celery
from celery.schedules import crontab
from src.core.config import settings

# Create Celery app
celery_app = Celery(
    "ml_training_worker",
    broker=settings.REDIS_URL,
    backend=settings.REDIS_URL,
)

# Configure Celery
celery_app.conf.update(
    # Serialization settings
    task_serializer="json",
    accept_content=["json"],
    result_serializer="json",

    # Time settings
    timezone="UTC",
    enable_utc=True,
    task_track_started=True,

    # Default task settings
    task_time_limit=3600 * 2,  # 2 hours default time limit
    task_soft_time_limit=3600 * 1.5,  # 1.5 hours soft time limit

    # Worker settings
    worker_max_tasks_per_child=10,  # Restart worker after 10 tasks to prevent memory leaks
    worker_prefetch_multiplier=1,  # Don't prefetch more than one task

    # Queue settings
    task_default_queue="default",
    task_queues={
        "training": {
            "exchange": "training",
            "routing_key": "training",
        },
        "batch_processing": {
            "exchange": "batch_processing",
            "routing_key": "batch_processing",
        },
        "maintenance": {
            "exchange": "maintenance",
            "routing_key": "maintenance",
        },
        "default": {
            "exchange": "default",
            "routing_key": "default",
        },
    },

    # Task routing
    task_routes={
        # Training tasks
        "train_model": {"queue": "training"},
        "train_model_for_job": {"queue": "training"},

        # Batch processing tasks
        "process_batch_data": {"queue": "batch_processing"},

        # Maintenance tasks
        "cleanup_old_audit_logs": {"queue": "maintenance"},
        "cleanup_old_interactions": {"queue": "maintenance"},
        "cleanup_old_data_secure": {"queue": "maintenance"},
        "monitor_high_volume_tables": {"queue": "maintenance"},
        "manage_partitions_task": {"queue": "maintenance"},
        "reset_monthly_api_calls": {"queue": "maintenance"},
        "update_storage_usage": {"queue": "maintenance"},
        "measure_storage_usage": {"queue": "maintenance"},

        # Storage meter tasks
        "get_storage_usage": {"queue": "default"},
    }
)

# ============================================================================
# SECURITY CRITICAL: Enhanced Task Security Configuration
# ============================================================================

def validate_beat_schedule_security():
    """
    🚨 SECURITY CRITICAL: Validates beat schedule for security compliance
    
    This function ensures all maintenance tasks have proper security controls
    and prevents RLS bypass vulnerabilities.
    """
    import logging
    security_logger = logging.getLogger("security.celery")
    
    # Tasks that MUST NOT operate globally without tenant context
    TENANT_ISOLATION_REQUIRED = [
        "cleanup-old-audit-logs",
        "cleanup-old-interactions", 
        "cleanup-soft-deleted-records",
        "cleanup-old-data-secure"
    ]
    
    violations = []
    
    for task_name, config in celery_app.conf.beat_schedule.items():
        kwargs = config.get("kwargs", {})
        
        # CRITICAL: Check for account_id=None in tenant-specific tasks
        if task_name in TENANT_ISOLATION_REQUIRED:
            if "account_id" in kwargs and kwargs["account_id"] is None:
                violations.append(f"Task {task_name} has account_id=None - SECURITY RISK")
        
        # Ensure security validation is required
        if any(keyword in task_name for keyword in ["cleanup", "maintenance", "delete"]):
            if not kwargs.get("require_security_validation", False):
                security_logger.warning(
                    f"[SECURITY WARNING] Task {task_name} lacks security validation"
                )
    
    if violations:
        security_logger.error(f"[SECURITY VIOLATIONS DETECTED] {violations}")
        raise ValueError(f"Insecure task configurations found: {violations}")
    
    security_logger.info("✅ Beat schedule security validation passed")

# Configure periodic tasks with enhanced security
celery_app.conf.beat_schedule = {
    # ========================================================================
    # SECURE MAINTENANCE TASKS - PER TENANT EXECUTION
    # ========================================================================
    
    # SECURITY FIX: Replace global tasks with secure per-tenant scheduling
    "schedule-secure-audit-logs-cleanup": {
        "task": "src.workers.celery_tasks.schedule_per_tenant_cleanup",
        "schedule": 86400.0,  # Once a day
        "kwargs": {
            "target_task": "cleanup_old_audit_logs_secure",
            "task_kwargs": {"days_to_keep": 90},
            "require_security_validation": True,
            "enforce_tenant_isolation": True
        },
        "options": {"queue": "maintenance"},
    },
    
    "schedule-secure-interactions-cleanup": {
        "task": "src.workers.celery_tasks.schedule_per_tenant_cleanup", 
        "schedule": 86400.0 * 7,  # Once a week
        "kwargs": {
            "target_task": "cleanup_old_interactions_secure",
            "task_kwargs": {"days_to_keep": 180, "batch_size": 10000},
            "require_security_validation": True,
            "enforce_tenant_isolation": True
        },
        "options": {"queue": "maintenance"},
    },

    # SECURITY FIX: Secure archive and cleanup tasks with tenant isolation
    "schedule-secure-archive-audit-logs": {
        "task": "src.workers.celery_tasks.schedule_per_tenant_cleanup",
        "schedule": 86400.0,  # Once a day
        "kwargs": {
            "target_task": "archive_and_cleanup_old_audit_logs_secure",
            "task_kwargs": {"days_to_keep": 30, "batch_size": 10000},
            "require_security_validation": True,
            "enforce_tenant_isolation": True
        },
        "options": {"queue": "maintenance"},
    },
    
    "schedule-secure-archive-interactions": {
        "task": "src.workers.celery_tasks.schedule_per_tenant_cleanup",
        "schedule": 86400.0 * 7,  # Once a week
        "kwargs": {
            "target_task": "archive_and_cleanup_old_interactions_secure",
            "task_kwargs": {"days_to_keep": 60, "batch_size": 10000},
            "require_security_validation": True,
            "enforce_tenant_isolation": True
        },
        "options": {"queue": "maintenance"},
    },

    # SECURITY FIX: Secure soft delete cleanup with tenant isolation
    "schedule-secure-soft-delete-cleanup": {
        "task": "src.workers.celery_tasks.schedule_per_tenant_cleanup",
        "schedule": 86400.0 * 30,  # Once a month
        "kwargs": {
            "target_task": "cleanup_soft_deleted_records_secure",
            "task_kwargs": {"retention_days": 90, "dry_run": False},
            "require_security_validation": True,
            "enforce_tenant_isolation": True
        },
        "options": {"queue": "maintenance"},
    },
    
    # ⚠️ SECURITY CRITICAL: RLS bypass task with mandatory tenant validation
    "schedule-secure-data-cleanup": {
        "task": "src.workers.celery_tasks.schedule_per_tenant_cleanup",
        "schedule": 86400.0 * 14,  # Once every two weeks
        "kwargs": {
            "target_task": "cleanup_old_data_secure_validated",
            "task_kwargs": {"days_to_keep": 90},
            "require_security_validation": True,
            "enforce_tenant_isolation": True,
            "rls_bypass_operation": True  # Flag for critical security logging
        },
        "options": {"queue": "maintenance"},
    },
    "monitor-high-volume-tables": {
        "task": "src.workers.celery_tasks.monitor_high_volume_tables",
        "schedule": 3600.0,  # Once an hour
        "options": {"queue": "maintenance"},
    },
    "manage-partitions": {
        "task": "src.workers.celery_tasks_partition.manage_partitions_task",
        "schedule": crontab(hour=1, minute=0),  # Daily at 1:00 AM (low traffic time)
        "options": {"queue": "maintenance"},
    },
    "reset-monthly-api-calls": {
        "task": "src.tasks.subscription_tasks.reset_monthly_api_calls",
        "schedule": 86400.0,  # Once a day at midnight
        "options": {"queue": "maintenance"},
    },
    "update-storage-usage": {
        "task": "src.tasks.subscription_tasks.update_storage_usage",
        "schedule": 86400.0,  # Once a day
        "options": {"queue": "maintenance"},
    },
    "measure-storage-usage": {
        "task": "src.tasks.storage_meter_tasks.measure_storage_usage",
        "schedule": 86400.0,  # Once a day
        "options": {"queue": "maintenance"},
    },
}

# Importar tareas para asegurar que están registradas correctamente
# Esto evita el error de "Received unregistered task of type"
# No elimines estas importaciones
import src.workers.celery_tasks
import src.workers.celery_tasks_partition

# También importamos las tareas que están en otros módulos y son usadas en el beat_schedule
try:
    import src.tasks.subscription_tasks
    import src.tasks.storage_meter_tasks
except ImportError:
    # Si estos módulos no existen, lo registramos pero no fallamos
    # ya que podrían ser opcionales o estar en desarrollo
    import logging
    logging.warning("No se pudieron importar todos los módulos de tareas opcionales.")

# ============================================================================
# SECURITY CRITICAL: Validate configuration on module load
# ============================================================================

try:
    validate_beat_schedule_security()
    import logging
    security_logger = logging.getLogger("security.celery")
    security_logger.info("🛡️ Celery security validation completed successfully")
except Exception as e:
    import logging
    security_logger = logging.getLogger("security.celery")
    security_logger.critical(f"🚨 SECURITY VALIDATION FAILED: {str(e)}")
    # In production, this should prevent the application from starting
    raise RuntimeError(f"Celery security validation failed: {str(e)}")
