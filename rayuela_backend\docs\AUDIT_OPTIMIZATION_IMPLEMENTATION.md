# Implementación: Optimización de Granularidad del Registro de Auditoría

## Resumen

Esta implementación optimiza los costos de la auditoría mediante una estrategia híbrida que mantiene la trazabilidad completa pero reduce las escrituras costosas a la base de datos Cloud SQL.

## Historia de Usuario Implementada

**Como** administrador del sistema  
**Quiero** optimizar la granularidad del registro de auditoría para reducir costos  
**Para que** solo las acciones críticas se almacenen en la base de datos costosa, manteniendo todos los logs en Cloud Logging para búsquedas y análisis.

## Arquitectura de la Solución

### Estrategia Híbrida

1. **Todos los logs → Cloud Logging** (costo-efectivo para grandes volúmenes)
2. **Solo acciones críticas → Cloud SQL** (para cumplimiento y auditorías formales)

### Beneficios

- **Reducción de costos**: ~70-80% menos escrituras a Cloud SQL
- **Mantiene trazabilidad completa**: Todos los logs siguen disponibles en Cloud Logging  
- **Cumplimiento**: Acciones críticas siguen en BD para auditorías formales
- **Performance**: Menos carga en la base de datos

## Criterios de Auditoría Crítica

Se considera "crítica" una acción que debe almacenarse en Cloud SQL si cumple alguno de estos criterios:

### 1. Endpoints de Administración y Mantenimiento
- Cualquier método en `/api/v1/maintenance`
- Cualquier método en `/api/v1/system-users`

### 2. Operaciones de Autenticación
- Cualquier método en `/api/v1/auth`

### 3. Métodos que Cambian Estado en Recursos Clave
Métodos `POST`, `PUT`, `PATCH`, `DELETE` en:
- `/api/v1/roles`
- `/api/v1/permissions` 
- `/api/v1/accounts`
- `/api/v1/products`
- `/api/v1/recommendations`
- `/api/v1/pipeline`
- `/api/v1/ingestion`
- `/api/v1/billing`
- `/api/v1/api-keys`

## Implementación Técnica

### Archivo Modificado: `src/middleware/audit.py`

#### Nuevas Constantes
```python
CRITICAL_ENDPOINTS = {
    "/api/v1/maintenance",
    "/api/v1/system-users", 
    "/api/v1/roles",
    "/api/v1/permissions",
    "/api/v1/accounts",
    "/api/v1/products",
    "/api/v1/recommendations", 
    "/api/v1/pipeline",
    "/api/v1/ingestion",
    "/api/v1/billing",
    "/api/v1/api-keys",
    "/api/v1/auth",
}

STATE_CHANGING_METHODS = {"POST", "PUT", "PATCH", "DELETE"}
```

#### Nueva Función de Decisión
```python
def _should_audit_to_database(self, method: str, path: str) -> bool:
    """Determina si una solicitud debe ser auditada en la base de datos."""
```

#### Lógica de Optimización
```python
# SIEMPRE enviar logs estructurados a Cloud Logging
log_info(f"{method} {path}", {
    "audit": audit_data,
    "critical_action": self._should_audit_to_database(method, path),
    # ... otros datos
})

# OPTIMIZACIÓN: Solo escribir a BD las acciones críticas
if account_id and self._should_audit_to_database(method, path):
    background_tasks.add_task(write_audit_log_to_db_task, audit_data)
```

## Configuración de Cloud Logging

### Gunicorn Access Logs
El archivo `gunicorn_conf.py` ya está configurado para enviar logs de acceso a Cloud Logging:

```python
accesslog = "-"  # Envía a stdout → Cloud Logging
```

### Formato de Logs de Acceso
- **Producción**: Sanitizado, sin parámetros de consulta sensibles
- **Desarrollo**: Formato completo para debugging

## Casos de Uso

### ✅ SE AUDITA EN BD (Crítico)
- `POST /api/v1/products` - Crear producto
- `DELETE /api/v1/api-keys/123` - Eliminar API key
- `PUT /api/v1/roles/admin` - Modificar rol
- `GET /api/v1/maintenance/status` - Acceso a mantenimiento
- `POST /api/v1/auth/login` - Login de usuario

### ❌ NO SE AUDITA EN BD (Solo Cloud Logging)
- `GET /api/v1/products` - Listar productos
- `GET /api/v1/analytics` - Ver analytics
- `POST /api/v1/interactions` - Registrar interacción
- `GET /health` - Health check

## Tests Implementados

### Archivo: `tests/test_audit_middleware_optimization.py`

#### Test Cases Cubiertos
- Endpoints críticos con métodos que cambian estado
- Endpoints no críticos que no deben auditarse en BD
- Endpoints de autenticación (siempre críticos)
- Endpoints de administración (siempre críticos)

#### Ejemplo de Test
```python
def test_should_audit_to_database_critical_endpoints(self):
    critical_cases = [
        ("POST", "/api/v1/products"),
        ("DELETE", "/api/v1/api-keys/abc123"),
        ("GET", "/api/v1/maintenance/status"),
    ]
    
    for method, path in critical_cases:
        assert self.middleware._should_audit_to_database(method, path)
```

## Métricas de Impacto Esperado

### Reducción de Escrituras en BD
- **Antes**: 100% de solicitudes API → Cloud SQL
- **Después**: ~20-30% de solicitudes API → Cloud SQL
- **Ahorro**: ~70-80% menos escrituras costosas

### Mantenimiento de Trazabilidad
- **Cloud Logging**: 100% de solicitudes (sin cambios)
- **Búsquedas**: Disponibles en ambos sistemas
- **Cumplimiento**: Acciones críticas en BD (sin cambios)

## Monitoreo y Observabilidad

### Logs Estructurados
Todos los logs incluyen el campo `critical_action: boolean` para facilitar el monitoreo:

```json
{
  "message": "GET /api/v1/products",
  "audit": { "method": "GET", "url": "/api/v1/products" },
  "critical_action": false,
  "account_id": 123
}
```

### Métricas Recomendadas
1. **Ratio de auditoría crítica**: `critical_actions / total_requests`
2. **Escrituras en BD por hora**: Monitorear reducción
3. **Latencia P95**: Verificar que no hay degradación

## Consideraciones de Seguridad

### Sin Compromiso en Seguridad
- Todos los logs siguen disponibles en Cloud Logging
- Acciones críticas siguen en BD para cumplimiento
- Logs estructurados permiten búsquedas eficientes

### Datos Sensibles
- Logs de producción sanitizados (sin query params)
- Headers sensibles excluidos del logging
- Configuración diferenciada dev/prod

## Próximos Pasos

1. **Deploy**: Implementar en entorno de staging primero
2. **Monitoreo**: Configurar alertas para el ratio de auditoría crítica
3. **Validación**: Verificar reducción de costos en 1-2 semanas
4. **Ajuste**: Refinar criterios si es necesario basado en métricas reales

## Rollback Plan

Si es necesario volver al comportamiento anterior:

1. Cambiar `self._should_audit_to_database(method, path)` por `True`
2. O comentar la condición y mantener el comportamiento original
3. Los logs en Cloud Logging no se ven afectados

## Documentación Relacionada

- `gunicorn_conf.py`: Configuración de logs de acceso
- `src/utils/audit_tasks.py`: Tareas de escritura en BD
- `src/utils/base_logger.py`: Configuración de Cloud Logging 