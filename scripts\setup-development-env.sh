#!/bin/bash

# Script para configurar el entorno de desarrollo de Rayuela
# Resuelve problemas de configuración y prepara el entorno local

set -e

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

echo -e "${BLUE}🔧 Configurando Entorno de Desarrollo de Rayuela${NC}"
echo "=================================================="
echo ""

# Function to generate secure secret key
generate_secret_key() {
    python3 -c "import secrets; print(secrets.token_urlsafe(32))"
}

# Function to setup environment variables
setup_environment_variables() {
    echo -e "${BLUE}1. Configurando variables de entorno...${NC}"
    
    ENV_FILE="rayuela_backend/.env"
    
    if [ -f "$ENV_FILE" ]; then
        echo -e "${YELLOW}⚠️ Archivo .env ya existe, respaldando...${NC}"
        cp "$ENV_FILE" "$ENV_FILE.backup.$(date +%Y%m%d_%H%M%S)"
    fi
    
    echo "🔑 Generando SECRET_KEY segura..."
    SECRET_KEY=$(generate_secret_key)
    
    echo "📝 Creando archivo .env..."
    cat > "$ENV_FILE" << EOF
# Rayuela Backend Environment Variables - Development
# Generated: $(date)

# =============================================================================
# SECURITY CONFIGURATION
# =============================================================================
SECRET_KEY=$SECRET_KEY

# =============================================================================
# DATABASE CONFIGURATION (Development)
# =============================================================================
POSTGRES_USER=rayuela_user
POSTGRES_PASSWORD=dev_password_change_in_production
POSTGRES_SERVER=localhost
POSTGRES_PORT=5432
POSTGRES_DB=rayuela_dev

# =============================================================================
# REDIS CONFIGURATION (Development)
# =============================================================================
REDIS_HOST=localhost
REDIS_PORT=6379
REDIS_PASSWORD=
REDIS_URL=redis://localhost:6379

# =============================================================================
# APPLICATION SETTINGS
# =============================================================================
ENV=development
API_HOST=0.0.0.0
API_PORT=8000
LOG_LEVEL=INFO
DEBUG=True

# =============================================================================
# EXTERNAL SERVICES (Development - Optional)
# =============================================================================
MERCADOPAGO_ACCESS_TOKEN=TEST-your-test-token-here
GCS_BUCKET_NAME=rayuela-dev-bucket

# =============================================================================
# GCP CONFIGURATION (Optional for Development)
# =============================================================================
GCP_PROJECT_ID=rayuela-dev
GCP_REGION=us-central1

# =============================================================================
# DEVELOPMENT SPECIFIC
# =============================================================================
ALLOWED_HOSTS=["localhost", "127.0.0.1", "0.0.0.0"]
EOF
    
    echo -e "${GREEN}✅ Archivo .env creado exitosamente${NC}"
    echo "📋 SECRET_KEY generada: ${SECRET_KEY:0:10}... (truncada por seguridad)"
    echo ""
}

# Function to fix Pydantic warnings
fix_pydantic_warnings() {
    echo -e "${BLUE}2. Resolviendo warnings de Pydantic...${NC}"
    
    # Find RecommendationQueryRequest model
    SCHEMA_FILE="rayuela_backend/src/db/schemas/recommendation.py"
    
    if [ -f "$SCHEMA_FILE" ]; then
        echo "🔍 Verificando archivo: $SCHEMA_FILE"
        
        # Check if the warning is about model_type field
        if grep -q "model_type" "$SCHEMA_FILE"; then
            echo "⚠️ Campo 'model_type' encontrado que causa conflicto con namespace protegido"
            echo "💡 Creando backup del archivo..."
            cp "$SCHEMA_FILE" "$SCHEMA_FILE.backup.$(date +%Y%m%d_%H%M%S)"
            
            # Create a simple fix by adding model_config
            echo "🔧 Aplicando fix de Pydantic..."
            
            # This will be handled in a separate edit
            echo -e "${YELLOW}⚠️ Requiere edición manual del archivo${NC}"
            echo "   Archivo: $SCHEMA_FILE"
            echo "   Agregar: model_config = ConfigDict(protected_namespaces=())"
        else
            echo -e "${GREEN}✅ No se encontraron campos problemáticos${NC}"
        fi
    else
        echo -e "${YELLOW}⚠️ Archivo de esquema no encontrado, omitiendo fix${NC}"
    fi
    
    echo ""
}

# Function to test backend startup
test_backend_startup() {
    echo -e "${BLUE}3. Probando inicio del backend...${NC}"
    
    cd rayuela_backend
    
    echo "🧪 Verificando importación de la aplicación..."
    if python -c "from main import app; print('✅ App imported successfully')" 2>/dev/null; then
        echo -e "${GREEN}✅ Backend se importa correctamente${NC}"
    else
        echo -e "${RED}❌ Error importando backend${NC}"
        echo "🔍 Intentando diagnóstico..."
        python -c "from main import app; print('App imported')" || true
    fi
    
    cd ..
    echo ""
}

# Function to setup frontend environment
setup_frontend_environment() {
    echo -e "${BLUE}4. Configurando entorno del frontend...${NC}"
    
    cd rayuela_frontend
    
    ENV_LOCAL_FILE=".env.local"
    
    if [ -f "$ENV_LOCAL_FILE" ]; then
        echo -e "${YELLOW}⚠️ Archivo .env.local ya existe, respaldando...${NC}"
        cp "$ENV_LOCAL_FILE" "$ENV_LOCAL_FILE.backup.$(date +%Y%m%d_%H%M%S)"
    fi
    
    echo "📝 Creando .env.local para frontend..."
    cat > "$ENV_LOCAL_FILE" << EOF
# Rayuela Frontend Environment Variables - Development
# Generated: $(date)

# Backend API URL
NEXT_PUBLIC_API_URL=http://localhost:8001

# Development flags
NODE_ENV=development
EOF
    
    echo -e "${GREEN}✅ Frontend .env.local creado${NC}"
    
    cd ..
    echo ""
}

# Function to verify npm dependencies
verify_npm_dependencies() {
    echo -e "${BLUE}5. Verificando dependencias de npm...${NC}"
    
    cd rayuela_frontend
    
    if [ ! -d "node_modules" ]; then
        echo "📦 Instalando dependencias de npm..."
        npm install
    else
        echo -e "${GREEN}✅ Dependencias de npm ya instaladas${NC}"
    fi
    
    # Check if TypeScript compilation works
    echo "🔧 Verificando compilación TypeScript..."
    if npx tsc --noEmit --skipLibCheck 2>/dev/null; then
        echo -e "${GREEN}✅ TypeScript compila correctamente${NC}"
    else
        echo -e "${YELLOW}⚠️ Errores de TypeScript encontrados (normales durante desarrollo)${NC}"
    fi
    
    cd ..
    echo ""
}

# Function to create development scripts
create_development_scripts() {
    echo -e "${BLUE}6. Creando scripts de desarrollo...${NC}"
    
    # Create start backend script
    cat > "scripts/start-backend-dev.sh" << 'EOF'
#!/bin/bash
# Start Rayuela backend in development mode

cd rayuela_backend

# Check if .env exists
if [ ! -f ".env" ]; then
    echo "❌ .env file not found. Run ./scripts/setup-development-env.sh first"
    exit 1
fi

echo "🚀 Starting Rayuela backend..."
python main.py
EOF
    
    # Create start frontend script
    cat > "scripts/start-frontend-dev.sh" << 'EOF'
#!/bin/bash
# Start Rayuela frontend in development mode

cd rayuela_frontend

# Check if .env.local exists
if [ ! -f ".env.local" ]; then
    echo "❌ .env.local file not found. Run ./scripts/setup-development-env.sh first"
    exit 1
fi

echo "🚀 Starting Rayuela frontend..."
npm run dev
EOF
    
    # Create regenerate OpenAPI script
    cat > "scripts/regenerate-openapi.sh" << 'EOF'
#!/bin/bash
# Regenerate OpenAPI specification and client

echo "🔄 Regenerating OpenAPI specification..."

# Start backend temporarily to generate OpenAPI
cd rayuela_backend
python -c "
from main import app
import json

spec = app.openapi()
with open('../rayuela_frontend/src/lib/openapi/openapi.json', 'w') as f:
    json.dump(spec, f, indent=2)

print('✅ OpenAPI specification generated')
"

# Generate API client
cd ../rayuela_frontend
npx orval --config orval.config.ts

echo "✅ API client regenerated"
EOF
    
    chmod +x scripts/start-backend-dev.sh
    chmod +x scripts/start-frontend-dev.sh
    chmod +x scripts/regenerate-openapi.sh
    
    echo -e "${GREEN}✅ Scripts de desarrollo creados${NC}"
    echo ""
}

# Function to provide usage instructions
provide_usage_instructions() {
    echo -e "${BLUE}7. Instrucciones de uso${NC}"
    echo "======================"
    echo ""
    
    echo -e "${GREEN}🚀 Para iniciar el desarrollo:${NC}"
    echo ""
    echo "1. 🔧 Backend:"
    echo "   ./scripts/start-backend-dev.sh"
    echo ""
    echo "2. 🌐 Frontend (en otra terminal):"
    echo "   ./scripts/start-frontend-dev.sh"
    echo ""
    echo "3. 🔄 Regenerar OpenAPI cuando sea necesario:"
    echo "   ./scripts/regenerate-openapi.sh"
    echo ""
    
    echo -e "${YELLOW}📋 URLs de desarrollo:${NC}"
    echo "   Backend API: http://localhost:8001"
    echo "   Backend Docs: http://localhost:8001/api/docs"
    echo "   Frontend: http://localhost:3000"
    echo ""
    
    echo -e "${BLUE}🔍 Para verificar configuración:${NC}"
    echo "   ./scripts/validate-critical-implementations.sh"
    echo ""
}

# Main execution
echo "🚀 Iniciando configuración del entorno de desarrollo..."
echo ""

# Check if we're in the right directory
if [ ! -d "rayuela_backend" ] || [ ! -d "rayuela_frontend" ]; then
    echo -e "${RED}❌ Error: Execute this script from the project root directory${NC}"
    echo "Expected structure:"
    echo "  project-root/"
    echo "  ├── rayuela_backend/"
    echo "  └── rayuela_frontend/"
    exit 1
fi

# Execute setup steps
setup_environment_variables
fix_pydantic_warnings
test_backend_startup
setup_frontend_environment
verify_npm_dependencies
create_development_scripts
provide_usage_instructions

echo ""
echo "======================================================"
echo -e "${GREEN}🎉 Entorno de desarrollo configurado exitosamente!${NC}"
echo "======================================================"
echo ""
echo -e "${BLUE}💡 El entorno está listo para desarrollo. Usa los scripts creados para iniciar.${NC}" 