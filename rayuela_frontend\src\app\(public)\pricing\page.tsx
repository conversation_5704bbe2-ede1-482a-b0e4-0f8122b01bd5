import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card";
import { But<PERSON> } from "@/components/ui/button";
import { Badge } from "@/components/ui/badge";
import Link from "next/link";
import { generateMetadata as generateSEOMetadata } from '@/lib/seo';
import { Check, Star } from "lucide-react";
import { Container, Stack, Group, IconText } from '@/components/ui/spacing-system';

export const metadata = generateSEOMetadata({
  title: 'Precios y Planes',
  description: 'Planes flexibles para todos los tamaños de empresa. Desde startups hasta enterprise. Comienza gratis y escala según tus necesidades.',
  path: '/pricing',
  keywords: ['precios', 'planes', 'suscripción', 'API pricing', 'enterprise', 'startup'],
});

const plans = [
  {
    name: "Starter",
    price: "Gratis",
    period: "para siempre",
    description: "Perfecto para probar y proyectos pequeños",
    features: [
      "1,000 recomendaciones/mes",
      "1 modelo de recomendación",
      "API básica",
      "Documentación completa",
      "Soporte por email"
    ],
    cta: "Comenzar Gratis",
    href: "/register",
    popular: false
  },
  {
    name: "Professional",
    price: "$99",
    period: "/mes",
    description: "Para empresas en crecimiento",
    features: [
      "100,000 recomendaciones/mes",
      "5 modelos de recomendación",
      "API avanzada con analytics",
      "Webhooks y callbacks",
      "Soporte prioritario",
      "SLA 99.9%"
    ],
    cta: "Empezar Prueba",
    href: "/register",
    popular: true
  },
  {
    name: "Enterprise",
    price: "Personalizado",
    period: "",
    description: "Para grandes empresas con necesidades específicas",
    features: [
      "Recomendaciones ilimitadas",
      "Modelos personalizados",
      "Infraestructura dedicada",
      "Integración personalizada",
      "Soporte 24/7",
      "SLA 99.99%",
      "Consultoría incluida"
    ],
    cta: "Contactar Ventas",
    href: "/contact",
    popular: false
  }
];

export default function PricingPage() {
  const offerSchema = {
    '@context': 'https://schema.org',
    '@type': 'Product',
    name: 'Rayuela Recommendation API',
    description: 'Sistema de recomendaciones como servicio',
    offers: plans.map(plan => ({
      '@type': 'Offer',
      name: plan.name,
      description: plan.description,
      price: plan.price === 'Gratis' ? '0' : plan.price.replace('$', ''),
      priceCurrency: 'USD',
      availability: 'https://schema.org/InStock',
    })),
  };

  return (
    <>
      <script
        type="application/ld+json"
        dangerouslySetInnerHTML={{
          __html: JSON.stringify(offerSchema),
        }}
      />

      <div className="min-h-screen bg-gradient-to-br from-marketing-gradient-start to-marketing-gradient-end">
        <Container spacing="loose" maxWidth="full">
          <Stack spacing="xl">
            {/* Hero Section */}
            <div className="text-center">
              <Stack spacing="lg">
                <h1 className="text-4xl md:text-5xl font-bold text-gray-900 dark:text-white">
                  Precios Simples y Transparentes
                </h1>
                <p className="text-xl text-gray-600 dark:text-gray-300 max-w-3xl mx-auto">
                  Comienza gratis y escala según tus necesidades. Sin costos ocultos, sin compromisos a largo plazo.
                </p>
              </Stack>
            </div>

            {/* Pricing Cards */}
            <div className="grid grid-cols-1 md:grid-cols-3 gap-8">
            {plans.map((plan, index) => (
              <Card key={index} className={`relative h-full ${plan.popular ? 'border-blue-500 shadow-lg scale-105' : ''}`}>
                {plan.popular && (
                  <div className="absolute -top-4 left-1/2 transform -translate-x-1/2">
                    <Badge className="bg-blue-500 text-white px-4 py-1">
                      <IconText icon={<Star className="w-4 h-4" />} size="sm">
                        Más Popular
                      </IconText>
                    </Badge>
                  </div>
                )}

                <CardHeader className="text-center">
                  <CardTitle className="text-2xl">{plan.name}</CardTitle>
                  <div className="mt-4">
                    <span className="text-4xl font-bold text-gray-900 dark:text-white">
                      {plan.price}
                    </span>
                    <span className="text-gray-600 dark:text-gray-300">
                      {plan.period}
                    </span>
                  </div>
                  <CardDescription className="mt-2">
                    {plan.description}
                  </CardDescription>
                </CardHeader>

                <CardContent className="flex-1">
                  <Stack spacing="md" className="mb-8">
                    {plan.features.map((feature, featureIndex) => (
                      <IconText 
                        key={featureIndex} 
                        icon={<Check className="w-5 h-5 text-green-500 flex-shrink-0" />}
                        align="start"
                      >
                        <span className="text-gray-700 dark:text-gray-300">{feature}</span>
                      </IconText>
                    ))}
                  </Stack>

                  <Button
                    asChild
                    className="w-full"
                    variant={plan.popular ? "default" : "outline"}
                    size="lg"
                  >
                    <Link href={plan.href}>{plan.cta}</Link>
                  </Button>
                </CardContent>
              </Card>
            ))}
          </div>

          {/* FAQ Section */}
          <div className="bg-white dark:bg-gray-800 rounded-lg p-8 shadow-lg">
            <h2 className="text-3xl font-bold text-center text-gray-900 dark:text-white mb-8">
              Preguntas Frecuentes
            </h2>

            <div className="grid grid-cols-1 md:grid-cols-2 gap-8">
              <div>
                <h3 className="text-lg font-semibold text-gray-900 dark:text-white mb-2">
                  ¿Puedo cambiar de plan en cualquier momento?
                </h3>
                <p className="text-gray-600 dark:text-gray-300">
                  Sí, puedes actualizar o degradar tu plan en cualquier momento desde tu dashboard.
                </p>
              </div>

              <div>
                <h3 className="text-lg font-semibold text-gray-900 dark:text-white mb-2">
                  ¿Qué sucede si excedo mi límite?
                </h3>
                <p className="text-gray-600 dark:text-gray-300">
                  Te notificaremos cuando te acerques al límite. Puedes actualizar tu plan o las solicitudes adicionales se facturarán por separado.
                </p>
              </div>

              <div>
                <h3 className="text-lg font-semibold text-gray-900 dark:text-white mb-2">
                  ¿Ofrecen descuentos anuales?
                </h3>
                <p className="text-gray-600 dark:text-gray-300">
                  Sí, ofrecemos 2 meses gratis al pagar anualmente en los planes Professional y Enterprise.
                </p>
              </div>

              <div>
                <h3 className="text-lg font-semibold text-gray-900 dark:text-white mb-2">
                  ¿Hay período de prueba?
                </h3>
                <p className="text-gray-600 dark:text-gray-300">
                  El plan Starter es gratis para siempre. Los planes pagos incluyen 14 días de prueba gratuita.
                </p>
              </div>
            </div>
          </div>
          </Stack>
        </Container>
      </div>
    </>
  );
}
