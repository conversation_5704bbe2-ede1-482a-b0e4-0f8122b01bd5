#!/bin/bash

echo "🎨 Aplicando correcciones de colores directos a variables CSS semánticas..."

# Función para hacer backup
make_backup() {
    echo "📁 Creando backup de archivos..."
    cp -r src/ src_backup_$(date +%Y%m%d_%H%M%S)/
}

# Función para reemplazar colores en archivos TSX
fix_colors() {
    echo "🔧 Corrigiendo colores directos..."
    
    # Corregir iconos con colores directos
    find ./src -name "*.tsx" -type f -exec sed -i 's/text-blue-500/text-info/g' {} \;
    find ./src -name "*.tsx" -type f -exec sed -i 's/text-green-500/text-success/g' {} \;
    find ./src -name "*.tsx" -type f -exec sed -i 's/text-green-600/text-success/g' {} \;
    find ./src -name "*.tsx" -type f -exec sed -i 's/text-green-700/text-success/g' {} \;
    find ./src -name "*.tsx" -type f -exec sed -i 's/text-red-500/text-destructive/g' {} \;
    find ./src -name "*.tsx" -type f -exec sed -i 's/text-red-600/text-destructive/g' {} \;
    find ./src -name "*.tsx" -type f -exec sed -i 's/text-red-700/text-destructive/g' {} \;
    find ./src -name "*.tsx" -type f -exec sed -i 's/text-amber-500/text-warning/g' {} \;
    find ./src -name "*.tsx" -type f -exec sed -i 's/text-amber-600/text-warning/g' {} \;
    find ./src -name "*.tsx" -type f -exec sed -i 's/text-amber-700/text-warning/g' {} \;
    find ./src -name "*.tsx" -type f -exec sed -i 's/text-yellow-500/text-warning/g' {} \;
    
    # Corregir fondos de error
    find ./src -name "*.tsx" -type f -exec sed -i 's/bg-red-50 dark:bg-red-900\/20/bg-destructive\/5/g' {} \;
    find ./src -name "*.tsx" -type f -exec sed -i 's/border-red-200 dark:border-red-800/border-destructive\/20/g' {} \;
    find ./src -name "*.tsx" -type f -exec sed -i 's/border-red-500/border-destructive/g' {} \;
    find ./src -name "*.tsx" -type f -exec sed -i 's/text-red-600 dark:text-red-400/text-destructive/g' {} \;
    find ./src -name "*.tsx" -type f -exec sed -i 's/text-red-700 dark:hover:text-red-300/text-destructive/g' {} \;
    find ./src -name "*.tsx" -type f -exec sed -i 's/text-red-400/text-destructive\/70/g' {} \;
    
    # Corregir elementos de éxito
    find ./src -name "*.tsx" -type f -exec sed -i 's/bg-green-50 dark:bg-green-900\/20/bg-success-light/g' {} \;
    find ./src -name "*.tsx" -type f -exec sed -i 's/border-green-200 dark:border-green-800/border-success\/20/g' {} \;
    find ./src -name "*.tsx" -type f -exec sed -i 's/text-green-600 dark:text-green-400/text-success/g' {} \;
    find ./src -name "*.tsx" -type f -exec sed -i 's/text-green-700 dark:text-green-300/text-success/g' {} \;
    find ./src -name "*.tsx" -type f -exec sed -i 's/text-green-800 dark:text-green-200/text-success-foreground/g' {} \;
    
    # Corregir elementos de información
    find ./src -name "*.tsx" -type f -exec sed -i 's/bg-blue-50 dark:bg-blue-900\/20/bg-info-light/g' {} \;
    find ./src -name "*.tsx" -type f -exec sed -i 's/border-blue-200 dark:border-blue-800/border-info\/20/g' {} \;
    find ./src -name "*.tsx" -type f -exec sed -i 's/text-blue-600 dark:text-blue-400/text-info/g' {} \;
    find ./src -name "*.tsx" -type f -exec sed -i 's/text-blue-700 dark:text-blue-300/text-info/g' {} \;
    find ./src -name "*.tsx" -type f -exec sed -i 's/text-blue-800 dark:text-blue-200/text-info-foreground/g' {} \;
    
    # Corregir elementos de advertencia
    find ./src -name "*.tsx" -type f -exec sed -i 's/bg-amber-50 dark:bg-amber-900\/20/bg-warning-light/g' {} \;
    find ./src -name "*.tsx" -type f -exec sed -i 's/border-amber-200 dark:border-amber-800/border-warning\/20/g' {} \;
    find ./src -name "*.tsx" -type f -exec sed -i 's/border-amber-500/border-warning/g' {} \;
    find ./src -name "*.tsx" -type f -exec sed -i 's/text-amber-600 dark:text-amber-400/text-warning/g' {} \;
    find ./src -name "*.tsx" -type f -exec sed -i 's/text-amber-700 dark:text-amber-300/text-warning/g' {} \;
    find ./src -name "*.tsx" -type f -exec sed -i 's/text-amber-800 dark:text-amber-300/text-warning-foreground/g' {} \;
    
    # Corregir hover states
    find ./src -name "*.tsx" -type f -exec sed -i 's/hover:bg-red-50 hover:text-red-600 hover:border-red-200/hover:bg-destructive\/10 hover:text-destructive hover:border-destructive\/20/g' {} \;
    find ./src -name "*.tsx" -type f -exec sed -i 's/hover:bg-blue-50 hover:text-blue-600 hover:border-blue-200/hover:bg-info\/10 hover:text-info hover:border-info\/20/g' {} \;
    find ./src -name "*.tsx" -type f -exec sed -i 's/hover:bg-green-50 hover:text-green-600 hover:border-green-200/hover:bg-success\/10 hover:text-success hover:border-success\/20/g' {} \;
    
    # Corregir focus rings
    find ./src -name "*.tsx" -type f -exec sed -i 's/focus:ring-red-200/focus:ring-destructive\/20/g' {} \;
    find ./src -name "*.tsx" -type f -exec sed -i 's/focus:ring-blue-200/focus:ring-info\/20/g' {} \;
    find ./src -name "*.tsx" -type f -exec sed -i 's/focus:ring-green-200/focus:ring-success\/20/g' {} \;
    
    # Corregir badges específicos
    find ./src -name "*.tsx" -type f -exec sed -i 's/bg-green-50 text-green-700 border-green-200 dark:bg-green-900\/20 dark:text-green-300 dark:border-green-800/variant="success"/g' {} \;
    find ./src -name "*.tsx" -type f -exec sed -i 's/bg-blue-50 text-blue-700 border-blue-200 dark:bg-blue-900\/20 dark:text-blue-300 dark:border-blue-800/variant="info"/g' {} \;
    find ./src -name "*.tsx" -type f -exec sed -i 's/bg-red-50 text-red-700 border-red-200 dark:bg-red-900\/20 dark:text-red-300 dark:border-red-800/variant="destructive"/g' {} \;
    
    # Corregir tabs active states
    find ./src -name "*.tsx" -type f -exec sed -i 's/data-\[state=active\]:bg-blue-100 data-\[state=active\]:text-blue-700 dark:data-\[state=active\]:bg-blue-900\/30 dark:data-\[state=active\]:text-blue-300/data-[state=active]:bg-info\/10 data-[state=active]:text-info/g' {} \;
    find ./src -name "*.tsx" -type f -exec sed -i 's/data-\[state=active\]:bg-green-100 data-\[state=active\]:text-green-700 dark:data-\[state=active\]:bg-green-900\/30 dark:data-\[state=active\]:text-green-300/data-[state=active]:bg-success\/10 data-[state=active]:text-success/g' {} \;
    
    # Corregir elementos de métrica
    find ./src -name "*.tsx" -type f -exec sed -i 's/text-blue-600 dark:text-blue-400/text-info/g' {} \;
    find ./src -name "*.tsx" -type f -exec sed -i 's/text-green-600 dark:text-green-400/text-success/g' {} \;
    
    # Corregir indicadores de progreso
    find ./src -name "*.tsx" -type f -exec sed -i 's/bg-green-500/bg-success/g' {} \;
    find ./src -name "*.tsx" -type f -exec sed -i 's/bg-amber-500/bg-warning/g' {} \;
    find ./src -name "*.tsx" -type f -exec sed -i 's/bg-red-500/bg-destructive/g' {} \;
    
    # Corregir gradientes específicos
    find ./src -name "*.tsx" -type f -exec sed -i 's/from-indigo-400 to-indigo-500 dark:from-indigo-500 dark:to-indigo-600/from-primary to-primary\/80/g' {} \;
    
    # Corregir highlights específicos
    find ./src -name "*.tsx" -type f -exec sed -i 's/bg-blue-100 dark:bg-blue-900\/50/bg-info-light/g' {} \;
    find ./src -name "*.tsx" -type f -exec sed -i 's/bg-amber-100 dark:bg-amber-900\/50/bg-warning-light/g' {} \;
    
    # Corregir botones específicos
    find ./src -name "*.tsx" -type f -exec sed -i 's/bg-blue-600 hover:bg-blue-700/bg-primary hover:bg-primary\/90/g' {} \;
    find ./src -name "*.tsx" -type f -exec sed -i 's/border-blue-600 text-blue-600 hover:bg-blue-50/border-primary text-primary hover:bg-primary\/10/g' {} \;
    
    echo "✅ Correcciones aplicadas exitosamente!"
}

# Función para validar correcciones
validate_corrections() {
    echo "🔍 Validando correcciones..."
    
    # Buscar colores directos restantes
    REMAINING_COLORS=$(grep -r "text-\(blue\|green\|red\|amber\|yellow\)-[0-9]" src/ --include="*.tsx" | wc -l)
    REMAINING_BACKGROUNDS=$(grep -r "bg-\(blue\|green\|red\|amber\|yellow\)-[0-9]" src/ --include="*.tsx" | wc -l)
    REMAINING_BORDERS=$(grep -r "border-\(blue\|green\|red\|amber\|yellow\)-[0-9]" src/ --include="*.tsx" | wc -l)
    
    echo "📊 Resultados de la validación:"
    echo "   Colores de texto restantes: $REMAINING_COLORS"
    echo "   Fondos restantes: $REMAINING_BACKGROUNDS" 
    echo "   Bordes restantes: $REMAINING_BORDERS"
    
    if [ $REMAINING_COLORS -eq 0 ] && [ $REMAINING_BACKGROUNDS -eq 0 ] && [ $REMAINING_BORDERS -eq 0 ]; then
        echo "🎉 ¡Todas las correcciones aplicadas exitosamente!"
        echo "✅ Sistema de colores 100% unificado"
    else
        echo "⚠️  Quedan algunos colores directos por corregir"
        echo "📋 Archivos con colores restantes:"
        grep -r "text-\(blue\|green\|red\|amber\|yellow\)-[0-9]\|bg-\(blue\|green\|red\|amber\|yellow\)-[0-9]\|border-\(blue\|green\|red\|amber\|yellow\)-[0-9]" src/ --include="*.tsx" -l | head -10
    fi
}

# Función principal
main() {
    echo "🚀 Iniciando corrección masiva del sistema de colores..."
    echo "=================================================="
    
    # Crear backup
    make_backup
    
    # Aplicar correcciones
    fix_colors
    
    # Validar resultado
    validate_corrections
    
    echo ""
    echo "=================================================="
    echo "✨ Proceso completado. Tu sistema de colores oklch"
    echo "   ahora es 100% consistente y accesible."
    echo ""
    echo "📁 Backup creado en: src_backup_$(date +%Y%m%d_%H%M%S)/"
    echo "🔄 Ejecuta 'npm run dev' para ver los cambios"
}

# Ejecutar script
main 