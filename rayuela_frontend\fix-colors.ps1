# Script PowerShell para corregir colores directos de Tailwind a variables CSS semánticas
# Autor: Asistente IA - Sistema de Colores Unificado Rayuela

Write-Host "🎨 Aplicando correcciones de colores directos a variables CSS semánticas..." -ForegroundColor Cyan
Write-Host "=================================================================" -ForegroundColor Blue

# Función para crear backup
function Make-Backup {
    Write-Host "📁 Creando backup de archivos..." -ForegroundColor Yellow
    $backupName = "src_backup_$(Get-Date -Format 'yyyyMMdd_HHmmss')"
    Copy-Item -Path "src" -Destination $backupName -Recurse
    Write-Host "✅ Backup creado en: $backupName" -ForegroundColor Green
}

# Función para aplicar correcciones
function Fix-Colors {
    Write-Host "🔧 Corrigiendo colores directos..." -ForegroundColor Yellow
    
    # Obtener todos los archivos TSX
    $tsxFiles = Get-ChildItem -Path "src" -Filter "*.tsx" -Recurse
    $totalFiles = $tsxFiles.Count
    $currentFile = 0
    
    foreach ($file in $tsxFiles) {
        $currentFile++
        Write-Progress -Activity "Corrigiendo archivos" -Status "Procesando $($file.Name)" -PercentComplete (($currentFile / $totalFiles) * 100)
        
        $content = Get-Content $file.FullName -Raw
        $originalContent = $content
        
        # Corregir iconos con colores directos
        $content = $content -replace 'text-blue-500', 'text-info'
        $content = $content -replace 'text-green-500', 'text-success'
        $content = $content -replace 'text-green-600', 'text-success'
        $content = $content -replace 'text-green-700', 'text-success'
        $content = $content -replace 'text-red-500', 'text-destructive'
        $content = $content -replace 'text-red-600', 'text-destructive'
        $content = $content -replace 'text-red-700', 'text-destructive'
        $content = $content -replace 'text-amber-500', 'text-warning'
        $content = $content -replace 'text-amber-600', 'text-warning'
        $content = $content -replace 'text-amber-700', 'text-warning'
        $content = $content -replace 'text-yellow-500', 'text-warning'
        
        # Corregir fondos de error
        $content = $content -replace 'bg-red-50 dark:bg-red-900/20', 'bg-destructive/5'
        $content = $content -replace 'border-red-200 dark:border-red-800', 'border-destructive/20'
        $content = $content -replace 'border-red-500', 'border-destructive'
        $content = $content -replace 'text-red-600 dark:text-red-400', 'text-destructive'
        $content = $content -replace 'text-red-700 dark:hover:text-red-300', 'text-destructive'
        $content = $content -replace 'text-red-400', 'text-destructive/70'
        
        # Corregir elementos de éxito
        $content = $content -replace 'bg-green-50 dark:bg-green-900/20', 'bg-success-light'
        $content = $content -replace 'border-green-200 dark:border-green-800', 'border-success/20'
        $content = $content -replace 'text-green-600 dark:text-green-400', 'text-success'
        $content = $content -replace 'text-green-700 dark:text-green-300', 'text-success'
        $content = $content -replace 'text-green-800 dark:text-green-200', 'text-success-foreground'
        
        # Corregir elementos de información
        $content = $content -replace 'bg-blue-50 dark:bg-blue-900/20', 'bg-info-light'
        $content = $content -replace 'border-blue-200 dark:border-blue-800', 'border-info/20'
        $content = $content -replace 'text-blue-600 dark:text-blue-400', 'text-info'
        $content = $content -replace 'text-blue-700 dark:text-blue-300', 'text-info'
        $content = $content -replace 'text-blue-800 dark:text-blue-200', 'text-info-foreground'
        
        # Corregir elementos de advertencia
        $content = $content -replace 'bg-amber-50 dark:bg-amber-900/20', 'bg-warning-light'
        $content = $content -replace 'border-amber-200 dark:border-amber-800', 'border-warning/20'
        $content = $content -replace 'border-amber-500', 'border-warning'
        $content = $content -replace 'text-amber-600 dark:text-amber-400', 'text-warning'
        $content = $content -replace 'text-amber-700 dark:text-amber-300', 'text-warning'
        $content = $content -replace 'text-amber-800 dark:text-amber-300', 'text-warning-foreground'
        
        # Corregir hover states
        $content = $content -replace 'hover:bg-red-50 hover:text-red-600 hover:border-red-200', 'hover:bg-destructive/10 hover:text-destructive hover:border-destructive/20'
        $content = $content -replace 'hover:bg-blue-50 hover:text-blue-600 hover:border-blue-200', 'hover:bg-info/10 hover:text-info hover:border-info/20'
        $content = $content -replace 'hover:bg-green-50 hover:text-green-600 hover:border-green-200', 'hover:bg-success/10 hover:text-success hover:border-success/20'
        
        # Corregir focus rings
        $content = $content -replace 'focus:ring-red-200', 'focus:ring-destructive/20'
        $content = $content -replace 'focus:ring-blue-200', 'focus:ring-info/20'
        $content = $content -replace 'focus:ring-green-200', 'focus:ring-success/20'
        
        # Corregir badges específicos (versión simplificada)
        $content = $content -replace 'className="bg-green-50 text-green-700 border-green-200[^"]*"', 'variant="success"'
        $content = $content -replace 'className="bg-blue-50 text-blue-700 border-blue-200[^"]*"', 'variant="info"'
        $content = $content -replace 'className="bg-red-50 text-red-700 border-red-200[^"]*"', 'variant="destructive"'
        
        # Corregir tabs active states
        $content = $content -replace 'data-\[state=active\]:bg-blue-100 data-\[state=active\]:text-blue-700 dark:data-\[state=active\]:bg-blue-900/30 dark:data-\[state=active\]:text-blue-300', 'data-[state=active]:bg-info/10 data-[state=active]:text-info'
        $content = $content -replace 'data-\[state=active\]:bg-green-100 data-\[state=active\]:text-green-700 dark:data-\[state=active\]:bg-green-900/30 dark:data-\[state=active\]:text-green-300', 'data-[state=active]:bg-success/10 data-[state=active]:text-success'
        
        # Corregir indicadores de progreso
        $content = $content -replace 'bg-green-500', 'bg-success'
        $content = $content -replace 'bg-amber-500', 'bg-warning'
        $content = $content -replace 'bg-red-500', 'bg-destructive'
        
        # Corregir gradientes específicos
        $content = $content -replace 'from-indigo-400 to-indigo-500 dark:from-indigo-500 dark:to-indigo-600', 'from-primary to-primary/80'
        
        # Corregir highlights específicos
        $content = $content -replace 'bg-blue-100 dark:bg-blue-900/50', 'bg-info-light'
        $content = $content -replace 'bg-amber-100 dark:bg-amber-900/50', 'bg-warning-light'
        
        # Corregir botones específicos
        $content = $content -replace 'bg-blue-600 hover:bg-blue-700', 'bg-primary hover:bg-primary/90'
        $content = $content -replace 'border-blue-600 text-blue-600 hover:bg-blue-50', 'border-primary text-primary hover:bg-primary/10'
        
        # Solo escribir si hay cambios
        if ($content -ne $originalContent) {
            Set-Content -Path $file.FullName -Value $content -NoNewline
        }
    }
    
    Write-Progress -Activity "Corrigiendo archivos" -Completed
    Write-Host "✅ Correcciones aplicadas exitosamente!" -ForegroundColor Green
}

# Función para validar correcciones
function Validate-Corrections {
    Write-Host "🔍 Validando correcciones..." -ForegroundColor Yellow
    
    # Buscar colores directos restantes
    $remainingColors = Select-String -Path "src\*.tsx" -Pattern "text-(blue|green|red|amber|yellow)-\d" -Recurse | Measure-Object | Select-Object -ExpandProperty Count
    $remainingBackgrounds = Select-String -Path "src\*.tsx" -Pattern "bg-(blue|green|red|amber|yellow)-\d" -Recurse | Measure-Object | Select-Object -ExpandProperty Count
    $remainingBorders = Select-String -Path "src\*.tsx" -Pattern "border-(blue|green|red|amber|yellow)-\d" -Recurse | Measure-Object | Select-Object -ExpandProperty Count
    
    Write-Host "📊 Resultados de la validación:" -ForegroundColor Cyan
    Write-Host "   Colores de texto restantes: $remainingColors" -ForegroundColor White
    Write-Host "   Fondos restantes: $remainingBackgrounds" -ForegroundColor White
    Write-Host "   Bordes restantes: $remainingBorders" -ForegroundColor White
    
    if ($remainingColors -eq 0 -and $remainingBackgrounds -eq 0 -and $remainingBorders -eq 0) {
        Write-Host "🎉 ¡Todas las correcciones aplicadas exitosamente!" -ForegroundColor Green
        Write-Host "✅ Sistema de colores 100% unificado" -ForegroundColor Green
    } else {
        Write-Host "⚠️  Quedan algunos colores directos por corregir" -ForegroundColor Yellow
        Write-Host "📋 Archivos con colores restantes:" -ForegroundColor Yellow
        Select-String -Path "src\*.tsx" -Pattern "(text|bg|border)-(blue|green|red|amber|yellow)-\d" -Recurse | Select-Object -First 10 | ForEach-Object { Write-Host "   $($_.Filename)" -ForegroundColor Gray }
    }
}

# Función principal
function Main {
    Write-Host "🚀 Iniciando corrección masiva del sistema de colores..." -ForegroundColor Magenta
    Write-Host "=================================================================" -ForegroundColor Blue
    
    # Verificar que estamos en el directorio correcto
    if (-not (Test-Path "src")) {
        Write-Host "❌ Error: No se encontró el directorio 'src'. Asegúrate de ejecutar el script desde rayuela_frontend/" -ForegroundColor Red
        return
    }
    
    # Crear backup
    Make-Backup
    
    # Aplicar correcciones
    Fix-Colors
    
    # Validar resultado
    Validate-Corrections
    
    Write-Host ""
    Write-Host "=================================================================" -ForegroundColor Blue
    Write-Host "✨ Proceso completado. Tu sistema de colores oklch" -ForegroundColor Green
    Write-Host "   ahora es 100% consistente y accesible." -ForegroundColor Green
    Write-Host ""
    Write-Host "📁 Backup creado con timestamp" -ForegroundColor Cyan
    Write-Host "🔄 Ejecuta 'npm run dev' para ver los cambios" -ForegroundColor Cyan
}

# Ejecutar script
Main 