# Global Email Uniqueness Security Fix

## Problema de Seguridad Identificado

### Descripción del Riesgo
El servicio de autenticación (`AuthService`) intentaba aplicar la unicidad global del email para los `SystemUser`, pero la ausencia de una restricción `UNIQUE` a nivel de base de datos en la columna `email` de la tabla `system_users` (sin incluir `account_id`) permitía condiciones de carrera.

### Impacto Potencial
- **Confusión del usuario**: Múltiples cuentas con el mismo email
- **Vectores de ataque**: Restablecimiento de contraseña a la cuenta equivocada
- **Escalada de privilegios**: Posible toma de control de cuentas si los flujos de restablecimiento de contraseña no manejaban emails no únicos de forma segura

## Solución Implementada

### 1. Cambios en el Modelo de Base de Datos

**Antes:**
```python
Index("idx_system_user_email", "account_id", "email", unique=True)
```

**Después:**
```python
Index("idx_system_user_email_global", "email", unique=True)
```

### 2. Migración de Base de Datos

Se creó la migración `20250105_000000_add_global_email_uniqueness_system_users.py` que:

1. **Elimina** el índice compuesto anterior: `idx_system_user_email`
2. **Crea** un nuevo índice único global: `idx_system_user_email_global`

```python
def upgrade():
    # Drop the old compound unique index (account_id, email)
    op.drop_index('idx_system_user_email', table_name='system_users')
    
    # Create new global unique index on email only
    op.create_index('idx_system_user_email_global', 'system_users', ['email'], unique=True)
```

### 3. Mejoras en el Manejo de Errores

Se actualizó el `AuthService` para manejar violaciones de restricción de integridad:

```python
except IntegrityError as e:
    # Manejar violaciones de restricción de unicidad de email a nivel de base de datos
    if "idx_system_user_email_global" in str(e.orig) or "email" in str(e.orig).lower():
        log_warning(f"Database constraint violation for email {email}: {str(e)}")
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail="Email already exists globally. Please use a different email address.",
        )
```

## Beneficios de la Solución

### 1. Prevención de Condiciones de Carrera
- La restricción a nivel de base de datos garantiza atomicidad
- Elimina la ventana de tiempo entre verificación y inserción
- Funciona correctamente en entornos de alta concurrencia

### 2. Robustez de la Aplicación
- La aplicación maneja elegantemente las violaciones de restricción
- Proporciona mensajes de error consistentes al usuario
- Mantiene la funcionalidad existente intacta

### 3. Seguridad Mejorada
- Previene la creación de cuentas con emails duplicados
- Elimina vectores de ataque relacionados con emails no únicos
- Asegura que los flujos de restablecimiento de contraseña sean seguros

## Tests de Verificación

Se crearon tests exhaustivos para verificar:

1. **Prevención de duplicados**: La restricción DB previene emails duplicados
2. **Manejo de errores**: El servicio maneja violaciones elegantemente
3. **Condiciones de carrera**: Registros concurrentes se manejan correctamente
4. **Funcionalidad existente**: Login y otras operaciones siguen funcionando

## Consideraciones de Migración

### Prerequisitos
- Verificar que no existan emails duplicados en la base de datos actual
- Si existen duplicados, ejecutar script de limpieza antes de la migración

### Comando de Migración
```bash
cd rayuela_backend
python -m alembic upgrade head
```

### Rollback (si es necesario)
```bash
cd rayuela_backend
python -m alembic downgrade 20250614_130000
```

## Impacto en la Aplicación

### Cambios No Disruptivos
- La funcionalidad existente se mantiene intacta
- Los usuarios no notan cambios en el comportamiento
- Las API responses siguen siendo las mismas

### Mejoras Transparentes
- Mayor robustez en condiciones de alta concurrencia
- Eliminación de vulnerabilidades de seguridad
- Mejor manejo de errores de duplicados

## Validación Post-Despliegue

### 1. Verificar Restricción Activa
```sql
-- PostgreSQL
SELECT 
    indexname, 
    indexdef 
FROM pg_indexes 
WHERE tablename = 'system_users' 
    AND indexdef LIKE '%UNIQUE%';
```

### 2. Test de Funcionalidad
- Registrar cuenta nueva (debe funcionar)
- Intentar registrar con email existente (debe fallar con error 400)
- Login con cuenta existente (debe funcionar)

### 3. Test de Concurrencia
- Ejecutar múltiples registros simultáneos con el mismo email
- Verificar que solo uno tenga éxito

## Referencias

- **Archivos modificados:**
  - `rayuela_backend/src/db/models/system_user.py`
  - `rayuela_backend/src/services/auth_service.py`
  - `rayuela_backend/alembic/versions/20250105_000000_add_global_email_uniqueness_system_users.py`

- **Tests relacionados:**
  - `rayuela_backend/tests/unit/test_global_email_uniqueness.py`
  - `rayuela_backend/tests/integration/test_global_email_uniqueness_constraint.py`

- **Prioridad**: **Crítica** - Fix de seguridad que previene vulnerabilidades de escalada de privilegios 