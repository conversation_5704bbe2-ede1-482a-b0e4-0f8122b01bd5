import pytest
from unittest.mock import AsyncMock, MagicMock, patch
from src.middleware.audit import AuditMiddleware


class TestAuditMiddlewareOptimization:
    """
    Tests para verificar la optimización de granularidad del registro de auditoría.
    """

    def setup_method(self):
        """Setup para cada test."""
        self.middleware = AuditMiddleware(app=MagicMock())

    def test_should_audit_to_database_critical_endpoints(self):
        """
        Test: Endpoints críticos con métodos que cambian estado deben auditarse en BD.
        """
        # Casos que SÍ deben auditarse en BD
        critical_cases = [
            ("POST", "/api/v1/products"),
            ("PUT", "/api/v1/products/123"),
            ("DELETE", "/api/v1/products/456"),
            ("PATCH", "/api/v1/recommendations/789"),
            ("POST", "/api/v1/auth/login"),
            ("DELETE", "/api/v1/api-keys/abc123"),
            ("PUT", "/api/v1/roles/admin"),
            ("POST", "/api/v1/maintenance/cleanup"),
            ("GET", "/api/v1/maintenance/status"),  # Cualquier método en maintenance
            ("GET", "/api/v1/system-users"),  # Cualquier método en system-users
        ]

        for method, path in critical_cases:
            assert self.middleware._should_audit_to_database(method, path), \
                f"Debería auditar {method} {path} en BD"

    def test_should_not_audit_to_database_non_critical(self):
        """
        Test: Endpoints no críticos o métodos de solo lectura no deben auditarse en BD.
        """
        # Casos que NO deben auditarse en BD
        non_critical_cases = [
            ("GET", "/api/v1/products"),  # Solo lectura en endpoint crítico
            ("GET", "/api/v1/recommendations"),  # Solo lectura
            ("POST", "/api/v1/interactions"),  # Endpoint no crítico
            ("GET", "/api/v1/analytics"),  # Solo lectura en endpoint no crítico
            ("PUT", "/api/v1/usage"),  # Endpoint no crítico
            ("GET", "/health"),  # Endpoint de salud
            ("GET", "/api/docs"),  # Documentación
        ]

        for method, path in non_critical_cases:
            assert not self.middleware._should_audit_to_database(method, path), \
                f"NO debería auditar {method} {path} en BD"

    def test_auth_endpoints_always_audited(self):
        """
        Test: Todos los endpoints de autenticación deben auditarse.
        """
        auth_cases = [
            ("POST", "/api/v1/auth/login"),
            ("POST", "/api/v1/auth/logout"),
            ("GET", "/api/v1/auth/me"),
            ("PUT", "/api/v1/auth/change-password"),
        ]

        for method, path in auth_cases:
            assert self.middleware._should_audit_to_database(method, path), \
                f"Debería auditar operación de auth: {method} {path}"

    def test_admin_endpoints_always_audited(self):
        """
        Test: Todos los endpoints de administración deben auditarse.
        """
        admin_cases = [
            ("GET", "/api/v1/maintenance/logs"),
            ("POST", "/api/v1/maintenance/archive"),
            ("DELETE", "/api/v1/maintenance/cleanup"),
            ("GET", "/api/v1/system-users/list"),
            ("POST", "/api/v1/system-users/create"),
            ("PUT", "/api/v1/system-users/123"),
        ]

        for method, path in admin_cases:
            assert self.middleware._should_audit_to_database(method, path), \
                f"Debería auditar operación de admin: {method} {path}"

    @pytest.mark.asyncio
    async def test_optimization_reduces_database_writes(self):
        """
        Test de integración: Verificar que la optimización reduce las escrituras a BD.
        """
        # Mock de las dependencias
        mock_request = MagicMock()
        mock_request.url.path = "/api/v1/analytics"  # Endpoint no crítico
        mock_request.method = "GET"
        mock_request.query_params = {}
        mock_request.client.host = "127.0.0.1"
        mock_request.headers = {"user-agent": "test-agent"}

        mock_response = MagicMock()
        mock_response.status_code = 200
        mock_response.headers = {}

        async def mock_call_next(request):
            return mock_response

        # Mock de las funciones de base de datos y logging
        with patch('src.middleware.audit.get_user_and_account_ids') as mock_get_ids, \
             patch('src.middleware.audit.get_db') as mock_get_db, \
             patch('src.middleware.audit.log_info') as mock_log_info, \
             patch('src.middleware.audit.BackgroundTasks') as mock_bg_tasks, \
             patch('src.middleware.audit.write_audit_log_to_db_task') as mock_write_task:

            # Configurar mocks
            mock_get_ids.return_value = ("user123", 456)
            mock_get_db.return_value.__aenter__.return_value = MagicMock()
            mock_bg_tasks_instance = MagicMock()
            mock_bg_tasks.return_value = mock_bg_tasks_instance

            # Ejecutar el middleware
            result = await self.middleware.dispatch(mock_request, mock_call_next)

            # Verificaciones
            assert result == mock_response
            
            # Debe llamar a log_info (Cloud Logging) pero NO a write_audit_log_to_db_task
            mock_log_info.assert_called()
            mock_write_task.assert_not_called()
            
            # No debe crear BackgroundTasks para escritura en BD
            # (podría crear para analytics, pero no para audit)
            calls_to_add_task = mock_bg_tasks_instance.add_task.call_args_list
            audit_calls = [call for call in calls_to_add_task 
                          if 'write_audit_log_to_db_task' in str(call)]
            assert len(audit_calls) == 0, "No debe escribir auditoría no crítica en BD"

    @pytest.mark.asyncio
    async def test_critical_action_writes_to_database(self):
        """
        Test de integración: Verificar que las acciones críticas SÍ se escriben en BD.
        """
        # Mock de las dependencias
        mock_request = MagicMock()
        mock_request.url.path = "/api/v1/products"  # Endpoint crítico
        mock_request.method = "POST"  # Método que cambia estado
        mock_request.query_params = {}
        mock_request.client.host = "127.0.0.1"
        mock_request.headers = {"user-agent": "test-agent"}

        mock_response = MagicMock()
        mock_response.status_code = 201
        mock_response.headers = {}

        async def mock_call_next(request):
            return mock_response

        # Mock de las funciones de base de datos y logging
        with patch('src.middleware.audit.get_user_and_account_ids') as mock_get_ids, \
             patch('src.middleware.audit.get_db') as mock_get_db, \
             patch('src.middleware.audit.log_info') as mock_log_info, \
             patch('src.middleware.audit.BackgroundTasks') as mock_bg_tasks:

            # Configurar mocks
            mock_get_ids.return_value = ("user123", 456)
            mock_get_db.return_value.__aenter__.return_value = MagicMock()
            mock_bg_tasks_instance = MagicMock()
            mock_bg_tasks.return_value = mock_bg_tasks_instance

            # Ejecutar el middleware
            result = await self.middleware.dispatch(mock_request, mock_call_next)

            # Verificaciones
            assert result == mock_response
            
            # Debe llamar a log_info (Cloud Logging)
            mock_log_info.assert_called()
            
            # Debe crear BackgroundTasks para escritura en BD
            mock_bg_tasks.assert_called()
            mock_bg_tasks_instance.add_task.assert_called()
            
            # Verificar que se programa la tarea de auditoría
            calls_to_add_task = mock_bg_tasks_instance.add_task.call_args_list
            audit_calls = [call for call in calls_to_add_task 
                          if 'write_audit_log_to_db_task' in str(call)]
            assert len(audit_calls) > 0, "Debe escribir auditoría crítica en BD"


if __name__ == "__main__":
    pytest.main([__file__, "-v"]) 