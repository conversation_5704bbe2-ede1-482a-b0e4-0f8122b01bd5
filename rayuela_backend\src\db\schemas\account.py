from pydantic import Field
from typing import Optional, Dict, Any
from datetime import datetime
from .base import CamelCaseModel


class AccountBase(CamelCaseModel):
    name: str = Field(..., min_length=1)


class AccountCreate(AccountBase):
    # stripe_customer_id eliminado
    mercadopago_customer_id: Optional[str] = None
    is_active: bool = True
    # Considera añadir email/password del primer usuario admin aquí si se crea junto
    # initial_admin_email: EmailStr
    # initial_admin_password: str


class AccountUpdate(CamelCaseModel):
    name: Optional[str] = None  # Permitir actualizar solo el nombre
    # stripe_customer_id eliminado
    mercadopago_customer_id: Optional[str] = None
    is_active: Optional[bool] = None
    onboarding_checklist_status: Optional[Dict[str, Any]] = None


class SubscriptionBasicInfo(CamelCaseModel):
    """Información básica de suscripción para incluir en las respuestas de cuenta"""
    plan: str
    is_active: bool
    expires_at: Optional[datetime] = None


class AccountResponse(AccountBase):
    account_id: int
    name: str
    # stripe_customer_id eliminado
    mercadopago_customer_id: Optional[str] = None  # ID del cliente en Mercado Pago
    created_at: datetime
    updated_at: datetime
    is_active: bool
    deleted_at: Optional[datetime] = None
    onboarding_checklist_status: Optional[Dict[str, Any]] = None
    # Información básica sobre la suscripción
    subscription: Optional[SubscriptionBasicInfo] = None

    class ConfigDict:
        from_attributes = True  # Habilitar compatibilidad con ORM
