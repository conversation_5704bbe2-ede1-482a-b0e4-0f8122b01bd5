import React from 'react';

/**
 * Componente de demostración para las nuevas clases tipográficas semánticas
 * Este componente muestra todos los estilos disponibles y sirve como guía de uso
 */
export function TypographyShowcase() {
  return (
    <div className="max-w-4xl mx-auto p-8 space-y-12">
      {/* Header */}
      <header className="text-center border-b pb-8">
        <h1 className="text-display-large mb-4">
          Sistema de Tipografía Rayuela
        </h1>
        <p className="text-body text-muted-foreground max-w-2xl mx-auto">
          Documentación y ejemplos de uso de las clases tipográficas semánticas implementadas en el frontend.
        </p>
      </header>

      {/* Display Styles */}
      <section>
        <h2 className="text-heading-large mb-6">Estilos Display</h2>
        <div className="space-y-4">
          <div className="border rounded-lg p-4">
            <div className="text-display-large">Display Large</div>
            <code className="text-code-inline">text-display-large</code>
          </div>
          <div className="border rounded-lg p-4">
            <div className="text-display">Display Medium</div>
            <code className="text-code-inline">text-display</code>
          </div>
        </div>
      </section>

      {/* Heading Styles */}
      <section>
        <h2 className="text-heading-large mb-6">Estilos de Títulos</h2>
        <div className="space-y-4">
          <div className="border rounded-lg p-4">
            <div className="text-heading-large">Heading Large</div>
            <code className="text-code-inline">text-heading-large</code>
          </div>
          <div className="border rounded-lg p-4">
            <div className="text-heading">Heading Medium</div>
            <code className="text-code-inline">text-heading</code>
          </div>
          <div className="border rounded-lg p-4">
            <div className="text-subheading">Subheading</div>
            <code className="text-code-inline">text-subheading</code>
          </div>
        </div>
      </section>

      {/* Body Styles */}
      <section>
        <h2 className="text-heading-large mb-6">Estilos de Texto</h2>
        <div className="space-y-4">
          <div className="border rounded-lg p-4">
            <div className="text-body-large">
              Texto de cuerpo grande. Lorem ipsum dolor sit amet, consectetur adipiscing elit. 
              Sed do eiusmod tempor incididunt ut labore et dolore magna aliqua.
            </div>
            <code className="text-code-inline">text-body-large</code>
          </div>
          <div className="border rounded-lg p-4">
            <div className="text-body">
              Texto de cuerpo estándar. Lorem ipsum dolor sit amet, consectetur adipiscing elit. 
              Sed do eiusmod tempor incididunt ut labore et dolore magna aliqua.
            </div>
            <code className="text-code-inline">text-body</code>
          </div>
        </div>
      </section>

      {/* Caption and Utility Styles */}
      <section>
        <h2 className="text-heading-large mb-6">Estilos Auxiliares</h2>
        <div className="space-y-4">
          <div className="border rounded-lg p-4">
            <div className="text-caption-large">Caption Large</div>
            <code className="text-code-inline">text-caption-large</code>
          </div>
          <div className="border rounded-lg p-4">
            <div className="text-caption">Caption Standard</div>
            <code className="text-code-inline">text-caption</code>
          </div>
          <div className="border rounded-lg p-4">
            <div className="text-overline">Overline Text</div>
            <code className="text-code-inline">text-overline</code>
          </div>
        </div>
      </section>

      {/* Metric Styles */}
      <section>
        <h2 className="text-heading-large mb-6">Estilos para Métricas</h2>
        <div className="space-y-4">
          <div className="border rounded-lg p-4">
            <div className="text-metric-large text-blue-600">1,234,567</div>
            <code className="text-code-inline">text-metric-large</code>
          </div>
          <div className="border rounded-lg p-4">
            <div className="text-metric text-green-600">89,123</div>
            <code className="text-code-inline">text-metric</code>
          </div>
          <div className="border rounded-lg p-4">
            <div className="text-metric-small text-purple-600">456</div>
            <code className="text-code-inline">text-metric-small</code>
          </div>
        </div>
      </section>

      {/* Code Styles */}
      <section>
        <h2 className="text-heading-large mb-6">Estilos de Código</h2>
        <div className="space-y-4">
          <div className="border rounded-lg p-4">
            <div className="text-code">
              const apiKey = "ryu_test_1234567890abcdef";
            </div>
            <code className="text-code-inline">text-code</code>
          </div>
          <div className="border rounded-lg p-4">
            <p className="text-body">
              Para configurar tu API key usa: <span className="text-code-inline">ryu_live_abc123</span>
            </p>
            <code className="text-code-inline">text-code-inline</code>
          </div>
        </div>
      </section>

      {/* Prose Example */}
      <section>
        <h2 className="text-heading-large mb-6">Texto Largo (Prose)</h2>
        <div className="border rounded-lg p-6">
          <div className="text-prose">
            <h1>Título Principal</h1>
            <p>
              Este es un ejemplo de texto largo utilizando la clase <code>text-prose</code>. 
              Esta clase está diseñada para contenido como documentación, artículos de blog, 
              o páginas legales donde la legibilidad es fundamental.
            </p>
            
            <h2>Subtítulo Secundario</h2>
            <p>
              El interlineado está optimizado para facilitar la lectura, y el ancho máximo 
              se limita para evitar líneas demasiado largas que dificulten el seguimiento visual.
            </p>
            
            <h3>Lista de características</h3>
            <ul>
              <li>Interlineado optimizado (leading-relaxed)</li>
              <li>Ancho máximo controlado (max-w-prose)</li>
              <li>Espaciado vertical coherente</li>
              <li>Jerarquía tipográfica automática</li>
            </ul>
          </div>
          <code className="text-code-inline mt-4 block">text-prose</code>
        </div>
      </section>

      {/* Interactive Example */}
      <section>
        <h2 className="text-heading-large mb-6">Elementos Interactivos</h2>
        <div className="space-y-4">
          <div className="border rounded-lg p-4">
            <a href="#" className="text-link">Enlace con estados de hover</a>
            <code className="text-code-inline ml-4">text-link</code>
          </div>
        </div>
      </section>

      <footer className="text-center pt-8 border-t">
        <p className="text-caption">
          Sistema tipográfico implementado con Tailwind CSS y clases semánticas personalizadas
        </p>
      </footer>
    </div>
  );
} 
