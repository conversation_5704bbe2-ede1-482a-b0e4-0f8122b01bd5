// src/app/(dashboard)/layout.tsx
"use client"; // Marcamos como Client Component para usar hooks (useEffect, etc.)

import React, { useEffect, useState } from 'react';
import { useRouter } from 'next/navigation'; // Importa desde 'next/navigation'
import { useAuth } from '@/lib/auth'; // Importaremos esto luego
import Header from '@/components/dashboard/Header'; // Crearemos estos componentes
import Sidebar from '@/components/dashboard/Sidebar';
import EmailVerificationBanner from '@/components/dashboard/EmailVerificationBanner';

export default function DashboardLayout({
  children,
}: {
  children: React.ReactNode;
}) {
  const { user, isLoading } = useAuth(); // Usaremos un hook de autenticación
  const router = useRouter();
  const [showEmailBanner, setShowEmailBanner] = useState(false);

  useEffect(() => {
    // Si la carga de autenticación ha terminado y no hay usuario, redirige a login
    if (!isLoading && !user) {
      router.push('/login');
    }
  }, [user, isLoading, router]);

  // Verificar si el usuario tiene el email verificado
  useEffect(() => {
    if (user) {
      // Verificar si el usuario tiene el email verificado
      // Esto dependerá de cómo esté estructurado tu objeto user
      // Aquí asumimos que hay una propiedad email_verified
      setShowEmailBanner((user as { email_verified?: boolean }).email_verified === false);
    }
  }, [user]);

  // Muestra un loader mientras se verifica la autenticación
  if (isLoading) {
    return <div>Cargando...</div>; // O un componente Spinner más elegante
  }

  // Si no hay usuario (aunque el redirect debería haber funcionado), no renderiza el contenido
  if (!user) {
    return null;
  }

  // Si el usuario está autenticado, muestra el layout del dashboard
  return (
    <div className="flex h-screen bg-background">
      <Sidebar />
      <div className="flex-1 flex flex-col overflow-hidden">
        <Header />
        <main className="flex-1 overflow-x-hidden overflow-y-auto bg-muted/30 p-6">
          {/* Mostrar el banner de verificación de email si es necesario */}
          {showEmailBanner && <EmailVerificationBanner onClose={() => setShowEmailBanner(false)} />}
          {children}
        </main>
      </div>
    </div>
  );
}
