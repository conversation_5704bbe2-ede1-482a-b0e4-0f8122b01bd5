"use client";

import { useState, useEffect } from 'react';
import { format, parseISO, subDays, formatDistanceToNow } from 'date-fns';
import { es } from 'date-fns/locale';
import {
  useAuth,
  usePlans,
  useAccountInfo,
  useUsageSummary,
  useUsageHistory
} from '@/lib/hooks';
import {
  AccountInfo,
  UsageHistoryPoint
} from '@/lib/api';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card";
import { Skeleton } from '@/components/ui/skeleton';
import { Button } from '@/components/ui/button';
import { Progress } from '@/components/ui/progress';
import { Badge } from '@/components/ui/badge';
import { Alert, AlertTitle, AlertDescription } from "@/components/ui/alert";
import { toast } from 'sonner';
import { handleApiError } from '@/lib/error-handler';
import {
  BarChart3Icon,
  DatabaseIcon,
  RefreshCwIcon,
  ClockIcon,
  TrendingUpIcon,
  InfoIcon,
  BrainIcon,
  ArrowUpIcon,
  AlertCircleIcon,
  HelpCircleIcon
} from 'lucide-react';
import { SemanticIcon } from '@/components/ui/icon';
import { BillingButton } from '@/components/dashboard/BillingButton';
import { BillingPortalButton } from '@/components/dashboard/BillingPortalButton';
import UsageChart from '@/components/dashboard/UsageChart';
import { DateRangeSelector, DateRange } from '@/components/dashboard/DateRangeSelector';
import {
  Tooltip,
  TooltipContent,
  TooltipProvider,
  TooltipTrigger
} from "@/components/ui/tooltip";

// Función para formatear bytes a una unidad legible
function formatBytes(bytes: number, decimals = 2): string {
  if (bytes === 0) return '0 Bytes';

  const k = 1024;
  const dm = decimals < 0 ? 0 : decimals;
  const sizes = ['Bytes', 'KB', 'MB', 'GB', 'TB', 'PB', 'EB', 'ZB', 'YB'];

  const i = Math.floor(Math.log(bytes) / Math.log(k));

  return parseFloat((bytes / Math.pow(k, i)).toFixed(dm)) + ' ' + sizes[i];
}

// Función para formatear fechas
function formatDate(dateString: string | null): string {
  if (!dateString) return 'No disponible';
  try {
    return format(parseISO(dateString), "d 'de' MMMM 'de' yyyy, HH:mm", { locale: es });
  } catch (error) {
    console.error("Error al formatear fecha:", error);
    return 'Formato de fecha inválido';
  }
}

// Función para formatear números grandes
function formatNumber(num: number): string {
  return new Intl.NumberFormat().format(num);
}

// Interfaz para los datos de uso históricos
interface UsageDataPoint {
  date: string;
  apiCalls: number;
  storage: number;
}

export default function UsageDashboard() {
  const { token, apiKey } = useAuth();
  const [isRefreshing, setIsRefreshing] = useState(false);
  const [filteredData, setFilteredData] = useState<UsageDataPoint[]>([]);
  const [dateRange, setDateRange] = useState<DateRange>({
    from: subDays(new Date(), 30),
    to: new Date()
  });

  // Usar los hooks personalizados para obtener datos
  const { plans, isLoading: plansLoading, error: plansError, getPlanById } = usePlans();

  // Obtener información de la cuenta
  const {
    accountData,
    error: accountError,
    isLoading: isAccountLoading,
  } = useAccountInfo();

  // Obtener datos de uso
  const {
    usageData: usageSummary,
    error: usageError,
    isLoading: isUsageLoading,
    isValidating: isValidatingUsage,
    refresh: mutateUsage,
    lastUpdated: usageUpdatedAt
  } = useUsageSummary({
    revalidateOnFocus: false,
    dedupingInterval: 60000, // 1 minuto
    errorRetryCount: 3
  });

  // Obtener historial de uso
  const {
    data: historyData,
    error: historyError,
    isLoading: isHistoryLoading,
    isValidating: isValidatingHistory,
    mutate: mutateHistory,
    dataUpdatedAt: historyUpdatedAt
  } = useUsageHistory(
    dateRange.from,
    dateRange.to,
    {
      revalidateOnFocus: false,
      dedupingInterval: 60000, // 1 minuto
      errorRetryCount: 3
    }
  );

  // Convertir los datos históricos al formato esperado por el componente UsageChart
  useEffect(() => {
    if (historyData) {
      const chartData: UsageDataPoint[] = historyData.map(item => ({
        date: item.date,
        apiCalls: item.api_calls,
        storage: item.storage
      }));
      setFilteredData(chartData);
    }
  }, [historyData]);

  // Función para obtener la última actualización de datos
  const getLastUpdateTime = (): string => {
    return 'Hace unos momentos';
  };

  // Función para refrescar los datos
  const handleRefresh = async () => {
    setIsRefreshing(true);
    try {
      await Promise.all([mutateUsage(), mutateHistory()]);
      toast.success('Datos de uso actualizados');
    } catch (error: any) {
      handleApiError(error, 'Error al actualizar los datos de uso');
    } finally {
      setIsRefreshing(false);
    }
  };

  // Función para manejar el cambio de rango de fechas
  const handleDateRangeChange = (range: DateRange) => {
    setDateRange(range);
  };

  // Calcular totales para el rango seleccionado
  const calculateTotals = () => {
    if (!filteredData.length) return { apiCalls: 0, storage: 0 };

    // Para las llamadas a la API, sumamos todas las llamadas en el rango
    const totalApiCalls = filteredData.reduce((sum, item) => sum + item.apiCalls, 0);

    // Para el almacenamiento, tomamos el valor más reciente
    const latestStorage = filteredData[filteredData.length - 1]?.storage || 0;

    return { apiCalls: totalApiCalls, storage: latestStorage };
  };

  const totals = calculateTotals();

  // Estado de carga combinado
  const isLoading = isUsageLoading || isAccountLoading || isHistoryLoading;

  // Mostrar un mensaje de carga mientras se obtienen los datos
  if (isLoading) {
    return (
      <div className="container mx-auto py-8">
        <div className="flex justify-between items-center mb-6">
          <h1 className="text-display">Uso de API</h1>
          <Skeleton className="h-9 w-24" />
        </div>

        {/* Skeletons para las tarjetas de métricas */}
        <div className="grid grid-cols-1 md:grid-cols-3 gap-6 mb-8">
          {[1, 2, 3].map((i) => (
            <Card key={i} className="transition-all duration-300 hover:shadow-md">
              <CardHeader className="pb-2">
                <CardTitle className="flex items-center text-lg">
                  <Skeleton className="h-5 w-5 mr-2 rounded-md" />
                  <Skeleton className="h-6 w-3/4" />
                </CardTitle>
                <CardDescription>
                  <Skeleton className="h-4 w-1/2" />
                </CardDescription>
              </CardHeader>
              <CardContent>
                <Skeleton className="h-10 w-1/2 mx-auto" />
              </CardContent>
            </Card>
          ))}
        </div>

        {/* Skeletons para el gráfico */}
        <Card className="mb-8">
          <CardHeader>
            <CardTitle><Skeleton className="h-6 w-1/3" /></CardTitle>
            <CardDescription><Skeleton className="h-4 w-1/2" /></CardDescription>
          </CardHeader>
          <CardContent>
            <Skeleton className="h-64 w-full rounded-lg" />
          </CardContent>
        </Card>
      </div>
    );
  }

  // Función para determinar si hay errores críticos que impiden mostrar datos útiles
  const hasCriticalErrors = (): boolean => {
    // Si no tenemos datos de cuenta o de uso, consideramos que es un error crítico
    return (!!accountError && !accountData) || (!!usageError && !usageSummary);
  };

  // Mostrar un mensaje de error si ocurre algún problema crítico
  if (hasCriticalErrors()) {
    return (
      <div className="container mx-auto py-8">
        <div className="flex justify-between items-center mb-6">
          <h1 className="text-display">Uso de API</h1>
          <div className="flex items-center gap-2">
            <Button
              onClick={handleRefresh}
              variant="outline"
              size="sm"
              disabled={isRefreshing}
              className="transition-all duration-200 hover:bg-red-50 hover:text-red-600 hover:border-red-200 focus:ring-2 focus:ring-red-200 focus:ring-offset-2"
            >
              {isRefreshing ? (
                <>
                  <SemanticIcon icon={RefreshCwIcon} size="sm" context="muted" className="mr-2 animate-spin" />
                  Actualizando...
                </>
              ) : (
                <>
                  <SemanticIcon icon={RefreshCwIcon} size="sm" context="muted" className="mr-2" />
                  Reintentar
                </>
              )}
            </Button>
          </div>
        </div>

        <Alert variant="destructive" className="mb-8">
          <SemanticIcon icon={AlertCircleIcon} size="sm" context="error" />
          <AlertTitle>Error al cargar los datos de uso</AlertTitle>
          <AlertDescription>
            <p className="mb-2">No se pudieron obtener las métricas de uso de la API. Por favor, intenta de nuevo más tarde.</p>
            <div className="mt-2 p-3 bg-red-50 dark:bg-red-900/20 rounded border border-red-200 dark:border-red-800 text-sm">
              <strong>Detalles del error:</strong><br />
              {accountError && <p>Error de cuenta: {accountError.message}</p>}
              {usageError && <p>Error de uso: {usageError.message}</p>}
              {historyError && <p>Error de historial: {historyError.message}</p>}
              {!accountError && !usageError && !historyError && <p>Ocurrió un error desconocido</p>}
            </div>
          </AlertDescription>
        </Alert>

        {/* Aún mostramos el gráfico con datos de ejemplo */}
        <UsageChart
          error={new Error("No se pudieron cargar los datos reales. Mostrando datos de ejemplo.")}
          title="Historial de Uso (Datos de Ejemplo)"
          description="Los datos mostrados son ejemplos y no reflejan el uso real"
        />
      </div>
    );
  }

  return (
    <div className="container mx-auto py-8">
      {/* Encabezado con información del plan y botones de facturación */}
      <Card className="mb-8 transition-all duration-300 hover:shadow-md">
        <CardHeader className="pb-2">
          <div className="flex flex-col md:flex-row justify-between items-start md:items-center">
            <div>
              <CardTitle className="flex items-center text-lg">
                <SemanticIcon icon={InfoIcon} size="md" context="info" className="mr-2" />
                Plan Actual: {' '}
                <Badge variant="default" className="ml-2 text-sm">
                  {(() => {
                    // Intentamos obtener el plan primero desde accountData, luego desde usageSummary
                    const planType = accountData?.subscription?.plan ||
                      (usageSummary ? usageSummary.subscription.plan : null);

                    return plans && planType ? plans[planType]?.name || planType : 'Desconocido';
                  })()}
                </Badge>
              </CardTitle>
              <CardDescription>
                Estado: {usageSummary && usageSummary.subscription.is_active ?
                  <span className="text-success">Activo</span> :
                  <span className="text-destructive">Inactivo</span>}
              </CardDescription>
            </div>
            <div className="flex flex-col sm:flex-row gap-2 mt-4 md:mt-0">
              <BillingPortalButton
                className="transition-all duration-300 hover:bg-blue-50 hover:text-blue-600 hover:border-blue-200 focus:ring-2 focus:ring-blue-200 focus:ring-offset-2"
              />
              {usageSummary && usageSummary.billing && usageSummary.billing.upgrade_available && (
                <BillingButton
                  priceId={plans && plans["PRO"]?.mercadopago_price_id || ""}
                  planName="Pro"
                  variant="default"
                  className="transition-all duration-300 hover:shadow-md focus:ring-2 focus:ring-blue-200 focus:ring-offset-2"
                >
                  <SemanticIcon icon={ArrowUpIcon} size="sm" context="primary" className="mr-1" />
                  Mejorar Plan
                </BillingButton>
              )}
            </div>
          </div>
        </CardHeader>
      </Card>

      {/* Mostrar alertas para errores no críticos */}
      {(accountError || usageError || historyError) && !hasCriticalErrors() && (
        <Alert variant="destructive" className="mb-6">
          <SemanticIcon icon={AlertCircleIcon} size="sm" context="error" />
          <AlertTitle>Algunos datos no pudieron cargarse correctamente</AlertTitle>
          <AlertDescription>
            <p>Estamos mostrando la información disponible, pero algunos datos podrían estar incompletos o desactualizados.</p>
            <Button
              onClick={handleRefresh}
              variant="outline"
              size="sm"
              className="mt-2"
              disabled={isRefreshing}
            >
              {isRefreshing ? "Actualizando..." : "Reintentar"}
            </Button>
          </AlertDescription>
        </Alert>
      )}

      <div className="flex flex-col md:flex-row justify-between items-start md:items-center gap-4 mb-6">
        <h1 className="text-display">Uso de API</h1>
        <div className="flex flex-col sm:flex-row gap-2 w-full md:w-auto">
          <div className="flex items-center gap-2 text-caption mr-2">
            <SemanticIcon icon={ClockIcon} size="xs" context="muted" />
            <span>
              Actualizado: {getLastUpdateTime()}
            </span>
          </div>
          <DateRangeSelector
            onChange={handleDateRangeChange}
            className="flex-1"
          />
          <Button
            onClick={handleRefresh}
            variant="outline"
            size="sm"
            disabled={isRefreshing}
            className="transition-all duration-200 hover:bg-blue-50 hover:text-blue-600 hover:border-blue-200 focus:ring-2 focus:ring-blue-200 focus:ring-offset-2"
          >
            {isRefreshing ? (
              <>
                <SemanticIcon icon={RefreshCwIcon} size="sm" context="muted" className="mr-2 animate-spin" />
                Actualizando...
              </>
            ) : (
              <>
                <SemanticIcon icon={RefreshCwIcon} size="sm" context="muted" className="mr-2" />
                Actualizar
              </>
            )}
          </Button>
        </div>
      </div>

      <div className="grid grid-cols-1 md:grid-cols-3 gap-6 mb-8">
        {/* Tarjeta de Llamadas a la API */}
        <Card className="transition-all duration-300 hover:shadow-md border-2 hover:border-blue-100 dark:hover:border-blue-900">
          <CardHeader className="pb-2">
            <CardTitle className="flex items-center text-lg">
              <SemanticIcon icon={BarChart3Icon} size="md" context="primary" className="mr-2" />
              Llamadas a la API
              {usageSummary && usageSummary.subscription && usageSummary.subscription.plan && plans && plans[usageSummary.subscription.plan]?.limits.api_calls && (
                <TooltipProvider>
                  <Tooltip>
                    <TooltipTrigger asChild>
                      <Badge variant="outline" className="ml-2 cursor-help">
                        Límite: {formatNumber(plans[usageSummary.subscription.plan]?.limits.api_calls || 0)}
                      </Badge>
                    </TooltipTrigger>
                    <TooltipContent>
                      <p className="text-xs">Límite máximo de llamadas a la API según tu plan actual</p>
                    </TooltipContent>
                  </Tooltip>
                </TooltipProvider>
              )}
            </CardTitle>
            <CardDescription>
              {dateRange.from && dateRange.to ? (
                <>Solicitudes entre {format(dateRange.from, 'dd/MM/yy')} y {format(dateRange.to, 'dd/MM/yy')}</>
              ) : (
                'Total de solicitudes realizadas'
              )}
            </CardDescription>
          </CardHeader>
          <CardContent>
            <div className="text-metric text-center py-2 text-blue-600 dark:text-blue-400">
              {totals.apiCalls.toLocaleString()}
            </div>
            {usageSummary && usageSummary.subscription && usageSummary.subscription.plan && plans && (
              <div className="mt-2">
                <div className="flex justify-between text-xs text-muted-foreground mb-1">
                  <span>Total del periodo</span>
                  <div className="flex items-center">
                    <span className="font-medium">{totals.apiCalls.toLocaleString()}</span>
                    {plans && plans[usageSummary.subscription.plan]?.limits.api_calls && (
                      <>
                        <span className="mx-1">/</span>
                        <span>{formatNumber(plans[usageSummary.subscription.plan]?.limits.api_calls)}</span>
                      </>
                    )}
                    {(!plans || !plans[usageSummary.subscription.plan]?.limits.api_calls) && (
                      <span className="ml-1">/ ∞</span>
                    )}
                  </div>
                </div>
                <Progress
                  value={(() => {
                    if (!plans || !usageSummary || !usageSummary.subscription.plan) return 0;
                    const limit = plans[usageSummary.subscription.plan]?.limits.api_calls;
                    if (!limit || limit <= 0) return 0;
                    return Math.min((totals.apiCalls / limit) * 100, 100);
                  })()}
                  className="h-1.5"
                />
                <div className="flex justify-between text-xs text-muted-foreground mt-2">
                  <span>Ciclo actual</span>
                  <div className="flex items-center">
                    <span className="font-medium">{formatNumber(usageSummary.api_calls.used)}</span>
                    {usageSummary.api_calls.limit > 0 && (
                      <>
                        <span className="mx-1">/</span>
                        <span>{formatNumber(usageSummary.api_calls.limit)}</span>
                      </>
                    )}
                    {usageSummary.api_calls.limit <= 0 && (
                      <span className="ml-1">/ ∞</span>
                    )}
                  </div>
                </div>
                <Progress
                  value={(() => {
                    if (!usageSummary.api_calls.limit || usageSummary.api_calls.limit <= 0) return 0;
                    return Math.min((usageSummary.api_calls.used / usageSummary.api_calls.limit) * 100, 100);
                  })()}
                  className="h-1.5"
                />
                <div className="text-xs text-muted-foreground mt-2 text-right">
                  Próximo reinicio: {formatDate(usageSummary.api_calls.next_reset)}
                </div>
              </div>
            )}
          </CardContent>
        </Card>

        {/* Tarjeta de Almacenamiento Utilizado */}
        <Card className="transition-all duration-300 hover:shadow-md border-2 hover:border-green-100 dark:hover:border-green-900">
          <CardHeader className="pb-2">
            <CardTitle className="flex items-center text-lg">
              <SemanticIcon icon={DatabaseIcon} size="md" context="success" className="mr-2" />
              Almacenamiento
              {usageSummary && usageSummary.subscription && usageSummary.subscription.plan && plans && plans[usageSummary.subscription.plan]?.limits.storage_bytes && (
                <TooltipProvider>
                  <Tooltip>
                    <TooltipTrigger asChild>
                      <Badge variant="outline" className="ml-2 cursor-help">
                        Límite: {formatBytes(plans[usageSummary.subscription.plan]?.limits.storage_bytes || 0)}
                      </Badge>
                    </TooltipTrigger>
                    <TooltipContent>
                      <p className="text-xs">Límite máximo de almacenamiento según tu plan actual</p>
                    </TooltipContent>
                  </Tooltip>
                </TooltipProvider>
              )}
            </CardTitle>
            <CardDescription>
              Espacio utilizado al {dateRange.to ? format(dateRange.to, 'dd/MM/yy') : 'final del periodo'}
            </CardDescription>
          </CardHeader>
          <CardContent>
            <div className="text-metric text-center py-2 text-green-600 dark:text-green-400">
              {formatBytes(usageSummary.storage.used_bytes || 0)}
            </div>
            {usageSummary && usageSummary.subscription && usageSummary.subscription.plan && plans && (
              <div className="mt-2">
                <div className="flex justify-between text-xs text-muted-foreground mb-1">
                  <span>Uso actual</span>
                  <div className="flex items-center">
                    <span className="font-medium">{formatBytes(usageSummary.storage.used_bytes)}</span>
                    {plans && plans[usageSummary.subscription.plan]?.limits.storage_bytes > 0 && (
                      <>
                        <span className="mx-1">/</span>
                        <span>{formatBytes(plans[usageSummary.subscription.plan]?.limits.storage_bytes)}</span>
                      </>
                    )}
                    {(!plans || !plans[usageSummary.subscription.plan]?.limits.storage_bytes || plans[usageSummary.subscription.plan]?.limits.storage_bytes <= 0) && (
                      <span className="ml-1">/ ∞</span>
                    )}
                  </div>
                </div>
                <Progress
                  value={(() => {
                    if (!plans || !usageSummary || !usageSummary.subscription.plan) return 0;
                    const limit = plans[usageSummary.subscription.plan]?.limits.storage_bytes;
                    if (!limit || limit <= 0) return 0;
                    return Math.min((usageSummary.storage.used_bytes / limit) * 100, 100);
                  })()}
                  className="h-1.5"
                />
                <div className="flex justify-between items-center text-xs text-muted-foreground mt-2">
                  <div className="flex items-center">
                    <SemanticIcon icon={ClockIcon} size="xs" context="muted" className="mr-1" />
                    <span>Última medición:</span>
                  </div>
                  <span className="font-medium">{usageSummary.storage.last_measured ? format(parseISO(usageSummary.storage.last_measured), 'dd/MM/yy HH:mm') : 'No disponible'}</span>
                </div>
              </div>
            )}
          </CardContent>
        </Card>

        {/* Tarjeta de Entrenamiento */}
        <Card className="transition-all duration-300 hover:shadow-md border-2 hover:border-purple-100 dark:hover:border-purple-900">
          <CardHeader className="pb-2">
            <CardTitle className="flex items-center text-lg">
              <SemanticIcon icon={BrainIcon} size="md" context="primary" className="mr-2" />
              Entrenamiento
              {usageSummary && usageSummary.subscription && usageSummary.subscription.plan && plans && plans[usageSummary.subscription.plan]?.limits.training_frequency && (
                <TooltipProvider>
                  <Tooltip>
                    <TooltipTrigger asChild>
                      <Badge variant="outline" className="ml-2 cursor-help">
                        {plans[usageSummary.subscription.plan]?.limits.training_frequency}
                      </Badge>
                    </TooltipTrigger>
                    <TooltipContent>
                      <p className="text-xs">Frecuencia de entrenamiento permitida según tu plan</p>
                    </TooltipContent>
                  </Tooltip>
                </TooltipProvider>
              )}
            </CardTitle>
            <CardDescription>
              Información sobre el entrenamiento del modelo
            </CardDescription>
          </CardHeader>
          <CardContent>
            {usageSummary && usageSummary.training && (
              <>
                <div className="flex flex-col space-y-3 py-2">
                  <div className="flex justify-between items-center">
                    <div className="flex items-center text-sm">
                      <SemanticIcon icon={ClockIcon} size="sm" context="primary" className="mr-1.5" />
                      <span className="text-muted-foreground">Último entrenamiento:</span>
                    </div>
                    <span className="font-medium text-sm">
                      {usageSummary.training.last_training_date ?
                        format(parseISO(usageSummary.training.last_training_date), 'dd/MM/yy HH:mm') :
                        'Nunca'}
                    </span>
                  </div>

                  <div className="flex justify-between items-center">
                    <div className="flex items-center text-sm">
                      <SemanticIcon icon={TrendingUpIcon} size="sm" context="primary" className="mr-1.5" />
                      <span className="text-muted-foreground">Frecuencia:</span>
                    </div>
                    <div className="flex items-center">
                      <span className="font-medium text-sm">{usageSummary.training.frequency_limit || 'Manual'}</span>
                      <TooltipProvider>
                        <Tooltip>
                          <TooltipTrigger asChild>
                            <SemanticIcon icon={HelpCircleIcon} size="xs" context="muted" className="ml-1 cursor-help" />
                          </TooltipTrigger>
                          <TooltipContent>
                            <p className="text-xs">Frecuencia con la que puedes entrenar tu modelo</p>
                          </TooltipContent>
                        </Tooltip>
                      </TooltipProvider>
                    </div>
                  </div>

                  <div className="flex justify-between items-center">
                    <div className="flex items-center text-sm">
                      <SemanticIcon icon={ClockIcon} size="sm" context="primary" className="mr-1.5" />
                      <span className="text-muted-foreground">Próximo disponible:</span>
                    </div>
                    <span className="font-medium text-sm">
                      {usageSummary.training.next_training_available ?
                        format(parseISO(usageSummary.training.next_training_available), 'dd/MM/yy HH:mm') :
                        'Ahora'}
                    </span>
                  </div>

                  <div className="flex justify-between items-center">
                    <div className="flex items-center text-sm">
                      <SemanticIcon icon={DatabaseIcon} size="sm" context="primary" className="mr-1.5" />
                      <span className="text-muted-foreground">Límite de datos:</span>
                    </div>
                    <div className="flex items-center">
                      <span className="font-medium text-sm">
                        {usageSummary.training.training_data_limit_formatted || 'Ilimitado'}
                      </span>
                      <TooltipProvider>
                        <Tooltip>
                          <TooltipTrigger asChild>
                            <SemanticIcon icon={HelpCircleIcon} size="xs" context="muted" className="ml-1 cursor-help" />
                          </TooltipTrigger>
                          <TooltipContent>
                            <p className="text-xs">Cantidad máxima de datos que puedes usar para entrenar</p>
                          </TooltipContent>
                        </Tooltip>
                      </TooltipProvider>
                    </div>
                  </div>

                  <div className="flex justify-between items-center">
                    <div className="flex items-center text-sm">
                      <SemanticIcon icon={InfoIcon} size="sm" context="primary" className="mr-1.5" />
                      <span className="text-muted-foreground">Estado del modelo:</span>
                    </div>
                    <Badge
                      variant={usageSummary.training.model_status === 'ready' ? 'default' : 'secondary'}
                      className="text-xs"
                    >
                      {usageSummary.training.model_status === 'ready' ? 'Listo' :
                        usageSummary.training.model_status === 'training' ? 'Entrenando' :
                          usageSummary.training.model_status === 'error' ? 'Error' : 'Pendiente'}
                    </Badge>
                  </div>
                </div>
              </>
            )}

            {(!usageSummary || !usageSummary.training) && (
              <div className="flex flex-col items-center justify-center py-8 text-center">
                <SemanticIcon icon={InfoIcon} size="2xl" context="muted" className="mb-2" />
                <p className="text-muted-foreground font-medium">No hay datos disponibles</p>
                <p className="text-sm text-muted-foreground text-center max-w-md">
                  Los datos de entrenamiento se mostrarán aquí una vez que hayas configurado tu modelo.
                </p>
              </div>
            )}
          </CardContent>
        </Card>
      </div>

      {/* Mostrar componente de gráfico */}
      <Card className="mb-8">
        <CardHeader>
          <CardTitle className="flex items-center">
            <SemanticIcon icon={TrendingUpIcon} size="md" context="primary" className="mr-2" />
            Historial de Uso
          </CardTitle>
          <CardDescription>
            Tendencias de uso de la API durante el periodo seleccionado
          </CardDescription>
        </CardHeader>
        <CardContent>
          {/* Mensaje informativo */}
          <div className="mb-4 p-3 bg-blue-50 dark:bg-blue-900/20 rounded-lg border border-blue-200 dark:border-blue-800">
            <p className="text-sm flex items-start">
              <SemanticIcon icon={InfoIcon} size="md" context="info" className="mr-2 shrink-0 mt-0.5" />
              <span>
                El gráfico muestra las tendencias de uso de tu API durante el periodo seleccionado.
                Los datos se actualizan automáticamente y reflejan las llamadas realizadas y el almacenamiento utilizado.
              </span>
            </p>
          </div>

          <UsageChart
            data={filteredData}
            dateRange={dateRange}
            isLoading={isValidatingHistory}
            error={historyError}
            showDateRange={false}
          />
        </CardContent>
      </Card>

      {/* Información adicional y consejos */}
      <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
        <Card className="transition-all duration-300 hover:shadow-md">
          <CardHeader>
            <CardTitle className="flex items-center text-lg">
              <SemanticIcon icon={TrendingUpIcon} size="md" context="primary" className="mr-2" />
              Optimización del Uso
            </CardTitle>
          </CardHeader>
          <CardContent>
            <div className="space-y-3">
              <div className="flex items-start gap-3">
                <div className="mt-1">
                  {totals.apiCalls > 1000 ? (
                    <SemanticIcon icon={TrendingUpIcon} size="md" context="success" />
                  ) : (
                    <SemanticIcon icon={TrendingUpIcon} size="md" context="primary" />
                  )}
                </div>
                <div>
                  <h4 className="font-medium">
                    {(() => {
                      const { apiCalls } = totals;
                      const isIncreasing = apiCalls > 500; // Umbral arbitrario

                      return isIncreasing ? "Uso Creciente" : "Desarrollo Inicial";
                    })()}
                  </h4>
                  <p className="text-sm text-muted-foreground">
                    {(() => {
                      const { apiCalls } = totals;
                      const isIncreasing = apiCalls > 500;

                      return isIncreasing ?
                        "Tu uso de API está creciendo. Considera optimizar las consultas para mejorar el rendimiento." :
                        "Estás en las primeras etapas de desarrollo. Experimenta y explora las funcionalidades disponibles.";
                    })()}
                  </p>
                </div>
              </div>
            </div>
          </CardContent>
        </Card>

        <Card className="transition-all duration-300 hover:shadow-md">
          <CardHeader>
            <CardTitle className="flex items-center text-lg">
              <SemanticIcon icon={InfoIcon} size="md" context="info" className="mr-2" />
              Recursos y Ayuda
            </CardTitle>
          </CardHeader>
          <CardContent>
            <div className="space-y-3">
              <p className="text-sm text-muted-foreground">
                ¿Necesitas ayuda para optimizar tu uso o tienes preguntas sobre los límites de tu plan?
              </p>
              <div className="flex flex-col gap-2">
                <Button variant="outline" size="sm" asChild>
                  <a href="https://docs.rayuela.ai" target="_blank" rel="noopener noreferrer">
                    <SemanticIcon icon={InfoIcon} size="sm" context="primary" className="mr-2" />
                    Ver Documentación
                  </a>
                </Button>
              </div>
            </div>
          </CardContent>
        </Card>
      </div>
    </div>
  );
}
