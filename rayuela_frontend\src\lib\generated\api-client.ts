import axios, { AxiosError, AxiosRequestConfig, AxiosResponse } from "axios";
// import { toast } from 'sonner'; // Comentado porque no se usa actualmente

// Interfaces de error
export interface ApiErrorDetails {
  field?: string;
  message: string;
  code?: string;
}

export interface ApiErrorResponse {
  message: string;
  error_code: string;
  details?: ApiErrorDetails[];
  status_code: number;
}

export class ApiError extends Error {
  constructor(
    message: string,
    public status: number,
    public errorCode: string,
    public details?: ApiErrorDetails[] | null,
  ) {
    super(message);
    this.name = "ApiError";
  }

  static isApiError(error: unknown): error is ApiError {
    return error instanceof ApiError;
  }

  static fromResponse(response: ApiErrorResponse): ApiError {
    return new ApiError(
      response.message,
      response.status_code,
      response.error_code,
      response.details,
    );
  }
}

// Obtén la URL base de las variables de entorno
const API_BASE_URL =
  process.env.NEXT_PUBLIC_API_URL || "http://localhost:8001/api/v1";

// Cliente Axios personalizado
export const customInstance = axios.create({
  baseURL: API_BASE_URL,
  headers: {
    "Content-Type": "application/json",
  },
});

// Interfaz para extender las opciones de solicitud con token y apiKey
export interface CustomRequestConfig extends AxiosRequestConfig {
  token?: string | null;
  apiKey?: string | null;
}

// Interceptor de solicitud para añadir cabeceras de autenticación
customInstance.interceptors.request.use((config) => {
  const customConfig = config as CustomRequestConfig;

  if (customConfig.token) {
    config.headers["Authorization"] = `Bearer ${customConfig.token}`;
  }

  if (customConfig.apiKey && !config.url?.endsWith("/auth/token")) {
    config.headers["X-API-Key"] = customConfig.apiKey;
  }

  // Eliminar propiedades personalizadas para evitar problemas con Axios
  delete (customConfig as any).token;
  delete (customConfig as any).apiKey;

  return config;
});

// Interceptor de respuesta para manejar errores
customInstance.interceptors.response.use(
  (response: AxiosResponse) => {
    return response;
  },
  (error: AxiosError<ApiErrorResponse>) => {
    if (error.response) {
      const errorResponse = error.response.data;
      throw ApiError.fromResponse(errorResponse);
    } else if (error.request) {
      // La solicitud se realizó pero no se recibió respuesta
      throw new ApiError(
        "No se recibió respuesta del servidor",
        0,
        "NETWORK_ERROR",
        null,
      );
    } else {
      // Error al configurar la solicitud
      throw new ApiError(error.message, 0, "REQUEST_ERROR", null);
    }
  },
);

export default customInstance;
