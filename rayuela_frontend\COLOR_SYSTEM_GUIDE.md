# Sistema de Colores Unificado - Rayuela

## Descripción General

Este documento describe el sistema de colores unificado de Rayuela, basado en la paleta `oklch` que garantiza consistencia entre el modo claro y oscuro, así como entre las secciones de marketing y producto.

## Paleta Principal (oklch)

### Variables CSS Disponibles

#### Colores Base
- `--background` / `bg-background` - Fondo principal de la aplicación
- `--foreground` / `text-foreground` - Texto principal
- `--card` / `bg-card` - Fondo de tarjetas y componentes
- `--muted` / `bg-muted` - Fondos secundarios y estados deshabilitados
- `--primary` / `bg-primary` - Color primario de la marca
- `--secondary` / `bg-secondary` - Color secundario
- `--accent` / `bg-accent` - Color de acento

#### Colores Semánticos
- `--success` / `bg-success` - Estados de éxito (verde)
- `--warning` / `bg-warning` - Advertencias (ámbar)
- `--info` / `bg-info` - Información (azul)
- `--destructive` / `bg-destructive` - Errores y acciones destructivas (rojo)

#### Colores del Sidebar
- `--sidebar` / `bg-sidebar` - Fondo del sidebar
- `--sidebar-foreground` / `text-sidebar-foreground` - Texto del sidebar
- `--sidebar-accent` / `bg-sidebar-accent` - Estados hover del sidebar

#### Gradientes de Marketing
- `--marketing-gradient-start` / `from-marketing-gradient-start`
- `--marketing-gradient-end` / `to-marketing-gradient-end`
- `--marketing-gradient-accent` / `bg-marketing-gradient-accent`

## Mejores Prácticas

### ✅ Usar Variables CSS (Recomendado)
```tsx
// En lugar de esto:
<div className="bg-gray-100 dark:bg-gray-900">

// Usar esto:
<div className="bg-background">
```

### ✅ Componentes Semánticos
```tsx
// Badge con colores semánticos
<Badge variant="success">Activo</Badge>
<Badge variant="warning">Pendiente</Badge>
<Badge variant="info">Info</Badge>

// Alert con colores semánticos
<Alert variant="success">Operación exitosa</Alert>
<Alert variant="warning">Advertencia importante</Alert>
```

### ✅ Gradientes Unificados
```tsx
// Para páginas públicas (marketing)
<div className="bg-gradient-to-br from-marketing-gradient-start to-marketing-gradient-end">
```

### ❌ Evitar Clases Directas de Tailwind
```tsx
// Evitar:
<div className="bg-gray-50 dark:bg-gray-800">
<div className="bg-blue-50 text-blue-700">
<div className="from-blue-50 to-indigo-100">

// Usar en su lugar las variables CSS apropiadas
```

## Componentes Actualizados

### Badge
- `variant="success"` - Verde para estados exitosos
- `variant="warning"` - Ámbar para advertencias  
- `variant="info"` - Azul para información
- `variant="outline-success"` - Variante outline verde

### Alert
- `variant="success"` - Alertas de éxito
- `variant="warning"` - Alertas de advertencia
- `variant="info"` - Alertas informativas

## Ventajas del Sistema oklch

1. **Consistencia de Luminancia**: Colores con la misma luminancia se perciben igual de brillantes
2. **Transiciones Suaves**: Gradientes más naturales entre colores
3. **Accesibilidad**: Fácil control del contraste ajustando el valor L (luminancia)
4. **Modo Oscuro**: Transiciones automáticas y armoniosas

## Contraste y Accesibilidad

El sistema garantiza:
- Contraste mínimo AA (4.5:1) para texto normal
- Contraste AAA (7:1) para elementos críticos
- Transiciones suaves entre modo claro y oscuro

## Migración de Código Existente

Para migrar código existente:

1. Reemplazar `bg-gray-*` por `bg-background`, `bg-card`, o `bg-muted`
2. Reemplazar `text-gray-*` por `text-foreground` o `text-muted-foreground`
3. Usar variantes semánticas en Badge y Alert
4. Aplicar gradientes unificados en páginas públicas

## Ejemplo de Implementación

```tsx
// Antes
<div className="bg-white dark:bg-gray-800 border border-gray-200 dark:border-gray-700">
  <Badge className="bg-green-100 text-green-800">Activo</Badge>
</div>

// Después  
<div className="bg-card border border-border">
  <Badge variant="success">Activo</Badge>
</div>
```

Este sistema asegura una experiencia visual cohesiva y profesional en toda la aplicación. 