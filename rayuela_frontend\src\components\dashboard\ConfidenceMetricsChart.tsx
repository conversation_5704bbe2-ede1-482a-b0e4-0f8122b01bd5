"use client";

import { useEffect, useState } from 'react';
import {
  Chart as ChartJS,
  CategoryScale,
  LinearScale,
  PointElement,
  LineElement,
  BarElement,
  Title,
  Tooltip,
  Legend,
  PieController,
  ArcElement,
  ChartOptions,
  ChartData,
} from 'chart.js';
import { Line, Bar, Pie, Doughnut } from 'react-chartjs-2';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs';
import { Skeleton } from '@/components/ui/skeleton';
import { TooltipHelper, MetricDescription, metricDefinitions } from '@/components/ui/tooltip-helper';
import { InfoIcon } from 'lucide-react';
import { ConfidenceMetrics } from '@/lib/api/recommendation-metrics';
import { format, parseISO } from 'date-fns';
import {
  baseChartOptions,
  chartColors,
  getColorByIndex,
  formatNumber,
  formatPercentage
} from '@/lib/chart-utils';
import { Badge } from '@/components/ui/badge';

// Registrar los componentes necesarios de Chart.js
ChartJS.register(
  CategoryScale,
  LinearScale,
  PointElement,
  LineElement,
  BarElement,
  PieController,
  ArcElement,
  Title,
  Tooltip,
  Legend
);

interface ConfidenceMetricsChartProps {
  data?: ConfidenceMetrics;
  isLoading?: boolean;
  error?: Error | null;
  title?: string;
  description?: string;
}

export default function ConfidenceMetricsChart({
  data,
  isLoading = false,
  error = null,
  title = "Métricas de Confianza",
  description = "Análisis de confianza en las recomendaciones"
}: ConfidenceMetricsChartProps) {
  const [activeTab, setActiveTab] = useState('distribution');

  // Datos para el gráfico de distribución de confianza
  const distributionData: ChartData<'bar'> = {
    labels: ['Baja', 'Media', 'Alta'],
    datasets: [
      {
        label: 'Colaborativo',
        data: data ? [
          data.confidence_distribution.collaborative.low,
          data.confidence_distribution.collaborative.medium,
          data.confidence_distribution.collaborative.high,
        ] : [],
        backgroundColor: chartColors.blue.background,
        borderColor: chartColors.blue.primary,
        borderWidth: 1,
      },
      {
        label: 'Contenido',
        data: data ? [
          data.confidence_distribution.content.low,
          data.confidence_distribution.content.medium,
          data.confidence_distribution.content.high,
        ] : [],
        backgroundColor: chartColors.green.background,
        borderColor: chartColors.green.primary,
        borderWidth: 1,
      },
      {
        label: 'Híbrido',
        data: data ? [
          data.confidence_distribution.hybrid.low,
          data.confidence_distribution.hybrid.medium,
          data.confidence_distribution.hybrid.high,
        ] : [],
        backgroundColor: chartColors.orange.background,
        borderColor: chartColors.orange.primary,
        borderWidth: 1,
      },
    ],
  };

  // Datos para el gráfico de confianza por categoría
  const categoryData: ChartData<'bar'> = {
    labels: data ? Object.keys(data.category_confidence).slice(0, 10) : [],
    datasets: [
      {
        label: 'Confianza Promedio',
        data: data ? Object.values(data.category_confidence).slice(0, 10).map(value => value * 100) : [],
        backgroundColor: chartColors.blue.background,
        borderColor: chartColors.blue.primary,
        borderWidth: 1,
      },
    ],
  };

  // Datos para el gráfico de factores de confianza
  const factorsData: ChartData<'doughnut'> = {
    labels: ['Tamaño Historial Usuario', 'Popularidad del Ítem', 'Fuerza de Categoría', 'Tipo de Modelo'],
    datasets: [
      {
        data: data ? [
          data.confidence_factors.user_history_size * 100,
          data.confidence_factors.item_popularity * 100,
          data.confidence_factors.category_strength * 100,
          data.confidence_factors.model_type * 100,
        ] : [],
        backgroundColor: [
          chartColors.blue.background,
          chartColors.green.background,
          chartColors.orange.background,
          chartColors.purple.background,
        ],
        borderColor: [
          chartColors.blue.primary,
          chartColors.green.primary,
          chartColors.orange.primary,
          chartColors.purple.primary,
        ],
        borderWidth: 1,
      },
    ],
  };

  // Datos para el gráfico de tendencias de confianza
  const trendsData: ChartData<'line'> = {
    labels: data?.confidence_trends.last_7_days.map(point => format(parseISO(point.date), 'dd/MM')) || [],
    datasets: [
      {
        label: 'Confianza Promedio',
        data: data?.confidence_trends.last_7_days.map(point => point.avg_confidence * 100) || [],
        borderColor: chartColors.blue.primary,
        backgroundColor: chartColors.blue.background,
        tension: 0.3,
        fill: true,
      },
      {
        label: 'Número de Interacciones',
        data: data?.confidence_trends.last_7_days.map(point => point.count) || [],
        borderColor: chartColors.orange.primary,
        backgroundColor: chartColors.orange.background,
        tension: 0.3,
        yAxisID: 'y1',
      },
    ],
  };

  // Opciones para los gráficos de barras
  const barOptions: ChartOptions<'bar'> = {
    responsive: true,
    maintainAspectRatio: false,
    plugins: {
      legend: {
        position: 'top',
        labels: {
          boxWidth: 12,
          padding: 15,
          font: {
            size: 11
          }
        }
      },
      tooltip: {
        backgroundColor: 'rgba(0, 0, 0, 0.7)',
        titleFont: {
          size: 12,
          weight: 'bold'
        },
        bodyFont: {
          size: 11
        },
        padding: 8,
        cornerRadius: 4,
        displayColors: true,
        boxWidth: 8,
        boxHeight: 8,
        boxPadding: 4,
        usePointStyle: true,
        callbacks: {
          label: (context) => {
            const label = context.dataset.label || '';
            const value = context.parsed.y?.toFixed(1) || '0';
            return `${label}: ${value}%`;
          }
        }
      }
    },
    scales: {
      x: {
        grid: {
          display: false,
        },
        ticks: {
          font: {
            size: 10
          },
          maxRotation: 45,
          minRotation: 45
        }
      },
      y: {
        beginAtZero: true,
        max: 100,
        grid: {
          color: 'rgba(0, 0, 0, 0.05)',
        },
        ticks: {
          font: {
            size: 11
          },
          callback: function (value) {
            return value + '%';
          }
        }
      }
    }
  };

  const categoryOptions: ChartOptions<'bar'> = {
    responsive: true,
    maintainAspectRatio: false,
    indexAxis: 'y' as const,
    plugins: {
      legend: {
        position: 'top',
        labels: {
          boxWidth: 12,
          padding: 15,
          font: {
            size: 11
          }
        }
      },
      tooltip: {
        backgroundColor: 'rgba(0, 0, 0, 0.7)',
        titleFont: {
          size: 12,
          weight: 'bold'
        },
        bodyFont: {
          size: 11
        },
        padding: 8,
        cornerRadius: 4,
        displayColors: true,
        boxWidth: 8,
        boxHeight: 8,
        boxPadding: 4,
        usePointStyle: true,
        callbacks: {
          label: function (context) {
            let label = context.dataset.label || '';
            if (label) {
              label += ': ';
            }
            if (context.parsed.x !== null) {
              label += formatPercentage(context.parsed.x);
            }
            return label;
          }
        }
      }
    },
    scales: {
      x: {
        beginAtZero: true,
        max: 100,
        grid: {
          color: 'rgba(0, 0, 0, 0.05)',
        },
        ticks: {
          font: {
            size: 11
          },
          callback: function (value) {
            return formatPercentage(value as number, 0);
          }
        }
      },
      y: {
        grid: {
          display: false,
        },
        ticks: {
          font: {
            size: 10
          }
        }
      }
    },
  };

  const doughnutOptions: ChartOptions<'doughnut'> = {
    responsive: true,
    maintainAspectRatio: false,
    plugins: {
      legend: {
        position: 'right',
        labels: {
          boxWidth: 12,
          padding: 15,
          font: {
            size: 11
          }
        }
      },
      tooltip: {
        backgroundColor: 'rgba(0, 0, 0, 0.7)',
        titleFont: {
          size: 12,
          weight: 'bold'
        },
        bodyFont: {
          size: 11
        },
        padding: 8,
        cornerRadius: 4,
        displayColors: true,
        boxWidth: 8,
        boxHeight: 8,
        boxPadding: 4,
        usePointStyle: true,
        callbacks: {
          label: function (context) {
            let label = context.label || '';
            if (label) {
              label += ': ';
            }
            if (context.raw !== null) {
              label += formatPercentage(context.raw as number);
            }
            return label;
          }
        }
      }
    },
  };

  const lineOptions: ChartOptions<'line'> = {
    responsive: true,
    maintainAspectRatio: false,
    plugins: {
      legend: {
        position: 'top',
        labels: {
          boxWidth: 12,
          padding: 15,
          font: {
            size: 11
          }
        }
      },
      tooltip: {
        backgroundColor: 'rgba(0, 0, 0, 0.7)',
        titleFont: {
          size: 12,
          weight: 'bold'
        },
        bodyFont: {
          size: 11
        },
        padding: 8,
        cornerRadius: 4,
        displayColors: true,
        boxWidth: 8,
        boxHeight: 8,
        boxPadding: 4,
        usePointStyle: true,
        callbacks: {
          label: function (context) {
            let label = context.dataset.label || '';
            if (label) {
              label += ': ';
            }
            if (context.parsed.y !== null) {
              if (label.includes('Confianza')) {
                label += formatPercentage(context.parsed.y);
              } else {
                label += formatNumber(context.parsed.y);
              }
            }
            return label;
          }
        }
      }
    },
    scales: {
      x: {
        grid: {
          display: false,
        },
        ticks: {
          font: {
            size: 10
          },
          maxRotation: 45,
          minRotation: 45
        }
      },
      y: {
        beginAtZero: true,
        position: 'left',
        ticks: {
          callback: function (value) {
            return formatPercentage(value as number, 0);
          }
        }
      },
      y1: {
        beginAtZero: true,
        position: 'right',
        grid: {
          drawOnChartArea: false,
        },
        ticks: {
          font: {
            size: 11
          }
        }
      }
    },
  };

  if (isLoading) {
    return (
      <Card className="transition-all duration-300 hover:shadow-md">
        <CardHeader>
          <CardTitle><Skeleton className="h-6 w-1/3" /></CardTitle>
          <CardDescription><Skeleton className="h-4 w-1/2" /></CardDescription>
          <Skeleton className="h-10 w-full mt-2" />
        </CardHeader>
        <CardContent className="h-80">
          <Skeleton className="h-full w-full rounded-md" />
        </CardContent>
      </Card>
    );
  }

  if (error) {
    return (
      <Card className="transition-all duration-300 hover:shadow-md border-red-200 dark:border-red-800">
        <CardHeader>
          <CardTitle className="text-red-500 flex items-center gap-2">
            <InfoIcon className="h-5 w-5" />
            {title} - Error
          </CardTitle>
          <CardDescription className="text-red-400">
            No se pudieron cargar las métricas: {error.message}
          </CardDescription>
        </CardHeader>
        <CardContent className="h-80">
          <div className="flex flex-col items-center justify-center h-full gap-2">
            <p className="text-red-500 mb-2">Ocurrió un error al cargar los datos de métricas de confianza.</p>
            <p className="text-sm text-gray-500">Intenta de nuevo más tarde o contacta con soporte.</p>
            <Badge variant="outline" className="mt-2 bg-red-50 text-red-700 border-red-200 dark:bg-red-900/20 dark:text-red-300 dark:border-red-800">
              Error: {error.message}
            </Badge>
          </div>
        </CardContent>
      </Card>
    );
  }

  if (!data) {
    return (
      <Card className="transition-all duration-300 hover:shadow-md">
        <CardHeader>
          <CardTitle>{title}</CardTitle>
          <CardDescription>{description}</CardDescription>
        </CardHeader>
        <CardContent className="h-80">
          <div className="flex flex-col items-center justify-center h-full gap-2">
            <InfoIcon className="h-12 w-12 text-gray-300 mb-2" />
            <p className="text-gray-500 font-medium">No hay datos disponibles</p>
            <p className="text-sm text-gray-400 text-center max-w-md">
              Genera recomendaciones para ver métricas de confianza y análisis de precisión.
            </p>
          </div>
        </CardContent>
      </Card>
    );
  }

  return (
    <Card className="transition-all duration-300 hover:shadow-md">
      <CardHeader>
        <div className="flex justify-between items-start">
          <div>
            <CardTitle className="text-xl font-semibold">{title}</CardTitle>
            <CardDescription>
              {description}
              {data.total_interactions_analyzed > 0 && (
                <span className="block text-xs text-gray-500 mt-1">
                  Basado en {formatNumber(data.total_interactions_analyzed)} interacciones analizadas
                </span>
              )}
            </CardDescription>
          </div>

          {/* Información de confianza global */}
          {data && data.confidence_distribution && (
            <Badge variant="outline" className="bg-blue-50 text-blue-700 border-blue-200 dark:bg-blue-900/20 dark:text-blue-300 dark:border-blue-800">
              Confianza promedio: {formatPercentage(
                (data.confidence_distribution.collaborative.avg +
                  data.confidence_distribution.content.avg +
                  data.confidence_distribution.hybrid.avg) / 3 * 100
              )}
            </Badge>
          )}
        </div>

        <Tabs value={activeTab} onValueChange={setActiveTab} className="w-full mt-2">
          <TabsList className="grid grid-cols-4 w-full">
            <div className="relative">
              <TabsTrigger
                value="distribution"
                className="transition-all duration-200 data-[state=active]:bg-blue-100 data-[state=active]:text-blue-700 dark:data-[state=active]:bg-blue-900/30 dark:data-[state=active]:text-blue-300 w-full"
              >
                <div className="flex items-center justify-center gap-1">
                  Distribución
                  <TooltipHelper
                    content={
                      <div className="space-y-2">
                        <p className="text-gray-700 dark:text-gray-300">
                          Distribución de los niveles de confianza por tipo de modelo de recomendación.
                        </p>
                        <ul className="list-disc pl-4 text-gray-700 dark:text-gray-300">
                          <li><span className="font-medium">Baja:</span> Recomendaciones con confianza menor al 40%</li>
                          <li><span className="font-medium">Media:</span> Recomendaciones con confianza entre 40% y 70%</li>
                          <li><span className="font-medium">Alta:</span> Recomendaciones con confianza mayor al 70%</li>
                        </ul>
                        <p className="text-gray-700 dark:text-gray-300">
                          Comparación entre modelos colaborativos, basados en contenido e híbridos.
                        </p>
                      </div>
                    }
                    iconSize={12}
                  />
                </div>
              </TabsTrigger>
            </div>
            <div className="relative">
              <TabsTrigger
                value="categories"
                className="transition-all duration-200 data-[state=active]:bg-green-100 data-[state=active]:text-green-700 dark:data-[state=active]:bg-green-900/30 dark:data-[state=active]:text-green-300 w-full"
              >
                <div className="flex items-center justify-center gap-1">
                  Categorías
                  <TooltipHelper
                    content={
                      <div className="space-y-2">
                        <p className="text-gray-700 dark:text-gray-300">
                          Nivel de confianza promedio por categoría de producto.
                        </p>
                        <p className="text-gray-700 dark:text-gray-300">
                          Este gráfico muestra:
                        </p>
                        <ul className="list-disc pl-4 text-gray-700 dark:text-gray-300">
                          <li>Qué categorías generan recomendaciones más confiables</li>
                          <li>Dónde el sistema tiene mayor certeza sobre sus predicciones</li>
                          <li>Categorías que podrían necesitar más datos o mejores modelos</li>
                        </ul>
                      </div>
                    }
                    iconSize={12}
                  />
                </div>
              </TabsTrigger>
            </div>
            <div className="relative">
              <TabsTrigger
                value="factors"
                className="transition-all duration-200 data-[state=active]:bg-purple-100 data-[state=active]:text-purple-700 dark:data-[state=active]:bg-purple-900/30 dark:data-[state=active]:text-purple-300 w-full"
              >
                <div className="flex items-center justify-center gap-1">
                  Factores
                  <TooltipHelper
                    content={
                      <div className="space-y-2">
                        <p className="text-gray-700 dark:text-gray-300">
                          Factores que influyen en la confianza de las recomendaciones.
                        </p>
                        <ul className="list-disc pl-4 text-gray-700 dark:text-gray-300">
                          <li><span className="font-medium">Tamaño Historial Usuario:</span> Impacto del número de interacciones previas</li>
                          <li><span className="font-medium">Popularidad del Ítem:</span> Influencia de la popularidad general del producto</li>
                          <li><span className="font-medium">Fuerza de Categoría:</span> Efecto de la afinidad del usuario por la categoría</li>
                          <li><span className="font-medium">Tipo de Modelo:</span> Impacto del algoritmo utilizado</li>
                        </ul>
                      </div>
                    }
                    iconSize={12}
                  />
                </div>
              </TabsTrigger>
            </div>
            <div className="relative">
              <TabsTrigger
                value="trends"
                className="transition-all duration-200 data-[state=active]:bg-orange-100 data-[state=active]:text-orange-700 dark:data-[state=active]:bg-orange-900/30 dark:data-[state=active]:text-orange-300 w-full"
              >
                <div className="flex items-center justify-center gap-1">
                  Tendencias
                  <TooltipHelper
                    content={
                      <div className="space-y-2">
                        <p className="text-gray-700 dark:text-gray-300">
                          Evolución de la confianza promedio a lo largo del tiempo.
                        </p>
                        <p className="text-gray-700 dark:text-gray-300">
                          Este gráfico muestra:
                        </p>
                        <ul className="list-disc pl-4 text-gray-700 dark:text-gray-300">
                          <li>Cambios en la confianza durante los últimos 7 días</li>
                          <li>Correlación entre número de interacciones y nivel de confianza</li>
                          <li>Impacto de actualizaciones del modelo o cambios en el catálogo</li>
                        </ul>
                      </div>
                    }
                    iconSize={12}
                  />
                </div>
              </TabsTrigger>
            </div>
          </TabsList>
        </Tabs>
      </CardHeader>
      <CardContent className="h-80 pt-4">
        <TabsContent value="distribution" className="h-full mt-0">
          <Bar
            options={barOptions}
            data={distributionData}
            className="h-full"
          />
        </TabsContent>
        <TabsContent value="categories" className="h-full mt-0">
          <Bar
            options={categoryOptions}
            data={categoryData}
            className="h-full"
          />
        </TabsContent>
        <TabsContent value="factors" className="h-full mt-0">
          <div className="flex items-center justify-center h-full">
            <div className="w-3/4 h-full">
              <Doughnut
                options={doughnutOptions}
                data={factorsData}
                className="h-full"
              />
            </div>
          </div>
        </TabsContent>
        <TabsContent value="trends" className="h-full mt-0">
          <Line
            options={lineOptions}
            data={trendsData}
            className="h-full"
          />
        </TabsContent>
      </CardContent>
    </Card>
  );
}
