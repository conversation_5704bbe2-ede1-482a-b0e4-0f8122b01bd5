# Implementación de Validación Temprana de Límites de Entrenamiento

## 📋 Resumen

Se implementó la validación temprana de límites de datos de entrenamiento en el endpoint de la API para evitar el consumo innecesario de recursos de workers Celery y proporcionar feedback inmediato al usuario.

## 🔧 Cambios Realizados

### 1. Endpoint API (`src/api/v1/endpoints/pipeline.py`)

**Agregado:**
- Import de `func` de SQLAlchemy y modelo `Interaction`
- Consulta de conteo ligera antes de encolar la tarea Celery
- Validación con `LimitService.validate_training_data_limit()`
- Respuesta HTTP 429 inmediata si se excede el límite

```python
# Validar límites de datos de entrenamiento antes de encolar la tarea
# Contar interacciones para validar límites de datos antes de encolar
total_interactions_query = select(func.count(Interaction.id)).where(
    Interaction.account_id == account.account_id
)
total_interactions = (await db.execute(total_interactions_query)).scalar_one()

try:
    await limit_service.validate_training_data_limit(total_interactions)
except LimitExceededError as e:
    log_error(f"Training data limit exceeded for account {account.account_id}: {str(e)}")
    raise HTTPException(status_code=429, detail=str(e))
```

### 2. Training Pipeline (`src/ml_pipeline/training_pipeline.py`)

**Modificado:**
- Método `train()`: Agregado parámetro `validate_limits: bool = True`
- Método `_prepare_training_data()`: Agregado parámetro `validate_limits: bool = True`
- Paso del parámetro a `fetch_training_data()` para controlar la validación

### 3. Workers Celery (`src/workers/celery_tasks.py`)

**Modificado:**
- Corregido llamadas de `train_models()` a `train()` (método correcto)
- Agregado `validate_limits=False` en ambas tareas de entrenamiento
- Comentarios explicativos sobre por qué se desactiva la validación

```python
# Note: validate_limits=False because validation is done in the API endpoint
result = await training_pipeline.train(
    db=session,
    account_id=account_id,
    data=data,
    max_interactions=max_interactions,
    sample_size=sample_size,
    days_limit=days_limit,
    interaction_types=interaction_types,
    validate_limits=False,
)
```

## 📊 Límites por Plan

Los límites están definidos en `src/db/enums.py:PLAN_LIMITS`:

| Plan | Límite de Interacciones de Entrenamiento |
|------|-------------------------------------------|
| FREE | 10,000 |
| STARTER | 100,000 |
| PRO | 1,000,000 |
| ENTERPRISE | Ilimitado (-1) |

## ✅ Criterios de Aceptación

- [x] Los usuarios reciben un error HTTP 429 en la API si exceden el límite de datos de entrenamiento
- [x] La validación ocurre **antes** de que se inicie un worker Celery
- [x] El endpoint realiza una consulta `SELECT COUNT(*)` ligera para contar interacciones
- [x] Se utiliza `LimitService.validate_training_data_limit()` para la validación
- [x] Los workers Celery no duplican la validación (se evita con `validate_limits=False`)

## 🚀 Beneficios

### Inmediatos
- **Feedback inmediato**: Los usuarios reciben un error HTTP 429 instantáneamente
- **Ahorro de recursos**: No se consumen workers Celery innecesariamente
- **Reducción de costos**: Menos uso de CPU y memoria en los workers

### A largo plazo
- **Mejor experiencia de usuario**: Respuesta rápida en lugar de esperar a que falle el worker
- **Escalabilidad**: Los workers se enfocan solo en entrenamiento válido
- **Observabilidad**: Errores claros y trazables en los logs de la API

## 🧪 Validación

La implementación fue validada con:
1. Verificación sintáctica de todos los archivos modificados
2. Test de simulación de diferentes planes y límites
3. Verificación de que todos los `PLAN_LIMITS` contienen `training_data_limit`

## 📈 Impacto

**Prioridad:** ALTA - Reduce costos de cómputo y mejora la experiencia del usuario al proporcionar feedback inmediato.

**Casos de uso beneficiados:**
- Cuentas FREE que intentan entrenar con más de 10K interacciones
- Cuentas STARTER que intentan entrenar con más de 100K interacciones  
- Cuentas PRO que intentan entrenar con más de 1M interacciones

Antes de esta implementación, estos casos consumirían recursos del worker antes de fallar. Ahora fallan inmediatamente en el endpoint de la API. 