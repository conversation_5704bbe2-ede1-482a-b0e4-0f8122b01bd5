import { Metadata } from 'next'

const baseUrl = process.env.NEXT_PUBLIC_SITE_URL || 'https://rayuela.ai'
const siteName = 'Rayuela.ai'
const defaultDescription = 'Sistemas de recomendación avanzados para tu negocio, sin la complejidad de construirlos desde cero.'

export interface SEOConfig {
  title: string
  description?: string
  path: string
  keywords?: string[]
  type?: 'website' | 'article'
  publishedTime?: string
  modifiedTime?: string
  noIndex?: boolean
}

export function generateMetadata({
  title,
  description = defaultDescription,
  path,
  keywords = [],
  type = 'website',
  publishedTime,
  modifiedTime,
  noIndex = false
}: SEOConfig): Metadata {
  const fullTitle = title.includes(siteName) ? title : `${title} | ${siteName}`
  const canonicalUrl = `${baseUrl}${path}`

  const metadata: Metadata = {
    title: fullTitle,
    description,
    keywords: keywords.join(', '),
    authors: [{ name: 'Rayuela Team' }],
    creator: '<PERSON><PERSON><PERSON>',
    publisher: '<PERSON><PERSON><PERSON>',
    robots: noIndex ? 'noindex, nofollow' : 'index, follow',
    alternates: {
      canonical: canonicalUrl,
    },
    openGraph: {
      title: fullTitle,
      description,
      url: canonicalUrl,
      siteName,
      type,
      locale: 'es_AR',
      images: [
        {
          url: `${baseUrl}/og-image.png`,
          width: 1200,
          height: 630,
          alt: title,
        },
      ],
    },
    twitter: {
      card: 'summary_large_image',
      title: fullTitle,
      description,
      images: [`${baseUrl}/og-image.png`],
      creator: '@rayuela_ai',
    },
  }

  if (publishedTime) {
    metadata.openGraph = {
      ...metadata.openGraph,
      type: 'article',
      publishedTime,
    }
  }

  if (modifiedTime) {
    metadata.openGraph = {
      ...metadata.openGraph,
      type: 'article',
      modifiedTime,
    }
  }

  return metadata
}

export function generateJsonLd(type: 'Organization' | 'SoftwareApplication' | 'APIReference', data: any) {
  const baseSchema = {
    '@context': 'https://schema.org',
    '@type': type,
  }

  switch (type) {
    case 'Organization':
      return {
        ...baseSchema,
        name: 'Rayuela',
        url: baseUrl,
        logo: `${baseUrl}/logo.png`,
        description: defaultDescription,
        foundingDate: '2024',
        industry: 'Software',
        sameAs: [
          'https://twitter.com/rayuela_ai',
          'https://linkedin.com/company/rayuela-ai',
        ],
        ...data,
      }

    case 'SoftwareApplication':
      return {
        ...baseSchema,
        name: 'Rayuela API',
        applicationCategory: 'DeveloperApplication',
        operatingSystem: 'Any',
        offers: {
          '@type': 'Offer',
          price: '0',
          priceCurrency: 'USD',
          description: 'Free tier available',
        },
        ...data,
      }

    case 'APIReference':
      return {
        ...baseSchema,
        name: data.name || 'Rayuela API Documentation',
        description: data.description || 'Complete API reference for Rayuela recommendation system',
        url: data.url || `${baseUrl}/docs`,
        programmingLanguage: ['Python', 'JavaScript', 'PHP'],
        ...data,
      }

    default:
      return baseSchema
  }
}
