import asyncio
import httpx
import json
from pprint import pprint

async def test_similar_products():
    """
    Prueba el endpoint de productos similares
    """
    # Configuración
    base_url = "http://localhost:8001"
    api_key = "test_api_key"  # Reemplazar con una API key válida
    product_id = 1  # Reemplazar con un ID de producto válido

    # Parámetros
    params = {
        "limit": 5,
        "include_explanation": True,
        "explanation_level": "detailed"
    }

    # Headers
    headers = {
        "X-API-Key": api_key
    }

    # Realizar la solicitud
    async with httpx.AsyncClient() as client:
        # La ruta correcta según el router en src/api/v1/endpoints/recommendations.py
        url = f"{base_url}/api/v1/recommendations/products/{product_id}/similar"
        print(f"Requesting URL: {url}")
        try:
            response = await client.get(url, params=params, headers=headers)
        except Exception as e:
            print(f"Error en la solicitud: {e}")
            return

        # Imprimir resultados
        print(f"Status code: {response.status_code}")
        if response.status_code == 200:
            data = response.json()
            print(f"Total items: {data['total']}")
            print(f"Page: {data['page']}")
            print(f"Size: {data['size']}")
            print("\nItems:")
            for i, item in enumerate(data['items']):
                print(f"\nItem {i+1}:")
                print(f"  ID: {item['id']}")
                print(f"  Name: {item['name']}")
                print(f"  Similarity score: {item['similarity_score']}")
                print(f"  Source: {item['source']}")

                # Imprimir explicación
                if 'explanation' in item:
                    explanation = item['explanation']
                    if isinstance(explanation, str):
                        print(f"  Explanation: {explanation}")
                    else:
                        print(f"  Explanation:")
                        print(f"    Primary reason: {explanation['primary_reason']}")
                        print(f"    Confidence: {explanation['confidence']}")
                        print(f"    Text: {explanation['text_explanation']}")
                        if 'evidence' in explanation:
                            print(f"    Evidence:")
                            for ev in explanation['evidence']:
                                print(f"      - {ev['type']}: {ev['name']} (relevance: {ev['relevance']})")

if __name__ == "__main__":
    asyncio.run(test_similar_products())
