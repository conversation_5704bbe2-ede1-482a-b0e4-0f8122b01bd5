import React from 'react';
import { LucideIcon } from 'lucide-react';
import { cn } from '@/lib/utils';

// Sistema de tamaños semánticos para iconos
export const iconSizes = {
  xs: 'h-3 w-3', // 12px - Para elementos muy pequeños, badges
  sm: 'h-4 w-4', // 16px - Para botones pequeños, enlaces, texto inline
  md: 'h-5 w-5', // 20px - Tamaño estándar para la mayoría de casos
  lg: 'h-6 w-6', // 24px - Para títulos, elementos destacados
  xl: 'h-8 w-8', // 32px - Para elementos de estado, ilustraciones pequeñas
  '2xl': 'h-12 w-12', // 48px - Para estados vacíos, ilustraciones grandes
} as const;

// Contextos semánticos con colores apropiados
export const iconContexts = {
  // Estados y feedback
  success: 'text-success',
  warning: 'text-warning',
  error: 'text-destructive',
  info: 'text-info',
  
  // Jerarquía visual
  primary: 'text-primary',
  secondary: 'text-secondary-foreground',
  muted: 'text-muted-foreground',
  
  // Contextos específicos
  interactive: 'text-primary hover:text-primary/80',
  neutral: 'text-foreground',
  subtle: 'text-muted-foreground',
  
  // Para elementos específicos de negocio
  metric: 'text-primary',
  action: 'text-primary',
  navigation: 'text-muted-foreground hover:text-foreground',
} as const;

export type IconSize = keyof typeof iconSizes;
export type IconContext = keyof typeof iconContexts;

interface SemanticIconProps {
  icon: LucideIcon;
  size?: IconSize;
  context?: IconContext;
  className?: string;
  ['aria-label']?: string;
  ['aria-hidden']?: boolean;
}

/**
 * Componente de icono semántico que maneja tamaños y colores de forma consistente
 * 
 * @param icon - El componente de icono de lucide-react
 * @param size - Tamaño semántico del icono (xs, sm, md, lg, xl, 2xl)
 * @param context - Contexto semántico que determina el color (success, warning, error, etc.)
 * @param className - Clases adicionales de Tailwind
 * @param aria-label - Etiqueta de accesibilidad
 * @param aria-hidden - Si el icono es decorativo
 */
export function SemanticIcon({
  icon: Icon,
  size = 'md',
  context = 'neutral',
  className,
  'aria-label': ariaLabel,
  'aria-hidden': ariaHidden = !ariaLabel,
  ...props
}: SemanticIconProps) {
  return (
    <Icon
      className={cn(
        iconSizes[size],
        iconContexts[context],
        'shrink-0', // Previene que el icono se comprima en layouts flex
        className
      )}
      aria-label={ariaLabel}
      aria-hidden={ariaHidden}
      {...props}
    />
  );
}

// Hook para obtener clases de icono sin renderizar el componente
export function useIconClasses(size: IconSize = 'md', context: IconContext = 'neutral') {
  return cn(iconSizes[size], iconContexts[context], 'shrink-0');
}

// Componentes de conveniencia para casos de uso frecuentes
interface StatusIconProps extends Omit<SemanticIconProps, 'context'> {}

export function SuccessIcon(props: StatusIconProps) {
  return <SemanticIcon {...props} context="success" />;
}

export function WarningIcon(props: StatusIconProps) {
  return <SemanticIcon {...props} context="warning" />;
}

export function ErrorIcon(props: StatusIconProps) {
  return <SemanticIcon {...props} context="error" />;
}

export function InfoIcon(props: StatusIconProps) {
  return <SemanticIcon {...props} context="info" />;
}

export function ActionIcon(props: StatusIconProps) {
  return <SemanticIcon {...props} context="action" />;
}

export function NavigationIcon(props: StatusIconProps) {
  return <SemanticIcon {...props} context="navigation" />;
}

// Componente para iconos con texto que mantiene alineación consistente
interface IconWithTextProps {
  icon: LucideIcon;
  children: React.ReactNode;
  size?: IconSize;
  context?: IconContext;
  iconPosition?: 'left' | 'right';
  spacing?: 'tight' | 'normal' | 'loose';
  className?: string;
}

const spacingClasses = {
  tight: 'gap-1',
  normal: 'gap-2',
  loose: 'gap-3',
} as const;

export function IconWithText({
  icon,
  children,
  size = 'sm',
  context = 'neutral',
  iconPosition = 'left',
  spacing = 'normal',
  className,
}: IconWithTextProps) {
  const iconElement = (
    <SemanticIcon 
      icon={icon} 
      size={size} 
      context={context}
      aria-hidden={true}
    />
  );

  return (
    <span className={cn('inline-flex items-center', spacingClasses[spacing], className)}>
      {iconPosition === 'left' && iconElement}
      {children}
      {iconPosition === 'right' && iconElement}
    </span>
  );
}

// Las constantes ya están exportadas arriba
