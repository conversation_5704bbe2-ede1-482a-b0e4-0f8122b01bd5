#!/usr/bin/env python3
"""
🔒 RLS Security Compliance Checker

This script monitors and validates the security of RLS bypass operations
to ensure they comply with security policies and detect potential violations.

SECURITY REQUIREMENTS:
- Only authorized internal processes should use RLS bypass
- All operations must have proper account_id validation
- Network endpoints exposing RLS bypass should be disabled
- Comprehensive audit logging must be in place

Usage:
    python scripts/security/rls_security_check.py [--check-logs] [--verify-endpoints]
"""

import asyncio
import sys
import json
from datetime import datetime, timezone, timedelta
from typing import Dict, List, Any, Optional
import argparse
from pathlib import Path

# Add the src directory to Python path
sys.path.insert(0, str(Path(__file__).parent.parent.parent / "src"))

from src.utils.base_logger import log_info, log_error, log_warning
from src.db.session import get_db
from sqlalchemy import text


class RLSSecurityChecker:
    """Security compliance checker for RLS bypass operations."""
    
    def __init__(self):
        self.violations = []
        self.warnings = []
        self.checks_passed = 0
        self.checks_failed = 0
    
    def add_violation(self, severity: str, message: str, details: Optional[Dict] = None):
        """Add a security violation."""
        violation = {
            "severity": severity,
            "message": message,
            "details": details or {},
            "timestamp": datetime.now(timezone.utc).isoformat()
        }
        self.violations.append(violation)
        self.checks_failed += 1
        
        if severity == "CRITICAL":
            log_error(f"🔴 CRITICAL SECURITY VIOLATION: {message}")
        elif severity == "HIGH":
            log_error(f"🟠 HIGH SECURITY RISK: {message}")
        else:
            log_warning(f"🟡 SECURITY WARNING: {message}")
    
    def add_success(self, message: str):
        """Add a successful security check."""
        self.checks_passed += 1
        log_info(f"✅ {message}")
    
    async def check_database_functions(self):
        """Check PostgreSQL SECURITY DEFINER functions for security compliance."""
        log_info("🔍 Checking PostgreSQL SECURITY DEFINER functions...")
        
        try:
            async with get_db() as db:
                # Check if cleanup_old_data function exists and has proper security
                result = await db.execute(text("""
                    SELECT 
                        p.proname,
                        p.prosecdef,
                        p.proowner,
                        r.rolname as owner_role,
                        p.prosrc
                    FROM pg_proc p
                    JOIN pg_roles r ON p.proowner = r.oid
                    WHERE p.proname IN ('cleanup_old_data', 'bypass_rls')
                    AND p.prosecdef = true
                """))
                
                functions = result.fetchall()
                
                if not functions:
                    self.add_violation(
                        "HIGH", 
                        "No SECURITY DEFINER functions found - this may indicate incomplete setup",
                        {"expected_functions": ["cleanup_old_data", "bypass_rls"]}
                    )
                    return
                
                for func in functions:
                    func_name = func.proname
                    owner_role = func.owner_role
                    func_source = func.prosrc
                    
                    # Check if function has proper parameter validation
                    if "account_id" not in func_source.lower():
                        self.add_violation(
                            "CRITICAL",
                            f"SECURITY DEFINER function '{func_name}' lacks account_id parameter validation",
                            {"function": func_name, "owner": owner_role}
                        )
                    else:
                        self.add_success(f"Function '{func_name}' has account_id parameter")
                    
                    # Check for SQL injection protection
                    if "concat" in func_source.lower() or "||" in func_source:
                        self.add_violation(
                            "HIGH",
                            f"Function '{func_name}' may be vulnerable to SQL injection (string concatenation detected)",
                            {"function": func_name, "owner": owner_role}
                        )
                    else:
                        self.add_success(f"Function '{func_name}' appears to use parameterized queries")
                        
        except Exception as e:
            self.add_violation(
                "HIGH",
                f"Failed to check database functions: {str(e)}",
                {"error_type": type(e).__name__}
            )
    
    async def check_recent_rls_usage(self, hours: int = 24):
        """Check recent RLS bypass usage in audit logs."""
        log_info(f"🔍 Checking RLS bypass usage in last {hours} hours...")
        
        try:
            async with get_db() as db:
                # Look for RLS bypass operations in audit logs
                cutoff_time = datetime.now(timezone.utc) - timedelta(hours=hours)
                
                result = await db.execute(text("""
                    SELECT 
                        created_at,
                        action,
                        details,
                        account_id,
                        user_id
                    FROM audit_logs 
                    WHERE created_at > :cutoff_time
                    AND (
                        action ILIKE '%rls%' 
                        OR action ILIKE '%bypass%'
                        OR details::text ILIKE '%rls%'
                        OR details::text ILIKE '%bypass%'
                        OR details::text ILIKE '%SECURITY DEFINER%'
                    )
                    ORDER BY created_at DESC
                    LIMIT 100
                """), {"cutoff_time": cutoff_time})
                
                rls_operations = result.fetchall()
                
                if rls_operations:
                    log_warning(f"Found {len(rls_operations)} RLS bypass operations in last {hours} hours")
                    
                    for op in rls_operations:
                        # Check if operation has proper account_id
                        if not op.account_id:
                            self.add_violation(
                                "CRITICAL",
                                "RLS bypass operation without account_id detected",
                                {
                                    "timestamp": op.created_at.isoformat(),
                                    "action": op.action,
                                    "user_id": op.user_id
                                }
                            )
                        else:
                            # This is expected for maintenance operations
                            log_info(f"RLS operation for account {op.account_id} at {op.created_at}")
                else:
                    self.add_success(f"No RLS bypass operations found in last {hours} hours")
                    
        except Exception as e:
            self.add_violation(
                "MEDIUM",
                f"Failed to check RLS usage logs: {str(e)}",
                {"error_type": type(e).__name__}
            )
    
    def check_endpoint_security(self):
        """Check if dangerous endpoints are properly disabled."""
        log_info("🔍 Checking API endpoint security...")
        
        try:
            # Check the actual endpoint code
            maintenance_file = Path(__file__).parent.parent.parent / "src" / "api" / "v1" / "endpoints" / "maintenance.py"
            
            if not maintenance_file.exists():
                self.add_violation(
                    "HIGH",
                    "Maintenance endpoint file not found",
                    {"expected_path": str(maintenance_file)}
                )
                return
            
            content = maintenance_file.read_text()
            
            # Check if the dangerous endpoint is disabled
            if 'status_code=503' in content and 'disabled for security reasons' in content:
                self.add_success("Dangerous RLS bypass endpoint is properly disabled")
            else:
                self.add_violation(
                    "CRITICAL",
                    "RLS bypass endpoint may still be accessible",
                    {"file": str(maintenance_file)}
                )
                
            # Check for security warnings in code
            if "SECURITY CRITICAL" in content:
                self.add_success("Security warnings present in endpoint code")
            else:
                self.add_violation(
                    "MEDIUM",
                    "Insufficient security warnings in endpoint code",
                    {"file": str(maintenance_file)}
                )
                
        except Exception as e:
            self.add_violation(
                "HIGH",
                f"Failed to check endpoint security: {str(e)}",
                {"error_type": type(e).__name__}
            )
    
    def generate_report(self) -> Dict[str, Any]:
        """Generate a comprehensive security report."""
        
        # Calculate risk score
        critical_violations = len([v for v in self.violations if v["severity"] == "CRITICAL"])
        high_violations = len([v for v in self.violations if v["severity"] == "HIGH"])
        medium_violations = len([v for v in self.violations if v["severity"] == "MEDIUM"])
        
        risk_score = (critical_violations * 10) + (high_violations * 5) + (medium_violations * 2)
        
        if risk_score == 0:
            risk_level = "LOW"
        elif risk_score <= 10:
            risk_level = "MEDIUM"
        elif risk_score <= 25:
            risk_level = "HIGH"
        else:
            risk_level = "CRITICAL"
        
        report = {
            "timestamp": datetime.now(timezone.utc).isoformat(),
            "summary": {
                "risk_level": risk_level,
                "risk_score": risk_score,
                "checks_passed": self.checks_passed,
                "checks_failed": self.checks_failed,
                "total_violations": len(self.violations)
            },
            "violations_by_severity": {
                "critical": critical_violations,
                "high": high_violations,
                "medium": medium_violations
            },
            "violations": self.violations,
            "recommendations": self._get_recommendations()
        }
        
        return report
    
    def _get_recommendations(self) -> List[str]:
        """Get security recommendations based on findings."""
        recommendations = []
        
        critical_violations = [v for v in self.violations if v["severity"] == "CRITICAL"]
        if critical_violations:
            recommendations.append("🔴 IMMEDIATE ACTION REQUIRED: Address all CRITICAL security violations immediately")
            recommendations.append("🔒 Disable all network access to RLS bypass functionality")
            recommendations.append("🚨 Review all recent RLS bypass operations for unauthorized access")
        
        high_violations = [v for v in self.violations if v["severity"] == "HIGH"]
        if high_violations:
            recommendations.append("🟠 HIGH PRIORITY: Implement additional access controls for SECURITY DEFINER functions")
            recommendations.append("📊 Set up continuous monitoring for RLS bypass operations")
        
        if not critical_violations and not high_violations:
            recommendations.append("✅ Security posture is acceptable, continue regular monitoring")
            recommendations.append("📋 Schedule weekly security checks for RLS bypass usage")
        
        return recommendations


async def main():
    """Main execution function."""
    parser = argparse.ArgumentParser(description="RLS Security Compliance Checker")
    parser.add_argument("--check-logs", action="store_true", help="Check recent audit logs for RLS usage")
    parser.add_argument("--verify-endpoints", action="store_true", help="Verify endpoint security")
    parser.add_argument("--hours", type=int, default=24, help="Hours to look back for log analysis")
    parser.add_argument("--output", type=str, help="Output file for JSON report")
    
    args = parser.parse_args()
    
    checker = RLSSecurityChecker()
    
    log_info("🔒 Starting RLS Security Compliance Check")
    log_info("=" * 60)
    
    # Always check database functions
    await checker.check_database_functions()
    
    # Check endpoints if requested
    if args.verify_endpoints:
        checker.check_endpoint_security()
    
    # Check logs if requested
    if args.check_logs:
        await checker.check_recent_rls_usage(args.hours)
    
    # Generate report
    report = checker.generate_report()
    
    # Print summary
    log_info("=" * 60)
    log_info("🔒 SECURITY COMPLIANCE REPORT")
    log_info("=" * 60)
    
    print(f"🎯 RISK LEVEL: {report['summary']['risk_level']}")
    print(f"📊 RISK SCORE: {report['summary']['risk_score']}")
    print(f"✅ CHECKS PASSED: {report['summary']['checks_passed']}")
    print(f"❌ CHECKS FAILED: {report['summary']['checks_failed']}")
    print(f"⚠️  TOTAL VIOLATIONS: {report['summary']['total_violations']}")
    
    if report['violations']:
        print("\n🚨 SECURITY VIOLATIONS:")
        for violation in report['violations']:
            print(f"  {violation['severity']}: {violation['message']}")
    
    print("\n📋 RECOMMENDATIONS:")
    for rec in report['recommendations']:
        print(f"  {rec}")
    
    # Save report if requested
    if args.output:
        with open(args.output, 'w') as f:
            json.dump(report, f, indent=2, default=str)
        log_info(f"Report saved to {args.output}")
    
    # Exit with appropriate code
    if report['summary']['risk_level'] in ['CRITICAL', 'HIGH']:
        log_error("🔴 SECURITY COMPLIANCE FAILED - Manual intervention required")
        sys.exit(1)
    else:
        log_info("✅ Security compliance check passed")
        sys.exit(0)


if __name__ == "__main__":
    asyncio.run(main()) 