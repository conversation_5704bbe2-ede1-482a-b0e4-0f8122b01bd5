"""
Celery tasks for ML training and related operations.
"""

from typing import Dict, Any, Optional
from datetime import datetime, timezone
from sqlalchemy.ext.asyncio import AsyncSession
from sqlalchemy.future import select
from asgiref.sync import async_to_sync
from celery.exceptions import MaxRetriesExceededError

from src.workers.celery_app import celery_app
from src.db.session import get_async_session_factory
from src.db.models import TrainingJob, ModelMetadata, BatchIngestionJob, Subscription
from src.db import schemas
from src.ml_pipeline.training_pipeline import TrainingPipeline
from src.ml_pipeline.model_artifact_manager import ModelArtifactManager
from src.ml_pipeline.metrics_tracker import MetricsTracker
from src.ml_pipeline.evaluation import RecommendationEvaluator
from src.utils.base_logger import log_info, log_error, log_warning
from src.utils.audit import write_audit_log_to_db
from src.core.exceptions import LimitExceededError
from src.utils.maintenance import cleanup_old_audit_logs as cleanup_audit_logs_async
from src.utils.maintenance import cleanup_old_interactions as cleanup_interactions_async
from src.utils.maintenance import cleanup_old_data_secure
from src.services.data_archival_service import DataArchivalService
from src.db.repositories import (
    ProductRepository,
    EndUserRepository,
    InteractionRepository,
    BatchIngestionJobRepository
)
from src.services.batch_data_storage_service import BatchDataStorageService
from src.utils.tenant_context import with_tenant_context_celery, run_with_tenant_context

# ============================================================================
# SECURITY CRITICAL: Enhanced Security Validation
# ============================================================================

import logging
security_logger = logging.getLogger("security.maintenance")

class SecurityViolationError(Exception):
    """Raised when a security violation is detected in maintenance tasks."""
    pass

def validate_task_security(
    task_id: str, 
    account_id: Optional[int], 
    task_name: str,
    require_account_id: bool = True
) -> None:
    """
    🚨 SECURITY CRITICAL: Validates security requirements for maintenance tasks.
    
    Args:
        task_id: Celery task ID for audit logging
        account_id: Tenant account ID 
        task_name: Name of the task being executed
        require_account_id: Whether account_id is mandatory
        
    Raises:
        SecurityViolationError: If security validation fails
    """
    # VALIDATION 1: account_id requirement for tenant-specific tasks
    if require_account_id:
        if account_id is None:
            error_msg = f"Task {task_id} ({task_name}): account_id is required for tenant-specific operations"
            security_logger.error(f"[SECURITY VIOLATION] {error_msg}")
            raise SecurityViolationError(error_msg)
        
        if not isinstance(account_id, int) or account_id <= 0:
            error_msg = f"Task {task_id} ({task_name}): Invalid account_id={account_id}. Must be positive integer."
            security_logger.error(f"[SECURITY VIOLATION] {error_msg}")
            raise SecurityViolationError(error_msg)
    
    # VALIDATION 2: Log security audit
    security_logger.warning(
        f"[SECURITY AUDIT] Maintenance task starting: "
        f"task_id={task_id}, task_name={task_name}, account_id={account_id}"
    )

# Inicializar los componentes para el pipeline de entrenamiento
artifact_manager = ModelArtifactManager()
metrics_tracker = MetricsTracker()
evaluator = RecommendationEvaluator()
training_pipeline = TrainingPipeline(
    artifact_manager=artifact_manager,
    metrics_tracker=metrics_tracker,
    evaluator=evaluator
)

# The write_audit_log Celery task has been removed in favor of using
# write_audit_log_to_db_task directly with FastAPI's BackgroundTasks.
#
# For API requests: AuditMiddleware automatically logs all requests
# For system events: Use SystemEventLogger
#
# Example:
#   logger = SystemEventLogger()
#   await logger.log_system_event(
#       account_id=account_id,
#       action="OPERATION_NAME",
#       entity_type="ENTITY_TYPE",
#       entity_id="entity_id",
#       success=True
#   )

# Ya no necesitamos el helper run_async porque usamos async_to_sync


@celery_app.task(name="cleanup_old_audit_logs", bind=True, max_retries=3, default_retry_delay=300, queue="maintenance")
@with_tenant_context_celery(account_id_arg_name='account_id')
def cleanup_old_audit_logs(self, days_to_keep: int = 90, batch_size: int = 10000, account_id: Optional[int] = None) -> Dict[str, Any]:
    """
    Celery task to clean up old audit logs.

    This task is idempotent and can be safely retried.

    Args:
        days_to_keep: Number of days to keep logs (default: 90)
        batch_size: Batch size for deletion (default: 10000)
        account_id: ID of the account (None for all accounts)

    Returns:
        Dictionary with cleanup results
    """
    try:
        task_id = self.request.id or "unknown"
        
        # 🚨 SECURITY CRITICAL: Validate task security
        validate_task_security(
            task_id=task_id,
            account_id=account_id,
            task_name="cleanup_old_audit_logs",
            require_account_id=True  # REQUIRE account_id for security
        )
        
        account_msg = f" for account {account_id}" if account_id else ""
        log_info(f"[Task {task_id}] Starting cleanup of audit logs older than {days_to_keep} days{account_msg} with batch size {batch_size}")

        # Si account_id no está establecido, verificar los metadatos de la tarea
        if account_id is None and 'account_id' in self.request.headers:
            account_id = self.request.headers.get('account_id')
            log_info(f"[Task {task_id}] Using account_id={account_id} from task metadata")

        # Run cleanup in async context
        start_time = datetime.now(timezone.utc)

        if account_id is not None:
            # If we have a specific account_id, use tenant context
            result = async_to_sync(run_with_tenant_context)(
                account_id=account_id,
                async_func=cleanup_audit_logs_async,
                days_to_keep=days_to_keep,
                batch_size=batch_size
            )
        else:
            # If we're cleaning up for all accounts, don't use tenant context
            result = async_to_sync(cleanup_audit_logs_async)(
                days_to_keep=days_to_keep,
                batch_size=batch_size,
                account_id=None
            )

        duration = (datetime.now(timezone.utc) - start_time).total_seconds()

        if result.get("success", False):
            log_info(f"[Task {task_id}] Completed cleanup of audit logs: {result.get('deleted_count', 0)}/{result.get('expected_count', 0)} in {duration:.2f}s")
        else:
            log_error(f"[Task {task_id}] Failed to clean up audit logs: {result.get('error', 'Unknown error')}")

        return result
    except Exception as e:
        log_error(f"[Task {self.request.id or 'unknown'}] Error in cleanup_old_audit_logs task: {str(e)}")
        # Retry the task with exponential backoff
        try:
            self.retry(exc=e, countdown=self.default_retry_delay * (2 ** self.request.retries))
        except MaxRetriesExceededError:
            log_error(f"[Task {self.request.id or 'unknown'}] Max retries exceeded for cleanup_old_audit_logs")

        return {
            "table": "audit_logs",
            "error": str(e),
            "success": False
        }


@celery_app.task(name="archive_and_cleanup_old_audit_logs", bind=True, max_retries=3, default_retry_delay=300, queue="maintenance")
@with_tenant_context_celery(account_id_arg_name='account_id')
def archive_and_cleanup_old_audit_logs(self, days_to_keep: int = 30, batch_size: int = 10000, account_id: Optional[int] = None) -> Dict[str, Any]:
    """
    Celery task to archive and then clean up old audit logs.

    This task first archives the data to GCS, then deletes it from Cloud SQL.
    This provides a cost-effective data retention strategy.

    Args:
        days_to_keep: Number of days to keep logs in Cloud SQL (default: 90)
        batch_size: Batch size for processing (default: 10000)
        account_id: ID of the account (None for all accounts)

    Returns:
        Dictionary with operation results
    """
    task_id = self.request.id or "unknown"

    try:
        log_info(f"[Task {task_id}] Starting archive and cleanup of audit logs older than {days_to_keep} days")

        # Run archival and cleanup in async context
        start_time = datetime.now(timezone.utc)

        if account_id is not None:
            # If we have a specific account_id, use tenant context
            result = async_to_sync(run_with_tenant_context)(
                account_id=account_id,
                async_func=archive_and_cleanup_audit_logs_async,
                days_to_keep=days_to_keep,
                batch_size=batch_size
            )
        else:
            # If we're processing all accounts, don't use tenant context
            result = async_to_sync(archive_and_cleanup_audit_logs_async)(
                days_to_keep=days_to_keep,
                batch_size=batch_size,
                account_id=None
            )

        duration = (datetime.now(timezone.utc) - start_time).total_seconds()

        if result.get("success", False):
            log_info(f"[Task {task_id}] Completed archive and cleanup of audit logs: "
                    f"archived {result.get('archived_count', 0)}, "
                    f"deleted {result.get('deleted_count', 0)} in {duration:.2f}s")
        else:
            log_error(f"[Task {task_id}] Failed to archive and cleanup audit logs: {result.get('error', 'Unknown error')}")

        return result
    except Exception as e:
        log_error(f"[Task {self.request.id or 'unknown'}] Error in archive_and_cleanup_old_audit_logs task: {str(e)}")
        # Retry the task with exponential backoff
        try:
            self.retry(exc=e, countdown=self.default_retry_delay * (2 ** self.request.retries))
        except MaxRetriesExceededError:
            log_error(f"[Task {self.request.id or 'unknown'}] Max retries exceeded for archive_and_cleanup_old_audit_logs")

        return {
            "table": "audit_logs",
            "error": str(e),
            "success": False
        }


@celery_app.task(name="cleanup_old_interactions", bind=True, max_retries=3, default_retry_delay=300, queue="maintenance")
@with_tenant_context_celery(account_id_arg_name='account_id')
def cleanup_old_interactions(
    self,
    days_to_keep: int = 180,
    account_id: Optional[int] = None,
    batch_size: int = 10000,
    max_retries: int = 3,
    retry_delay: int = 5
) -> Dict[str, Any]:
    """
    Celery task to clean up old interactions.

    This task is idempotent and can be safely retried.

    Args:
        days_to_keep: Number of days to keep interactions (default: 180)
        account_id: ID of the account (None for all accounts)
        batch_size: Batch size for deletion (default: 10000)
        max_retries: Maximum number of retries per batch (default: 3)
        retry_delay: Delay between retries in seconds (default: 5)

    Returns:
        Dictionary with cleanup results
    """
    try:
        task_id = self.request.id or "unknown"
        account_msg = f" for account {account_id}" if account_id else ""
        log_info(f"[Task {task_id}] Starting cleanup of interactions older than {days_to_keep} days{account_msg} with batch size {batch_size}")

        # Si account_id no está establecido, verificar los metadatos de la tarea
        if account_id is None and 'account_id' in self.request.headers:
            account_id = self.request.headers.get('account_id')
            log_info(f"[Task {task_id}] Using account_id={account_id} from task metadata")

        # Run cleanup in async context
        start_time = datetime.now(timezone.utc)

        if account_id is not None:
            # If we have a specific account_id, use tenant context
            result = async_to_sync(run_with_tenant_context)(
                account_id=account_id,
                async_func=cleanup_interactions_async,
                days_to_keep=days_to_keep,
                batch_size=batch_size,
                max_retries=max_retries,
                retry_delay=retry_delay
            )
        else:
            # If we're cleaning up for all accounts, don't use tenant context
            # The function will handle each account separately
            result = async_to_sync(cleanup_interactions_async)(
                days_to_keep=days_to_keep,
                account_id=None,
                batch_size=batch_size,
                max_retries=max_retries,
                retry_delay=retry_delay
            )

        duration = (datetime.now(timezone.utc) - start_time).total_seconds()

        if result.get("success", False):
            log_info(
                f"[Task {task_id}] Completed cleanup of interactions{account_msg}: "
                f"{result.get('deleted_count', 0)}/{result.get('expected_count', 0)} "
                f"in {duration:.2f}s"
            )
        else:
            log_error(
                f"[Task {task_id}] Failed to clean up interactions{account_msg}: "
                f"{result.get('error', 'Unknown error')}"
            )

        return result
    except Exception as e:
        log_error(f"[Task {self.request.id or 'unknown'}] Error in cleanup_old_interactions task: {str(e)}")
        # Retry the task with exponential backoff
        try:
            self.retry(exc=e, countdown=self.default_retry_delay * (2 ** self.request.retries))
        except MaxRetriesExceededError:
            log_error(f"[Task {self.request.id or 'unknown'}] Max retries exceeded for cleanup_old_interactions")

        return {
            "table": "interactions",
            "error": str(e),
            "account_id": account_id,
            "success": False
        }


@celery_app.task(name="cleanup_old_data_secure", bind=True, max_retries=3, default_retry_delay=300, queue="maintenance")
@with_tenant_context_celery(account_id_arg_name='account_id')
def cleanup_old_data_secure_task(
    self,
    days_to_keep: int = 90,  # Reduced from 180 to 90 days for cost optimization
    account_id: Optional[int] = None
) -> Dict[str, Any]:
    """
    ⚠️  SECURITY CRITICAL TASK ⚠️
    
    Celery task to clean up old data using the secure PostgreSQL function.

    This task uses the SECURITY DEFINER function in PostgreSQL to clean up old data
    for a specific account. It provides better security with parameter validation
    and protection against SQL injection.

    SECURITY RESTRICTIONS:
    - This task should ONLY be executed by internal cron jobs
    - Verify task origin and execution context before proceeding
    - Account ID is mandatory to prevent cross-tenant operations

    Args:
        days_to_keep: Number of days to keep data (default: 90)
        account_id: ID of the account (required)

    Returns:
        Dictionary with cleanup results
    """
    try:
        # SECURITY CHECK: Verify task is executed in secure context
        task_id = self.request.id or "unknown"
        
        # 🚨 SECURITY CRITICAL: Enhanced validation for RLS bypass operation
        validate_task_security(
            task_id=task_id,
            account_id=account_id,
            task_name="cleanup_old_data_secure_task",
            require_account_id=True
        )
        
        # Log security-critical operation
        log_warning(f"[SECURITY AUDIT] RLS bypass task {task_id} starting for account_id={account_id}")
        
        if account_id is None:
            log_error(f"[SECURITY VIOLATION] Task {task_id}: account_id is required for cleanup_old_data_secure_task")
            raise SecurityViolationError("account_id is required for cleanup_old_data_secure_task")

        # Additional validation for security
        if account_id <= 0:
            log_error(f"[SECURITY VIOLATION] Task {task_id}: Invalid account_id={account_id}")
            raise ValueError(f"Invalid account_id: {account_id}. Must be a positive integer.")

        log_info(f"[Task {task_id}] Starting secure cleanup of old data for account_id={account_id} with days_to_keep={days_to_keep}")

        # Run cleanup in async context with tenant context
        start_time = datetime.now(timezone.utc)

        # Use tenant context to run the secure cleanup function
        result = async_to_sync(run_with_tenant_context)(
            account_id=account_id,
            async_func=cleanup_old_data_secure,
            days_to_keep=days_to_keep
        )

        duration = (datetime.now(timezone.utc) - start_time).total_seconds()

        if result.get("success", False):
            log_info(
                f"[SECURITY AUDIT] Task {task_id} completed secure cleanup for account_id={account_id}: "
                f"Deleted {result.get('deleted_interactions', 0)} interactions, "
                f"Archived {result.get('archived_products', 0)} products, "
                f"Inactivated {result.get('inactivated_users', 0)} users "
                f"in {duration:.2f}s"
            )
        else:
            log_error(
                f"[SECURITY AUDIT] Task {task_id} failed cleanup for account_id={account_id}: "
                f"{result.get('error', 'Unknown error')}"
            )

        return result
    except ValueError as e:
        log_error(f"[SECURITY VIOLATION] Task {self.request.id or 'unknown'}: Invalid parameters for cleanup_old_data_secure_task: {str(e)}")
        raise
    except Exception as e:
        log_error(f"[SECURITY AUDIT] Task {self.request.id or 'unknown'}: Error in cleanup_old_data_secure_task: {str(e)}")
        # Retry the task with exponential backoff
        try:
            self.retry(exc=e, countdown=self.default_retry_delay * (2 ** self.request.retries))
        except MaxRetriesExceededError:
            log_error(f"[SECURITY AUDIT] Task {self.request.id or 'unknown'}: Max retries exceeded for cleanup_old_data_secure_task")

        return {
            "account_id": account_id,
            "error": str(e),
            "success": False,
            "timestamp": datetime.now(timezone.utc).isoformat()
        }


@celery_app.task(name="archive_and_cleanup_old_interactions", bind=True, max_retries=3, default_retry_delay=300, queue="maintenance")
@with_tenant_context_celery(account_id_arg_name='account_id')
def archive_and_cleanup_old_interactions(
    self,
    days_to_keep: int = 60,  # Reduced from 180 to 60 days for cost optimization
    account_id: Optional[int] = None,
    batch_size: int = 10000
) -> Dict[str, Any]:
    """
    Celery task to archive and then clean up old interactions.

    This task first archives the data to GCS, then deletes it from Cloud SQL.
    This provides a cost-effective data retention strategy.

    Args:
        days_to_keep: Number of days to keep interactions in Cloud SQL (default: 180)
        account_id: ID of the account (None for all accounts)
        batch_size: Batch size for processing (default: 10000)

    Returns:
        Dictionary with operation results
    """
    task_id = self.request.id or "unknown"

    try:
        log_info(f"[Task {task_id}] Starting archive and cleanup of interactions older than {days_to_keep} days")

        # Import here to avoid circular dependencies
        from src.utils.maintenance import archive_and_cleanup_interactions_async

        # Run archival and cleanup in async context
        start_time = datetime.now(timezone.utc)

        if account_id is not None:
            # If we have a specific account_id, use tenant context
            result = async_to_sync(run_with_tenant_context)(
                account_id=account_id,
                async_func=archive_and_cleanup_interactions_async,
                days_to_keep=days_to_keep,
                batch_size=batch_size
            )
        else:
            # If we're processing all accounts, don't use tenant context
            result = async_to_sync(archive_and_cleanup_interactions_async)(
                days_to_keep=days_to_keep,
                batch_size=batch_size,
                account_id=None
            )

        duration = (datetime.now(timezone.utc) - start_time).total_seconds()

        if result.get("success", False):
            log_info(f"[Task {task_id}] Completed archive and cleanup of interactions: "
                    f"archived {result.get('archived_count', 0)}, "
                    f"deleted {result.get('deleted_count', 0)} in {duration:.2f}s")
        else:
            log_error(f"[Task {task_id}] Failed to archive and cleanup interactions: {result.get('error', 'Unknown error')}")

        return result
    except Exception as e:
        log_error(f"[Task {self.request.id or 'unknown'}] Error in archive_and_cleanup_old_interactions task: {str(e)}")
        # Retry the task with exponential backoff
        try:
            self.retry(exc=e, countdown=self.default_retry_delay * (2 ** self.request.retries))
        except MaxRetriesExceededError:
            log_error(f"[Task {self.request.id or 'unknown'}] Max retries exceeded for archive_and_cleanup_old_interactions")

        return {
            "table": "interactions",
            "error": str(e),
            "success": False
        }


@celery_app.task(name="cleanup_soft_deleted_records", bind=True, max_retries=3, default_retry_delay=300, queue="maintenance")
@with_tenant_context_celery(account_id_arg_name='account_id')
def cleanup_soft_deleted_records(
    self,
    retention_days: int = 90,  # Reduced from 365 to 90 days for cost optimization
    account_id: Optional[int] = None,
    dry_run: bool = False
) -> Dict[str, Any]:
    """
    Celery task to clean up soft deleted records that have exceeded the final retention period.

    This task identifies and permanently deletes (or archives then deletes) records that have
    is_active = FALSE and deleted_at older than the defined threshold.

    Args:
        retention_days: Number of days to retain after soft delete (default: 365)
        account_id: ID of the account (None for all accounts)
        dry_run: If True, only reports what would be deleted without making changes

    Returns:
        Dictionary with operation results
    """
    task_id = self.request.id or "unknown"

    try:
        log_info(f"[Task {task_id}] Starting soft delete cleanup with {retention_days} days retention")

        # Import here to avoid circular dependencies
        from src.utils.maintenance import cleanup_soft_deleted_records_async

        # Run cleanup in async context
        start_time = datetime.now(timezone.utc)

        if account_id is not None:
            # If we have a specific account_id, use tenant context
            result = async_to_sync(run_with_tenant_context)(
                account_id=account_id,
                async_func=cleanup_soft_deleted_records_async,
                retention_days=retention_days,
                dry_run=dry_run
            )
        else:
            # If we're processing all accounts, don't use tenant context
            result = async_to_sync(cleanup_soft_deleted_records_async)(
                retention_days=retention_days,
                account_id=None,
                dry_run=dry_run
            )

        duration = (datetime.now(timezone.utc) - start_time).total_seconds()

        if result.get("success", False):
            log_info(f"[Task {task_id}] Completed soft delete cleanup: "
                    f"archived {result.get('total_archived', 0)}, "
                    f"deleted {result.get('total_deleted', 0)} records in {duration:.2f}s")
        else:
            log_error(f"[Task {task_id}] Failed soft delete cleanup: {result.get('errors', [])}")

        return result
    except Exception as e:
        log_error(f"[Task {self.request.id or 'unknown'}] Error in cleanup_soft_deleted_records task: {str(e)}")
        # Retry the task with exponential backoff
        try:
            self.retry(exc=e, countdown=self.default_retry_delay * (2 ** self.request.retries))
        except MaxRetriesExceededError:
            log_error(f"[Task {self.request.id or 'unknown'}] Max retries exceeded for cleanup_soft_deleted_records")

        return {
            "retention_days": retention_days,
            "account_id": account_id,
            "dry_run": dry_run,
            "total_archived": 0,
            "total_deleted": 0,
            "error": str(e),
            "success": False
        }


@celery_app.task(name="get_soft_delete_statistics", bind=True, max_retries=2, default_retry_delay=60, queue="maintenance")
@with_tenant_context_celery(account_id_arg_name='account_id')
def get_soft_delete_statistics(
    self,
    account_id: Optional[int] = None
) -> Dict[str, Any]:
    """
    Celery task to get statistics about soft deleted records.

    Args:
        account_id: ID of the account (None for all accounts)

    Returns:
        Dictionary with statistics by table
    """
    task_id = self.request.id or "unknown"

    try:
        log_info(f"[Task {task_id}] Getting soft delete statistics")

        # Import here to avoid circular dependencies
        from src.utils.maintenance import get_soft_delete_statistics_async

        # Run in async context
        if account_id is not None:
            # If we have a specific account_id, use tenant context
            result = async_to_sync(run_with_tenant_context)(
                account_id=account_id,
                async_func=get_soft_delete_statistics_async
            )
        else:
            # If we're processing all accounts, don't use tenant context
            result = async_to_sync(get_soft_delete_statistics_async)(
                account_id=None
            )

        log_info(f"[Task {task_id}] Retrieved soft delete statistics successfully")
        return result
    except Exception as e:
        log_error(f"[Task {self.request.id or 'unknown'}] Error in get_soft_delete_statistics task: {str(e)}")
        # Retry the task with exponential backoff
        try:
            self.retry(exc=e, countdown=self.default_retry_delay * (2 ** self.request.retries))
        except MaxRetriesExceededError:
            log_error(f"[Task {self.request.id or 'unknown'}] Max retries exceeded for get_soft_delete_statistics")

        return {
            "account_id": account_id,
            "error": str(e),
            "success": False
        }


@celery_app.task(name="monitor_high_volume_tables", queue="maintenance")
def monitor_high_volume_tables() -> Dict[str, Any]:
    """
    Celery task to monitor high volume tables.

    This is a system-wide task that doesn't operate on tenant-specific data,
    so it doesn't need tenant context.

    Returns:
        Dictionary with monitoring results
    """
    try:
        log_info("Starting monitoring of high volume tables")

        # Implementación real para monitorear tablas de alto volumen
        # En un entorno de producción, esto podría consultar estadísticas de tablas desde la base de datos
        # o usar herramientas de monitoreo específicas

        # Simulamos algunas estadísticas básicas
        current_time = datetime.now(timezone.utc)

        # Diccionario para almacenar resultados
        tables_stats = {
            "interactions": {
                "rows_estimate": "Pendiente implementación",
                "size_estimate": "Pendiente implementación",
                "last_vacuum": "Pendiente implementación",
                "last_analyze": "Pendiente implementación",
            },
            "audit_logs": {
                "rows_estimate": "Pendiente implementación",
                "size_estimate": "Pendiente implementación",
                "last_vacuum": "Pendiente implementación",
                "last_analyze": "Pendiente implementación",
            },
        }

        result = {
            "timestamp": current_time.isoformat(),
            "message": "Monitoreo de tablas de alto volumen completado",
            "tables": tables_stats,
            "success": True
        }

        log_info(f"Completed monitoring of high volume tables: {len(tables_stats)} tables checked")
        return result
    except Exception as e:
        log_error(f"Error in monitor_high_volume_tables task: {str(e)}")
        return {
            "timestamp": datetime.now(timezone.utc).isoformat(),
            "error": str(e),
            "success": False
        }


@celery_app.task(name="train_model", queue="training", time_limit=3600*4, soft_time_limit=3600*3.5)
@with_tenant_context_celery(account_id_arg_name='account_id')
def train_model(account_id: int, data: Dict[str, Any]) -> Dict[str, Any]:
    """
    Celery task to train ML models.

    Args:
        account_id: ID of the account
        data: Training data and parameters

    Returns:
        Dictionary with training results
    """
    try:
        log_info(f"Starting model training for account_id={account_id}")

        # Run training in async context with tenant context
        result = async_to_sync(run_with_tenant_context)(
            account_id=account_id,
            async_func=_train_model_async,
            data=data
        )

        log_info(f"Model training completed for account_id={account_id}")
        return result
    except LimitExceededError as e:
        log_error(f"Training limit exceeded for account_id={account_id}: {str(e)}")
        # Update training job status if possible
        try:
            async_to_sync(run_with_tenant_context)(
                account_id=account_id,
                async_func=_update_training_job_status,
                job_id=None,
                status="failed",
                error_message=f"Training limit exceeded: {str(e)}"
            )
        except Exception as inner_e:
            log_error(f"Error updating training job status: {str(inner_e)}")
        # Re-raise the exception
        raise
    except Exception as e:
        log_error(f"Error in train_model task for account_id={account_id}: {str(e)}")
        # Update training job status if possible
        try:
            async_to_sync(run_with_tenant_context)(
                account_id=account_id,
                async_func=_update_training_job_status,
                job_id=None,
                status="failed",
                error_message=str(e)
            )
        except Exception as inner_e:
            log_error(f"Error updating training job status: {str(inner_e)}")
        raise


@celery_app.task(name="train_model_for_job", queue="training", time_limit=3600*4, soft_time_limit=3600*3.5)
@with_tenant_context_celery(account_id_arg_name='account_id')
def train_model_for_job(account_id: int, job_id: int) -> Dict[str, Any]:
    """
    Celery task to train ML models for a specific training job.

    Args:
        account_id: ID of the account
        job_id: ID of the training job

    Returns:
        Dictionary with training results
    """
    try:
        log_info(
            f"Starting model training for account_id={account_id}, job_id={job_id}"
        )

        # Run training in async context with tenant context
        result = async_to_sync(run_with_tenant_context)(
            account_id=account_id,
            async_func=_train_model_for_job_async,
            job_id=job_id
        )

        log_info(
            f"Model training completed for account_id={account_id}, job_id={job_id}"
        )
        return result
    except LimitExceededError as e:
        log_error(f"Training limit exceeded for account_id={account_id}, job_id={job_id}: {str(e)}")
        # Update training job status
        try:
            async_to_sync(run_with_tenant_context)(
                account_id=account_id,
                async_func=_update_training_job_status,
                job_id=job_id,
                status="failed",
                error_message=f"Training limit exceeded: {str(e)}"
            )
        except Exception as inner_e:
            log_error(f"Error updating training job status: {str(inner_e)}")
        # Re-raise the exception
        raise
    except Exception as e:
        log_error(
            f"Error in train_model_for_job task for account_id={account_id}, job_id={job_id}: {str(e)}"
        )
        # Update training job status
        try:
            async_to_sync(run_with_tenant_context)(
                account_id=account_id,
                async_func=_update_training_job_status,
                job_id=job_id,
                status="failed",
                error_message=str(e)
            )
        except Exception as inner_e:
            log_error(f"Error updating training job status: {str(inner_e)}")
        raise


async def _train_model_async(
    db: AsyncSession, account_id: int, data: Dict[str, Any]
) -> Dict[str, Any]:
    """
    Asynchronous function to train ML models.

    Args:
        db: Database session
        account_id: ID of the account
        data: Training data and parameters

    Returns:
        Dictionary with training results
    """
    # Note: tenant context is now set by the run_with_tenant_context function

    async with db as session:
        async with session.begin():
            # Create a training job record
            training_job = TrainingJob(
                account_id=account_id,
                status="running",
                started_at=datetime.now(timezone.utc),
                parameters=data.get("parameters", {}),
            )
            session.add(training_job)
            await session.flush()
            job_id = training_job.id

            try:
                # Extract sampling parameters if provided
                max_interactions = data.get("max_interactions", 100000)
                sample_size = data.get("sample_size", 50000)
                days_limit = data.get("days_limit", 90)
                interaction_types = data.get("interaction_types")

                # Train models with sampling parameters  
                # Note: validate_limits=False because validation is done in the API endpoint
                result = await training_pipeline.train(
                    db=session,
                    account_id=account_id,
                    data=data,
                    max_interactions=max_interactions,
                    sample_size=sample_size,
                    days_limit=days_limit,
                    interaction_types=interaction_types,
                    validate_limits=False,
                )

                # Update training job status
                training_job.status = "completed"
                training_job.completed_at = datetime.now(timezone.utc)
                training_job.metrics = result.get("metrics", {})

                # Update last_successful_training_at in subscription
                # Get the subscription for this account
                stmt = select(Subscription).where(Subscription.account_id == account_id)
                subscription_result = await session.execute(stmt)
                subscription = subscription_result.scalar_one_or_none()

                if subscription:
                    # Update the last successful training time
                    subscription.last_successful_training_at = datetime.now(timezone.utc)
                    log_info(f"Updated last_successful_training_at for account {account_id}")

                # Get the model metadata ID
                stmt = (
                    select(ModelMetadata.id)
                    .where(ModelMetadata.account_id == account_id)
                    .order_by(ModelMetadata.created_at.desc())
                    .limit(1)
                )
                model_metadata_result = await session.execute(stmt)
                model_metadata_id = model_metadata_result.scalar_one_or_none()

                if model_metadata_id:
                    training_job.model_metadata_id = model_metadata_id

                return {
                    "job_id": job_id,
                    "account_id": account_id,
                    "status": "completed",
                    "metrics": result.get("metrics", {}),
                }
            except Exception as e:
                # Update training job status
                training_job.status = "failed"
                training_job.error_message = str(e)
                training_job.completed_at = datetime.now(timezone.utc)
                raise


async def _train_model_for_job_async(
    db: AsyncSession, account_id: int, job_id: int
) -> Dict[str, Any]:
    """
    Asynchronous function to train ML models for a specific training job.

    Args:
        db: Database session
        account_id: ID of the account
        job_id: ID of the training job

    Returns:
        Dictionary with training results
    """
    async with db as session:
        async with session.begin():
            # Get the training job
            training_job = await session.get(TrainingJob, job_id)
            if not training_job:
                raise ValueError(f"Training job {job_id} not found")

            # Update status to running
            training_job.status = "running"
            training_job.started_at = datetime.now(timezone.utc)
            await session.flush()

            try:
                # Get training data
                # In a real implementation, you might need to fetch or prepare the data
                # For now, we'll use empty data and let the model_manager handle it
                data = training_job.parameters or {}

                # Extract sampling parameters if provided in job parameters
                params = data if isinstance(data, dict) else {}
                max_interactions = params.get("max_interactions", 100000)
                sample_size = params.get("sample_size", 50000)
                days_limit = params.get("days_limit", 90)
                interaction_types = params.get("interaction_types")

                # Train models with sampling parameters
                # Note: validate_limits=False because validation is done in the API endpoint
                result = await training_pipeline.train(
                    db=session,
                    account_id=account_id,
                    data=params,
                    max_interactions=max_interactions,
                    sample_size=sample_size,
                    days_limit=days_limit,
                    interaction_types=interaction_types,
                    validate_limits=False,
                )

                # Update training job status
                training_job.status = "completed"
                training_job.completed_at = datetime.now(timezone.utc)
                training_job.metrics = result.get("metrics", {})

                # Update last_successful_training_at in subscription
                # Get the subscription for this account
                stmt = select(Subscription).where(Subscription.account_id == account_id)
                subscription_result = await session.execute(stmt)
                subscription = subscription_result.scalar_one_or_none()

                if subscription:
                    # Update the last successful training time
                    subscription.last_successful_training_at = datetime.now(timezone.utc)
                    log_info(f"Updated last_successful_training_at for account {account_id}")

                # Get the model metadata ID
                stmt = (
                    select(ModelMetadata.id)
                    .where(ModelMetadata.account_id == account_id)
                    .order_by(ModelMetadata.created_at.desc())
                    .limit(1)
                )
                model_metadata_result = await session.execute(stmt)
                model_metadata_id = model_metadata_result.scalar_one_or_none()

                if model_metadata_id:
                    training_job.model_metadata_id = model_metadata_id

                return {
                    "job_id": job_id,
                    "account_id": account_id,
                    "status": "completed",
                    "metrics": result.get("metrics", {}),
                }
            except Exception as e:
                # Update training job status
                training_job.status = "failed"
                training_job.error_message = str(e)
                training_job.completed_at = datetime.now(timezone.utc)
                raise


async def _update_training_job_status(
    account_id: int,
    job_id: Optional[int],
    status: str,
    error_message: Optional[str] = None,
) -> None:
    """
    Update the status of a training job.

    Args:
        account_id: ID of the account
        job_id: ID of the training job (optional)
        status: New status
        error_message: Error message (optional)
    """
    if not job_id:
        return

    # Obtener factory de sesiones
    session_factory = await get_async_session_factory()

    # Crear una nueva sesión
    async with session_factory() as session:
        async with session.begin():
            training_job = await session.get(TrainingJob, job_id)
            if training_job:
                training_job.status = status
                if error_message:
                    training_job.error_message = error_message
                if status in ["completed", "failed"]:
                    training_job.completed_at = datetime.now(timezone.utc)

                log_info(f"Updated training job {job_id} status to {status}")


@celery_app.task(name="process_batch_data", bind=True, max_retries=3, queue="batch_processing", time_limit=3600*3, soft_time_limit=3600*2.5)
@with_tenant_context_celery(account_id_arg_name='account_id')
def process_batch_data(self, account_id: int, job_id: int, file_path: str) -> Dict[str, Any]:
    """
    Celery task to process batch data ingestion.

    Args:
        account_id: ID of the account
        job_id: ID of the batch ingestion job
        file_path: Path to the file containing the batch data in GCS or local filesystem

    Returns:
        Dictionary with processing results
    """
    try:
        log_info(f"Starting batch data processing for account_id={account_id}, job_id={job_id}, file_path={file_path}")

        # Create storage service
        storage_service = BatchDataStorageService()

        # Run processing in async context with tenant context
        result = async_to_sync(run_with_tenant_context)(
            account_id=account_id,
            async_func=_process_batch_data_async,
            job_id=job_id,
            file_path=file_path,
            storage_service=storage_service
        )

        log_info(f"Batch data processing completed for account_id={account_id}, job_id={job_id}")
        return result
    except Exception as e:
        log_error(f"Error in process_batch_data task for account_id={account_id}, job_id={job_id}: {str(e)}")
        # Update job status if possible
        try:
            async_to_sync(run_with_tenant_context)(
                account_id=account_id,
                async_func=_update_batch_job_status,
                job_id=job_id,
                status="failed",
                error_message=str(e)
            )
        except Exception as inner_e:
            log_error(f"Error updating batch job status: {str(inner_e)}")

        # Retry the task if it's not the final retry
        if self.request.retries < self.max_retries:
            log_info(f"Retrying batch data processing for job_id={job_id} (retry {self.request.retries + 1})")
            raise self.retry(exc=e, countdown=60 * (2 ** self.request.retries))  # Exponential backoff
        raise


async def _process_batch_data_async(
    db: AsyncSession, account_id: int, job_id: int, file_path: str, storage_service: BatchDataStorageService
) -> Dict[str, Any]:
    """
    Asynchronous function to process batch data.

    Args:
        db: Database session
        account_id: ID of the account
        job_id: ID of the batch ingestion job
        file_path: Path to the file containing the batch data
        storage_service: Service to retrieve and delete the batch data

    Returns:
        Dictionary with processing results
    """
    # Note: tenant context is now set by the run_with_tenant_context function

    async with db as session:
        async with session.begin():
            # Get the batch ingestion job
            job_repo = BatchIngestionJobRepository(session, account_id=account_id)
            batch_job = await job_repo.get_by_id(job_id)

            if not batch_job:
                raise ValueError(f"Batch ingestion job {job_id} not found")

            # Update status to processing
            batch_job.status = "processing"
            batch_job.started_at = datetime.now(timezone.utc)
            await session.flush()

            try:
                # Retrieve data from storage
                log_info(f"Retrieving batch data from {file_path} for account {account_id}, job {job_id}")
                data = await storage_service.retrieve_batch_data(file_path)

                # Initialize repositories
                product_repo = ProductRepository(session, account_id=account_id)
                user_repo = EndUserRepository(session, account_id=account_id)
                interaction_repo = InteractionRepository(session, account_id=account_id)

                # Inicializar contadores y registro de errores
                users_processed = 0
                products_processed = 0
                interactions_processed = 0
                errors = 0
                error_details = {}

                # Process users
                if data.get("users"):
                    users = data["users"]
                    try:
                        # Registrar tiempo de inicio para usuarios
                        user_start_time = datetime.now(timezone.utc)

                        # Actualizar estado del trabajo para mostrar progreso
                        batch_job.status = "processing_users"
                        batch_job.processed_count = {
                            "current_stage": "users",
                            "total_expected": {
                                "users": len(users),
                                "products": len(data.get("products", [])),
                                "interactions": len(data.get("interactions", []))
                            },
                            "processed_so_far": {
                                "users": 0,
                                "products": 0,
                                "interactions": 0
                            }
                        }
                        await session.flush()

                        # Procesar usuarios en lotes para mejor rendimiento y seguimiento
                        batch_size = 1000
                        for i in range(0, len(users), batch_size):
                            batch = users[i:i+batch_size]
                            await user_repo.bulk_upsert(batch, unique_columns=["external_id"])
                            users_processed += len(batch)

                            # Actualizar contador de progreso
                            if batch_job.processed_count:
                                batch_job.processed_count["processed_so_far"]["users"] = users_processed
                                await session.flush()

                        user_end_time = datetime.now(timezone.utc)
                        log_info(f"Processed {users_processed} users for account {account_id} in {(user_end_time - user_start_time).total_seconds():.2f} seconds")
                    except Exception as e:
                        log_error(f"Error processing users for account {account_id}: {str(e)}")
                        errors += 1
                        error_details["users"] = str(e)

                # Process products
                if data.get("products"):
                    products = data["products"]
                    try:
                        # Registrar tiempo de inicio para productos
                        product_start_time = datetime.now(timezone.utc)

                        # Actualizar estado del trabajo
                        batch_job.status = "processing_products"
                        if batch_job.processed_count:
                            batch_job.processed_count["current_stage"] = "products"
                        await session.flush()

                        # Procesar productos en lotes
                        batch_size = 1000
                        for i in range(0, len(products), batch_size):
                            batch = products[i:i+batch_size]
                            await product_repo.bulk_upsert(batch, unique_columns=["external_id"])
                            products_processed += len(batch)

                            # Actualizar contador de progreso
                            if batch_job.processed_count and "processed_so_far" in batch_job.processed_count:
                                batch_job.processed_count["processed_so_far"]["products"] = products_processed
                                await session.flush()

                        product_end_time = datetime.now(timezone.utc)
                        log_info(f"Processed {products_processed} products for account {account_id} in {(product_end_time - product_start_time).total_seconds():.2f} seconds")
                    except Exception as e:
                        log_error(f"Error processing products for account {account_id}: {str(e)}")
                        errors += 1
                        error_details["products"] = str(e)

                # Process interactions
                if data.get("interactions"):
                    interactions = data["interactions"]
                    try:
                        # Registrar tiempo de inicio para interacciones
                        interaction_start_time = datetime.now(timezone.utc)

                        # Actualizar estado del trabajo
                        batch_job.status = "processing_interactions"
                        if batch_job.processed_count:
                            batch_job.processed_count["current_stage"] = "interactions"
                        await session.flush()

                        # Procesar interacciones en lotes
                        batch_size = 5000  # Lotes más grandes para interacciones
                        for i in range(0, len(interactions), batch_size):
                            batch = interactions[i:i+batch_size]
                            await interaction_repo.bulk_create(batch)
                            interactions_processed += len(batch)

                            # Actualizar contador de progreso
                            if batch_job.processed_count and "processed_so_far" in batch_job.processed_count:
                                batch_job.processed_count["processed_so_far"]["interactions"] = interactions_processed
                                await session.flush()

                        interaction_end_time = datetime.now(timezone.utc)
                        log_info(f"Processed {interactions_processed} interactions for account {account_id} in {(interaction_end_time - interaction_start_time).total_seconds():.2f} seconds")
                    except Exception as e:
                        log_error(f"Error processing interactions for account {account_id}: {str(e)}")
                        errors += 1
                        error_details["interactions"] = str(e)

                # Update job status
                total_processed = users_processed + products_processed + interactions_processed
                total_expected = len(data.get("users", [])) + len(data.get("products", [])) + len(data.get("interactions", []))

                batch_job.status = "completed" if errors == 0 else "completed_with_errors"
                batch_job.completed_at = datetime.now(timezone.utc)
                batch_job.processed_count = {
                    "users": users_processed,
                    "products": products_processed,
                    "interactions": interactions_processed,
                    "total": total_processed,
                    "expected": total_expected,
                    "errors": errors,
                    "error_details": error_details
                }

                # Clean up the data file
                try:
                    log_info(f"Cleaning up batch data file {file_path} for account {account_id}, job {job_id}")
                    await storage_service.delete_batch_data(file_path)
                    log_info(f"Successfully deleted batch data file {file_path}")
                except Exception as e:
                    log_error(f"Error deleting batch data file {file_path}: {str(e)}")
                    # We don't want to fail the job if cleanup fails

                return {
                    "job_id": job_id,
                    "account_id": account_id,
                    "status": "completed",
                    "processed_count": batch_job.processed_count
                }
            except Exception as e:
                # Update job status
                batch_job.status = "failed"
                batch_job.error_message = str(e)
                batch_job.completed_at = datetime.now(timezone.utc)

                # Clean up the data file even if processing fails
                try:
                    log_info(f"Cleaning up batch data file {file_path} after error for account {account_id}, job {job_id}")
                    await storage_service.delete_batch_data(file_path)
                    log_info(f"Successfully deleted batch data file {file_path} after error")
                except Exception as cleanup_error:
                    log_error(f"Error deleting batch data file {file_path} after processing error: {str(cleanup_error)}")
                    # We don't want to mask the original error

                raise


async def _update_batch_job_status(
    account_id: int,
    job_id: int,
    status: str,
    error_message: Optional[str] = None,
) -> None:
    """
    Update the status of a batch ingestion job.

    Args:
        account_id: ID of the account
        job_id: ID of the batch ingestion job
        status: New status
        error_message: Error message (optional)
    """
    # Obtener factory de sesiones
    session_factory = await get_async_session_factory()

    # Crear una nueva sesión
    try:
        async with session_factory() as session:
            async with session.begin():
                job_repo = BatchIngestionJobRepository(session, account_id=account_id)
                batch_job = await job_repo.get_by_id(job_id)

                if batch_job:
                    batch_job.status = status
                    if error_message:
                        batch_job.error_message = error_message
                    if status in ["completed", "failed"]:
                        batch_job.completed_at = datetime.now(timezone.utc)

                    log_info(f"Updated batch ingestion job {job_id} status to {status}")

    except Exception as e:
        log_error(f"Error updating batch job status for job_id={job_id}: {str(e)}")
        # Don't raise the exception to avoid breaking the main processing flow


# ============================================================================
# SECURITY CRITICAL: Secure Per-Tenant Task Orchestration
# ============================================================================

@celery_app.task(name="schedule_per_tenant_cleanup", bind=True, max_retries=2, default_retry_delay=300)
def schedule_per_tenant_cleanup(
    self,
    target_task: str,
    task_kwargs: Dict[str, Any],
    require_security_validation: bool = True,
    enforce_tenant_isolation: bool = True,
    rls_bypass_operation: bool = False
) -> Dict[str, Any]:
    """
    🚨 SECURITY CRITICAL: Schedules maintenance tasks for all active tenants securely.
    
    This task ensures maintenance operations are executed per-tenant with proper isolation,
    preventing RLS bypass vulnerabilities.
    
    Args:
        target_task: Name of the task to schedule for each tenant
        task_kwargs: Keyword arguments for the task
        require_security_validation: Whether to enforce security validation
        enforce_tenant_isolation: Whether to enforce strict tenant isolation
        rls_bypass_operation: Whether this operation bypasses RLS (for enhanced logging)
        
    Returns:
        Dictionary with scheduling results
    """
    task_id = self.request.id or "unknown"
    
    try:
        # Security validation for orchestration task
        if require_security_validation:
            security_logger.warning(
                f"[SECURITY AUDIT] Per-tenant orchestration starting: "
                f"task_id={task_id}, target_task={target_task}, "
                f"rls_bypass={rls_bypass_operation}"
            )
        
        # Get list of active tenants
        from src.db.repositories.account import AccountRepository
        from src.db.session import get_db
        
        async def get_active_tenants():
            async with get_db() as db:
                repo = AccountRepository(db)
                accounts = await repo.get_active_accounts()
                return [account.account_id for account in accounts]
        
        active_tenants = async_to_sync(get_active_tenants)()
        
        if not active_tenants:
            log_warning(f"[Task {task_id}] No active tenants found for {target_task}")
            return {
                "total_tenants": 0,
                "scheduled_tasks": [],
                "security_compliance": "NO_TENANTS_FOUND",
                "target_task": target_task
            }
        
        # Schedule tasks for each tenant individually
        scheduled_tasks = []
        
        for tenant_id in active_tenants:
            try:
                # Prepare task arguments with tenant context
                tenant_kwargs = {**task_kwargs, "account_id": tenant_id}
                
                # Additional security validation for RLS bypass operations
                if rls_bypass_operation:
                    security_logger.critical(
                        f"[SECURITY CRITICAL] Scheduling RLS bypass task {target_task} "
                        f"for tenant_id={tenant_id} (orchestration_task={task_id})"
                    )
                
                # Schedule the task with tenant isolation
                task_result = celery_app.send_task(
                    target_task,
                    kwargs=tenant_kwargs,
                    queue="maintenance"
                )
                
                scheduled_tasks.append({
                    "tenant_id": tenant_id,
                    "task_id": task_result.id,
                    "target_task": target_task,
                    "security_validated": require_security_validation
                })
                
            except Exception as tenant_error:
                security_logger.error(
                    f"[SECURITY ERROR] Failed to schedule {target_task} "
                    f"for tenant_id={tenant_id}: {str(tenant_error)}"
                )
        
        # Compile results
        result = {
            "total_tenants": len(active_tenants),
            "successful_schedules": len(scheduled_tasks),
            "scheduled_tasks": scheduled_tasks,
            "security_compliance": (
                "RLS_BYPASS_SECURE" if rls_bypass_operation and enforce_tenant_isolation
                else "TENANT_ISOLATION_ENFORCED" if enforce_tenant_isolation
                else "STANDARD"
            ),
            "target_task": target_task,
            "orchestration_task_id": task_id,
            "timestamp": datetime.now(timezone.utc).isoformat()
        }
        
        # Final security audit log
        security_logger.info(
            f"[SECURITY AUDIT] Per-tenant orchestration completed: "
            f"task_id={task_id}, target_task={target_task}, "
            f"successful={len(scheduled_tasks)}/{len(active_tenants)}, "
            f"rls_bypass={rls_bypass_operation}"
        )
        
        return result
        
    except Exception as e:
        security_logger.error(
            f"[SECURITY ERROR] Per-tenant orchestration failed: "
            f"task_id={task_id}, target_task={target_task}, error={str(e)}"
        )
        
        return {
            "total_tenants": 0,
            "successful_schedules": 0,
            "error": str(e),
            "security_compliance": "FAILED",
            "target_task": target_task,
            "orchestration_task_id": task_id,
            "timestamp": datetime.now(timezone.utc).isoformat()
        }
