# Testing OpenAPI Generation

## Quick Test Instructions

Follow these steps to verify that the OpenAPI generation fix is working:

### Step 1: Start Backend Server

```bash
# Terminal 1: Start the backend
cd rayuela_backend
python main.py
```

**Expected Output:**

```
INFO:     Started server process [12345]
INFO:     Waiting for application startup.
INFO:     Application startup complete.
INFO:     Uvicorn running on http://127.0.0.1:8001 (Press CTRL+C to quit)
```

### Step 2: Test Backend Endpoints

```bash
# Test health endpoint
curl http://localhost:8001/health

# Expected: {"status":"healthy"}

# Test OpenAPI endpoint
curl http://localhost:8001/api/openapi.json | jq '.paths | keys | length'

# Expected: A number > 0 (indicating endpoints exist)
```

### Step 3: Generate API Client

```bash
# Terminal 2: Generate the API client
cd rayuela_frontend
npm run generate-api:dev
```

**Expected Output:**

```
🔄 Fetching OpenAPI specification from: http://localhost:8001/api/openapi.json
🔍 Checking server health at: http://localhost:8001/health
✅ Server health check passed: { status: 'healthy' }
📊 OpenAPI Validation Results:
   - OpenAPI Version: 3.1.0
   - API Title: Rayuela
   - API Version: 1.0.0
   - Number of paths: 25
   - Number of components: 15
✅ OpenAPI specification validation passed
✅ OpenAPI specification saved to: /path/to/openapi.json
📊 Summary: 25 endpoints, 15 schemas

🎉 OpenAPI specification fetched successfully!
```

### Step 4: Verify Generated Files

```bash
# Check OpenAPI file has content
cat src/lib/openapi/openapi.json | jq '.paths | keys | length'

# Check generated API client exists
ls -la src/lib/generated/

# Expected files:
# - api-client.ts
# - schemas/
# - index.ts
```

### Step 5: Run Comprehensive Tests

```bash
npm run test-openapi:verbose
```

**Expected Output:**

```
🧪 OpenAPI Generation Test Suite
=================================

✅ Backend Connectivity: Backend server is running at http://localhost:8001
✅ OpenAPI Endpoint: OpenAPI spec available with 25 paths and 15 schemas
✅ Fetch Script: OpenAPI fetch script executed successfully
✅ OpenAPI File: Valid OpenAPI file with 25 paths and 15 schemas
✅ Orval Generation: Orval API client generation completed
✅ Generated Files: Generated 12 TypeScript files including API client
✅ TypeScript Compilation: TypeScript compilation successful

📊 TEST SUMMARY
================
✅ Passed: 7/7 (100%)

🎉 All tests passed! OpenAPI generation is working correctly.
```

## Troubleshooting

### Backend Server Won't Start

**Problem**: Backend fails to start

**Solutions**:

1. Check Python dependencies: `pip install -r requirements.txt`
2. Check database connection
3. Check environment variables
4. Try alternative port: The server will automatically find an available port

### Empty OpenAPI Specification

**Problem**: OpenAPI has no paths

**Check**:

```bash
curl http://localhost:8001/api/openapi.json | jq '.paths'
```

**If empty (`{}`)**:

1. Verify routers are included in `main.py`
2. Check that endpoints have proper FastAPI decorators
3. Ensure imports are working correctly

### Frontend Generation Fails

**Problem**: `npm run generate-api:dev` fails

**Solutions**:

1. Ensure backend is running first
2. Check network connectivity: `curl http://localhost:8001/health`
3. Try force mode: `npm run generate-api:force`
4. Check Node.js dependencies: `npm install`

### TypeScript Compilation Errors

**Problem**: Generated code has TypeScript errors

**Solutions**:

1. Update TypeScript: `npm update typescript`
2. Check tsconfig.json configuration
3. Regenerate API client: `npm run generate-api:dev`

## Manual Verification

### Check OpenAPI Content

```bash
# View OpenAPI specification
cat src/lib/openapi/openapi.json | jq '.'

# Count endpoints
cat src/lib/openapi/openapi.json | jq '.paths | keys | length'

# List all endpoints
cat src/lib/openapi/openapi.json | jq '.paths | keys'

# Check schemas
cat src/lib/openapi/openapi.json | jq '.components.schemas | keys'
```

### Check Generated API Client

```bash
# List generated files
find src/lib/generated -name "*.ts" | head -10

# Check main API client
head -20 src/lib/generated/api-client.ts

# Check if schemas were generated
ls src/lib/generated/schemas/
```

### Test API Client Usage

Create a test file `test-api-client.ts`:

```typescript
import { authApi } from './src/lib/generated';

// This should have full TypeScript support
async function testApiClient() {
  try {
    // This should be fully typed
    const response = await authApi.login({
      username: '<EMAIL>',
      password: 'password',
    });

    console.log('API client working:', response.data);
  } catch (error) {
    console.error('API client error:', error);
  }
}
```

## Success Criteria

✅ **Backend Running**: Health endpoint returns `{"status":"healthy"}`

✅ **OpenAPI Available**: `/api/openapi.json` returns specification with paths

✅ **Fetch Script Works**: `npm run fetch-openapi:verbose` succeeds

✅ **Validation Passes**: OpenAPI spec has > 0 paths and schemas

✅ **Generation Works**: `orval` generates TypeScript files

✅ **Files Created**: API client and schema files exist

✅ **TypeScript Compiles**: No compilation errors

✅ **Test Suite Passes**: All tests in `npm run test-openapi` pass

## Next Steps After Success

1. **Use Generated Client**: Replace manual API calls with generated client
2. **Add to CI/CD**: Include `npm run test-openapi` in your CI pipeline
3. **Update Documentation**: Document the new API-first workflow
4. **Train Team**: Ensure all developers understand the new process

## Getting Help

If you encounter issues:

1. **Check Logs**: Use `--verbose` flags for detailed output
2. **Read Documentation**: See `OPENAPI_DEVELOPMENT_GUIDE.md`
3. **Test Components**: Use `npm run test-openapi` to isolate issues
4. **Manual Testing**: Use curl to test backend endpoints directly

## Common Commands Reference

```bash
# Development workflow
npm run generate-api:dev        # Generate with validation
npm run test-openapi:verbose    # Test everything
npm run fetch-openapi -- --help # Get help

# Troubleshooting
npm run fetch-openapi:force     # Force minimal spec
npm run generate-api:force      # Force generation
curl http://localhost:8001/health # Test backend

# Production
npm run build                   # Uses prebuild with force mode
```
