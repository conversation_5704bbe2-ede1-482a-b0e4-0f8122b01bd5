"use client";

import React from 'react';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { BillingPortalButton } from '@/components/dashboard/BillingPortalButton';
import type { UsageStats, PlanInfo } from '@/lib/generated/rayuelaAPI';
import type { AccountInfo } from '@/lib/api';
import {
  getCurrentPlan,
  getPlanDisplayName,
  formatCurrency,
  formatUsageMetric
} from '@/lib/utils/billing';
import {
  CreditCardIcon,
  CalendarIcon,
  TrendingUpIcon,
  DatabaseIcon,
  ExternalLinkIcon
} from 'lucide-react';

interface CurrentPlanCardProps {
  accountData: AccountInfo | null;
  usageData: UsageStats | null;
  plans: Record<string, PlanInfo> | null;
  isLoading: boolean;
}

export function CurrentPlanCard({ accountData, usageData, plans, isLoading }: CurrentPlanCardProps) {
  if (isLoading) {
    return (
      <Card>
        <CardHeader>
          <CardTitle>Plan Actual</CardTitle>
          <CardDescription>Información de tu suscripción actual</CardDescription>
        </CardHeader>
        <CardContent>
          <div className="animate-pulse">
            <div className="h-4 bg-gray-200 rounded w-1/2 mb-2"></div>
            <div className="h-4 bg-gray-200 rounded w-1/3"></div>
          </div>
        </CardContent>
      </Card>
    );
  }

  const currentPlan = getCurrentPlan(accountData);
  const planInfo = plans?.[currentPlan || ''];

  return (
    <Card>
      <CardHeader>
        <CardTitle className="flex items-center justify-between">
          Plan Actual
          <Badge variant={currentPlan === 'free' ? 'secondary' : 'default'}>
            {getPlanDisplayName(currentPlan || '')}
          </Badge>
        </CardTitle>
        <CardDescription>Información de tu suscripción actual</CardDescription>
      </CardHeader>
      <CardContent className="space-y-4">
        {planInfo && (
          <>
            <div>
              <h4 className="font-medium">{planInfo.name}</h4>
              <p className="text-sm text-gray-600 dark:text-gray-400">{planInfo.description}</p>
            </div>

            <div className="flex justify-between items-center">
              <span className="text-sm">Precio:</span>
              <span className="font-medium">{planInfo.price}</span>
            </div>

            {usageData && planInfo.limits && (
              <div className="space-y-2">
                <h5 className="font-medium text-sm">Uso actual:</h5>
                <div className="text-xs space-y-1">
                  {Object.entries(planInfo.limits).map(([key, limit]) => {
                    const usage = (usageData as any)[key] || 0;
                    return (
                      <div key={key} className="flex justify-between">
                        <span>{formatUsageMetric(key)}:</span>
                        <span>{usage.toString()} / {limit?.toString() || 'Ilimitado'}</span>
                      </div>
                    );
                  })}
                </div>
              </div>
            )}
          </>
        )}

        <div className="pt-4">
          <BillingPortalButton />
        </div>
      </CardContent>
    </Card>
  );
}
