"""
Tests de integración para verificar que la restricción de unicidad global 
de email a nivel de base de datos previene condiciones de carrera.
"""

import pytest
import asyncio
from unittest.mock import patch
from sqlalchemy.ext.asyncio import AsyncSession
from sqlalchemy.exc import IntegrityError
from fastapi import HTTPException

from src.services.auth_service import AuthService
from src.db.models import SystemUser, Account
from src.db.schemas import SystemUserCreate
from src.db.repositories import SystemUserRepository


class TestGlobalEmailUniquenessConstraint:
    """Tests para verificar la restricción de unicidad global de email a nivel de DB."""

    @pytest.mark.asyncio
    async def test_database_constraint_prevents_duplicate_emails(
        self, db_session: AsyncSession, sample_accounts
    ):
        """
        Test que verifica que la restricción de base de datos previene emails duplicados
        incluso en condiciones de carrera.
        """
        auth_service = AuthService(db_session)
        email = "<EMAIL>"
        
        account1, account2 = sample_accounts[:2]
        
        # Crear primer usuario con el email
        user_repo1 = SystemUserRepository(db_session, account1.account_id)
        user_data1 = SystemUserCreate(email=email, password="password123")
        
        user1 = await user_repo1.create(user_data1)
        await db_session.commit()
        
        # Intentar crear segundo usuario con el mismo email en diferente cuenta
        user_repo2 = SystemUserRepository(db_session, account2.account_id)
        user_data2 = SystemUserCreate(email=email, password="password456")
        
        # Esto debe fallar por la restricción de unicidad global
        with pytest.raises(IntegrityError) as exc_info:
            await user_repo2.create(user_data2)
            await db_session.commit()
        
        # Verificar que el error contiene referencias al constraint de email
        error_message = str(exc_info.value)
        assert "email" in error_message.lower() or "unique" in error_message.lower()

    @pytest.mark.asyncio  
    async def test_auth_service_handles_constraint_violation_gracefully(
        self, db_session: AsyncSession
    ):
        """
        Test que verifica que AuthService maneja violaciones de restricción de forma elegante.
        """
        auth_service = AuthService(db_session)
        
        # Registrar primera cuenta
        result1 = await auth_service.register_account(
            account_name="Company 1",
            email="<EMAIL>", 
            password="password123"
        )
        
        assert "access_token" in result1
        
        # Intentar registrar segunda cuenta con el mismo email
        with pytest.raises(HTTPException) as exc_info:
            await auth_service.register_account(
                account_name="Company 2",
                email="<EMAIL>",
                password="password456" 
            )
        
        # Verificar que se retorna el error correcto
        assert exc_info.value.status_code == 400
        assert "Email already exists globally" in exc_info.value.detail

    @pytest.mark.asyncio
    async def test_concurrent_registration_race_condition_prevention(
        self, db_session: AsyncSession
    ):
        """
        Test que simula condiciones de carrera y verifica que la restricción DB las previene.
        """
        auth_service = AuthService(db_session)
        email = "<EMAIL>"
        
        async def register_account(name_suffix: str):
            """Función auxiliar para registro concurrente."""
            try:
                return await auth_service.register_account(
                    account_name=f"Company {name_suffix}",
                    email=email,
                    password="password123"
                )
            except HTTPException as e:
                return {"error": e.detail, "status_code": e.status_code}
        
        # Ejecutar registros concurrentes
        tasks = [
            register_account("A"),
            register_account("B"), 
            register_account("C")
        ]
        
        results = await asyncio.gather(*tasks, return_exceptions=True)
        
        # Verificar que solo uno tuvo éxito
        successful_registrations = [
            r for r in results 
            if isinstance(r, dict) and "access_token" in r
        ]
        failed_registrations = [
            r for r in results 
            if isinstance(r, dict) and "error" in r
        ]
        
        assert len(successful_registrations) == 1, "Solo una cuenta debe registrarse exitosamente"
        assert len(failed_registrations) >= 1, "Al menos una debe fallar por email duplicado"
        
        # Verificar que los fallos son por email duplicado
        for failed in failed_registrations:
            assert failed["status_code"] == 400
            assert "Email already exists globally" in failed["error"]

    @pytest.mark.asyncio
    async def test_login_still_works_with_global_uniqueness(
        self, db_session: AsyncSession
    ):
        """
        Test que verifica que el login sigue funcionando correctamente con unicidad global.
        """
        auth_service = AuthService(db_session)
        
        # Registrar cuenta
        email = "<EMAIL>"
        password = "securepassword123"
        
        registration_result = await auth_service.register_account(
            account_name="Test Company",
            email=email,
            password=password
        )
        
        # Intentar login
        with patch.object(auth_service, "_get_active_subscription", return_value=None):
            login_result = await auth_service.login(email, password)
        
        assert login_result["access_token"]
        assert login_result["account_id"] == registration_result["account_id"]
        assert login_result["user_id"] == registration_result["user_id"]

    @pytest.mark.asyncio
    async def test_email_uniqueness_is_case_sensitive(
        self, db_session: AsyncSession
    ):
        """
        Test que verifica que la unicidad de email es sensible a mayúsculas/minúsculas.
        """
        auth_service = AuthService(db_session)
        
        # Registrar con email en minúsculas
        result1 = await auth_service.register_account(
            account_name="Company 1", 
            email="<EMAIL>",
            password="password123"
        )
        
        assert "access_token" in result1
        
        # Intentar registrar con email en mayúsculas (debe fallar si DB es case-sensitive)
        # Nota: PostgreSQL por defecto es case-sensitive para UNIQUE constraints
        with pytest.raises(HTTPException) as exc_info:
            await auth_service.register_account(
                account_name="Company 2",
                email="<EMAIL>", 
                password="password456"
            )
        
        # Si el test falla aquí, significa que el DB no está siendo case-sensitive
        # En ese caso, el comportamiento depende de la configuración del DB
        assert exc_info.value.status_code == 400 