# Build stage
FROM python:3.12-slim AS builder

WORKDIR /app

# Install system dependencies for building wheels
RUN apt-get update && apt-get install -y --no-install-recommends \
    build-essential \
    gcc \
    g++ \
    gfortran \
    libgomp1 \
    libopenblas-dev \
    && rm -rf /var/lib/apt/lists/*

# Copy requirements and create wheels
COPY requirements.txt .
RUN pip wheel --no-cache-dir --no-deps --wheel-dir /app/wheels -r requirements.txt

# Final stage - optimized for production
FROM python:3.12-slim

# Install only runtime dependencies - no build tools
# Note: libpq-dev is not needed at runtime since we're using pre-built wheels
RUN apt-get update && apt-get install -y --no-install-recommends \
    libgomp1 \
    libopenblas0 \
    curl \
    && apt-get clean \
    && rm -rf /var/lib/apt/lists/*

# Create non-root user
RUN useradd -m -u 1000 workeruser

WORKDIR /app

# Copy wheels and requirements from builder stage
COPY --from=builder /app/wheels /wheels
COPY --from=builder /app/requirements.txt .

# Install dependencies from pre-built wheels and clean up
RUN pip install --no-cache-dir --upgrade pip && \
    pip install --no-cache-dir /wheels/* && \
    pip install --no-cache-dir asgiref==3.8.1 && \
    pip install --no-cache-dir itsdangerous && \
    rm -rf /wheels /root/.cache/pip/*

# Copy only necessary application code
COPY ./src ./src
COPY ./alembic ./alembic
COPY ./alembic.ini .

# Change ownership of files
RUN chown -R workeruser:workeruser /app

# Switch to non-root user
USER workeruser

# Environment variables for production
ENV ENV=production \
    PYTHONUNBUFFERED=1 \
    PYTHONDONTWRITEBYTECODE=1

# Copy health server and startup script
COPY ./health_server.py /app/
COPY ./start.sh /app/

# Install aiohttp for health server
RUN pip install --no-cache-dir aiohttp

# Make startup script executable
RUN chmod +x /app/start.sh

# Health check for worker
HEALTHCHECK CMD curl -f http://localhost:8080/health || exit 1

# Default command to run both health server and Celery worker
CMD ["/app/start.sh"]
