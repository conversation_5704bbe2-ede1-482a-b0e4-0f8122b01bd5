/**
 * Helper functions for migrating from manual API client to generated client
 */

// API Base URL configuration
const API_BASE_URL = process.env.NEXT_PUBLIC_API_URL || "http://localhost:8001";

/**
 * Create configuration for the generated API client
 */
export const createApiConfiguration = (token?: string, apiKey?: string) => {
  const headers: Record<string, string> = {};

  if (token) {
    headers["Authorization"] = `Bearer ${token}`;
  }

  if (apiKey) {
    headers["X-API-Key"] = apiKey;
  }

  return {
    basePath: API_BASE_URL,
    headers,
  };
};

/**
 * Convert auth options from the old API format to the new format
 */
export const convertAuthOptions = (
  token?: string | null,
  apiKey?: string | null,
) => {
  return createApiConfiguration(token || undefined, apiKey || undefined);
};

/**
 * Error handling wrapper for API calls
 */
export const handleApiResponse = async <T>(
  apiCall: Promise<{ data: T }>,
): Promise<T> => {
  try {
    const response = await apiCall;
    return response.data;
  } catch (error: any) {
    // Transform generated client errors to match our manual API error format
    if (error.response) {
      const apiError = {
        message: error.response.data?.message || error.message,
        status: error.response.status,
        errorCode: error.response.data?.error_code || "API_ERROR",
        details: error.response.data?.details || null,
      };
      throw apiError;
    }
    throw error;
  }
};

/**
 * Helper to extract data from generated client responses
 */
export const extractData = <T>(response: { data: T }): T => response.data;

/**
 * Legacy error class for backward compatibility
 */
export class ApiError extends Error {
  constructor(
    message: string,
    public status: number,
    public errorCode: string,
    public details?: any[] | null,
  ) {
    super(message);
    this.name = "ApiError";
  }

  static isApiError(error: unknown): error is ApiError {
    return error instanceof ApiError;
  }
}
