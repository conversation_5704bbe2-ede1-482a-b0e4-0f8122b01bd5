"""
Secure Celery tasks for maintenance operations.

⚠️ SECURITY CRITICAL: These tasks enforce tenant isolation and prevent RLS bypass ⚠️

This module provides secure implementations of maintenance tasks that:
1. Require explicit account_id for all operations
2. Never bypass RLS without strict validation
3. Log all security-critical operations
4. Validate tenant context before execution
"""

from typing import Dict, Any, Optional, List
from datetime import datetime, timezone
from asgiref.sync import async_to_sync
from celery.exceptions import MaxRetriesExceededError

from src.workers.celery_app import celery_app
from src.utils.base_logger import log_info, log_error, log_warning
from src.utils.tenant_context import run_with_tenant_context, with_tenant_context_celery

# Security logger for audit trail
import logging
security_logger = logging.getLogger("security.maintenance")

# ============================================================================
# SECURITY VALIDATION UTILITIES
# ============================================================================

class SecurityViolationError(Exception):
    """Raised when a security violation is detected in maintenance tasks."""
    pass

def validate_maintenance_security(
    task_id: str, 
    account_id: Optional[int], 
    task_name: str,
    require_account_id: bool = True
) -> None:
    """
    Validates security requirements for maintenance tasks.
    
    Args:
        task_id: Celery task ID for audit logging
        account_id: Tenant account ID 
        task_name: Name of the task being executed
        require_account_id: Whether account_id is mandatory
        
    Raises:
        SecurityViolationError: If security validation fails
    """
    # VALIDATION 1: account_id requirement
    if require_account_id:
        if account_id is None:
            error_msg = f"Task {task_id} ({task_name}): account_id is required for tenant-specific operations"
            security_logger.error(f"[SECURITY VIOLATION] {error_msg}")
            raise SecurityViolationError(error_msg)
        
        if not isinstance(account_id, int) or account_id <= 0:
            error_msg = f"Task {task_id} ({task_name}): Invalid account_id={account_id}. Must be positive integer."
            security_logger.error(f"[SECURITY VIOLATION] {error_msg}")
            raise SecurityViolationError(error_msg)
    
    # VALIDATION 2: Log security audit
    security_logger.warning(
        f"[SECURITY AUDIT] Maintenance task starting: "
        f"task_id={task_id}, task_name={task_name}, account_id={account_id}"
    )

def log_maintenance_completion(
    task_id: str,
    task_name: str, 
    account_id: Optional[int],
    result: Dict[str, Any],
    duration_seconds: float
) -> None:
    """
    Logs completion of maintenance tasks for security audit.
    """
    if result.get("success", False):
        security_logger.info(
            f"[SECURITY AUDIT] Task {task_id} ({task_name}) completed successfully: "
            f"account_id={account_id}, duration={duration_seconds:.2f}s, "
            f"processed={result.get('deleted_count', 0)} records"
        )
    else:
        security_logger.error(
            f"[SECURITY AUDIT] Task {task_id} ({task_name}) failed: "
            f"account_id={account_id}, error={result.get('error', 'Unknown')}"
        )

# ============================================================================
# SECURE PER-TENANT ORCHESTRATION
# ============================================================================

@celery_app.task(name="schedule_per_tenant_maintenance", bind=True)
def schedule_per_tenant_maintenance(
    self,
    task_name: str,
    task_kwargs: Dict[str, Any],
    enforce_tenant_isolation: bool = True,
    require_account_id: bool = True
) -> Dict[str, Any]:
    """
    Schedules a maintenance task for all active tenants securely.
    
    ⚠️ SECURITY CRITICAL: This task ensures maintenance operations
    are executed per-tenant with proper isolation.
    
    Args:
        task_name: Name of the task to schedule for each tenant
        task_kwargs: Keyword arguments for the task
        enforce_tenant_isolation: Whether to enforce strict tenant isolation
        require_account_id: Whether tasks require account_id parameter
        
    Returns:
        Dictionary with scheduling results
    """
    task_id = self.request.id or "unknown"
    
    try:
        # Security validation
        validate_maintenance_security(
            task_id=task_id,
            account_id=None,  # This is an orchestration task
            task_name=f"schedule_per_tenant_maintenance:{task_name}",
            require_account_id=False
        )
        
        # Get active tenant list
        from src.db.repositories.account import AccountRepository
        from src.db.session import get_db
        
        async def get_active_tenants():
            async with get_db() as db:
                repo = AccountRepository(db)
                accounts = await repo.get_active_accounts()
                return [account.account_id for account in accounts]
        
        active_tenants = async_to_sync(get_active_tenants)()
        
        if not active_tenants:
            log_warning(f"[Task {task_id}] No active tenants found")
            return {
                "total_tenants": 0,
                "scheduled_tasks": [],
                "security_compliance": "NO_TENANTS_FOUND"
            }
        
        # Schedule tasks for each tenant
        scheduled_tasks = []
        
        for tenant_id in active_tenants:
            # Prepare task arguments with tenant context
            tenant_kwargs = {**task_kwargs, "account_id": tenant_id}
            
            # Schedule the task
            task_result = celery_app.send_task(
                task_name,
                kwargs=tenant_kwargs,
                queue="maintenance_secure"
            )
            
            scheduled_tasks.append({
                "tenant_id": tenant_id,
                "task_id": task_result.id,
                "task_name": task_name
            })
        
        result = {
            "total_tenants": len(active_tenants),
            "scheduled_tasks": scheduled_tasks,
            "security_compliance": "TENANT_ISOLATION_ENFORCED" if enforce_tenant_isolation else "STANDARD",
            "orchestration_task_id": task_id,
            "timestamp": datetime.now(timezone.utc).isoformat()
        }
        
        log_info(
            f"[SECURITY AUDIT] Task {task_id} scheduled {task_name} "
            f"for {len(active_tenants)} tenants with tenant isolation"
        )
        
        return result
        
    except Exception as e:
        security_logger.error(
            f"[SECURITY ERROR] Task {task_id} failed to schedule per-tenant maintenance: {str(e)}"
        )
        return {
            "total_tenants": 0,
            "scheduled_tasks": [],
            "error": str(e),
            "security_compliance": "FAILED",
            "timestamp": datetime.now(timezone.utc).isoformat()
        }

# ============================================================================
# SECURE MAINTENANCE TASKS
# ============================================================================

@celery_app.task(name="cleanup_old_audit_logs_secure", bind=True, max_retries=3, default_retry_delay=300)
@with_tenant_context_celery(account_id_arg_name='account_id')
def cleanup_old_audit_logs_secure(
    self,
    account_id: int,
    days_to_keep: int = 90,
    batch_size: int = 10000
) -> Dict[str, Any]:
    """
    Securely cleans up old audit logs for a specific tenant.
    
    ⚠️ SECURITY CRITICAL: Requires valid account_id, enforces tenant isolation
    
    Args:
        account_id: Tenant account ID (REQUIRED)
        days_to_keep: Number of days to keep audit logs
        batch_size: Batch size for deletion
        
    Returns:
        Dictionary with cleanup results
    """
    task_id = self.request.id or "unknown"
    start_time = datetime.now(timezone.utc)
    
    try:
        # SECURITY VALIDATION
        validate_maintenance_security(
            task_id=task_id,
            account_id=account_id,
            task_name="cleanup_old_audit_logs_secure"
        )
        
        # Execute with tenant context
        result = async_to_sync(run_with_tenant_context)(
            account_id=account_id,
            async_func=cleanup_audit_logs_async,
            days_to_keep=days_to_keep,
            batch_size=batch_size
        )
        
        # Log completion
        duration = (datetime.now(timezone.utc) - start_time).total_seconds()
        log_maintenance_completion(task_id, "cleanup_old_audit_logs_secure", account_id, result, duration)
        
        return result
        
    except SecurityViolationError:
        raise  # Re-raise security violations
    except Exception as e:
        security_logger.error(
            f"[SECURITY ERROR] Task {task_id}: Error in cleanup_old_audit_logs_secure: {str(e)}"
        )
        # Retry with exponential backoff
        try:
            self.retry(exc=e, countdown=self.default_retry_delay * (2 ** self.request.retries))
        except MaxRetriesExceededError:
            security_logger.error(f"[SECURITY ERROR] Task {task_id}: Max retries exceeded")
        
        return {
            "account_id": account_id,
            "error": str(e),
            "success": False,
            "timestamp": datetime.now(timezone.utc).isoformat()
        }

@celery_app.task(name="cleanup_old_interactions_secure", bind=True, max_retries=3, default_retry_delay=300)
@with_tenant_context_celery(account_id_arg_name='account_id')
def cleanup_old_interactions_secure(
    self,
    account_id: int,
    days_to_keep: int = 180,
    batch_size: int = 10000,
    max_retries: int = 3,
    retry_delay: int = 5
) -> Dict[str, Any]:
    """
    Securely cleans up old interactions for a specific tenant.
    
    ⚠️ SECURITY CRITICAL: Requires valid account_id, enforces tenant isolation
    
    Args:
        account_id: Tenant account ID (REQUIRED)
        days_to_keep: Number of days to keep interactions
        batch_size: Batch size for deletion
        max_retries: Maximum retries per batch
        retry_delay: Delay between retries
        
    Returns:
        Dictionary with cleanup results
    """
    task_id = self.request.id or "unknown"
    start_time = datetime.now(timezone.utc)
    
    try:
        # SECURITY VALIDATION
        validate_maintenance_security(
            task_id=task_id,
            account_id=account_id,
            task_name="cleanup_old_interactions_secure"
        )
        
        # Execute with tenant context
        result = async_to_sync(run_with_tenant_context)(
            account_id=account_id,
            async_func=cleanup_interactions_async,
            days_to_keep=days_to_keep,
            batch_size=batch_size,
            max_retries=max_retries,
            retry_delay=retry_delay
        )
        
        # Log completion
        duration = (datetime.now(timezone.utc) - start_time).total_seconds()
        log_maintenance_completion(task_id, "cleanup_old_interactions_secure", account_id, result, duration)
        
        return result
        
    except SecurityViolationError:
        raise  # Re-raise security violations
    except Exception as e:
        security_logger.error(
            f"[SECURITY ERROR] Task {task_id}: Error in cleanup_old_interactions_secure: {str(e)}"
        )
        # Retry with exponential backoff
        try:
            self.retry(exc=e, countdown=self.default_retry_delay * (2 ** self.request.retries))
        except MaxRetriesExceededError:
            security_logger.error(f"[SECURITY ERROR] Task {task_id}: Max retries exceeded")
        
        return {
            "account_id": account_id,
            "error": str(e),
            "success": False,
            "timestamp": datetime.now(timezone.utc).isoformat()
        }

@celery_app.task(name="cleanup_old_data_secure_validated", bind=True, max_retries=3, default_retry_delay=300)
@with_tenant_context_celery(account_id_arg_name='account_id')
def cleanup_old_data_secure_validated(
    self,
    account_id: int,
    days_to_keep: int = 90
) -> Dict[str, Any]:
    """
    Securely cleans up old data using validated PostgreSQL function.
    
    ⚠️ SECURITY CRITICAL: This task uses RLS bypass with STRICT validation
    - account_id is MANDATORY
    - All operations are audited
    - Tenant isolation is enforced
    
    Args:
        account_id: Tenant account ID (REQUIRED)
        days_to_keep: Number of days to keep data
        
    Returns:
        Dictionary with cleanup results
    """
    task_id = self.request.id or "unknown"
    start_time = datetime.now(timezone.utc)
    
    try:
        # CRITICAL SECURITY VALIDATION
        validate_maintenance_security(
            task_id=task_id,
            account_id=account_id,
            task_name="cleanup_old_data_secure_validated"
        )
        
        # Additional validation for RLS bypass operations
        if days_to_keep < 1 or days_to_keep > 3650:
            raise SecurityViolationError(
                f"Invalid days_to_keep={days_to_keep}. Must be between 1 and 3650."
            )
        
        security_logger.warning(
            f"[SECURITY CRITICAL] Task {task_id}: Starting RLS bypass operation "
            f"for account_id={account_id}, days_to_keep={days_to_keep}"
        )
        
        # Execute with tenant context and RLS bypass
        from src.utils.maintenance import cleanup_old_data_secure
        result = async_to_sync(run_with_tenant_context)(
            account_id=account_id,
            async_func=cleanup_old_data_secure,
            days_to_keep=days_to_keep
        )
        
        # Log completion
        duration = (datetime.now(timezone.utc) - start_time).total_seconds()
        log_maintenance_completion(task_id, "cleanup_old_data_secure_validated", account_id, result, duration)
        
        return result
        
    except SecurityViolationError:
        raise  # Re-raise security violations
    except Exception as e:
        security_logger.error(
            f"[SECURITY CRITICAL] Task {task_id}: Error in RLS bypass operation: {str(e)}"
        )
        # Retry with exponential backoff
        try:
            self.retry(exc=e, countdown=self.default_retry_delay * (2 ** self.request.retries))
        except MaxRetriesExceededError:
            security_logger.error(f"[SECURITY CRITICAL] Task {task_id}: Max retries exceeded for RLS bypass")
        
        return {
            "account_id": account_id,
            "error": str(e),
            "success": False,
            "timestamp": datetime.now(timezone.utc).isoformat()
        }

@celery_app.task(name="cleanup_soft_deleted_records_secure", bind=True, max_retries=3, default_retry_delay=300)
@with_tenant_context_celery(account_id_arg_name='account_id')
def cleanup_soft_deleted_records_secure(
    self,
    account_id: int,
    retention_days: int = 90,
    dry_run: bool = False
) -> Dict[str, Any]:
    """
    Securely cleans up soft deleted records for a specific tenant.
    
    ⚠️ SECURITY CRITICAL: Requires valid account_id, enforces tenant isolation
    
    Args:
        account_id: Tenant account ID (REQUIRED)
        retention_days: Days to retain after soft delete
        dry_run: If True, only reports what would be deleted
        
    Returns:
        Dictionary with cleanup results
    """
    task_id = self.request.id or "unknown"
    start_time = datetime.now(timezone.utc)
    
    try:
        # SECURITY VALIDATION
        validate_maintenance_security(
            task_id=task_id,
            account_id=account_id,
            task_name="cleanup_soft_deleted_records_secure"
        )
        
        # Execute with tenant context
        from src.utils.maintenance import cleanup_soft_deleted_records_async
        result = async_to_sync(run_with_tenant_context)(
            account_id=account_id,
            async_func=cleanup_soft_deleted_records_async,
            retention_days=retention_days,
            dry_run=dry_run
        )
        
        # Log completion
        duration = (datetime.now(timezone.utc) - start_time).total_seconds()
        log_maintenance_completion(task_id, "cleanup_soft_deleted_records_secure", account_id, result, duration)
        
        return result
        
    except SecurityViolationError:
        raise  # Re-raise security violations
    except Exception as e:
        security_logger.error(
            f"[SECURITY ERROR] Task {task_id}: Error in cleanup_soft_deleted_records_secure: {str(e)}"
        )
        # Retry with exponential backoff
        try:
            self.retry(exc=e, countdown=self.default_retry_delay * (2 ** self.request.retries))
        except MaxRetriesExceededError:
            security_logger.error(f"[SECURITY ERROR] Task {task_id}: Max retries exceeded")
        
        return {
            "account_id": account_id,
            "retention_days": retention_days,
            "dry_run": dry_run,
            "total_archived": 0,
            "total_deleted": 0,
            "error": str(e),
            "success": False,
            "timestamp": datetime.now(timezone.utc).isoformat()
        }

# ============================================================================
# INFRASTRUCTURE TASKS (NO TENANT DATA ACCESS)
# ============================================================================

@celery_app.task(name="monitor_high_volume_tables_secure", bind=True, max_retries=2, default_retry_delay=300)
def monitor_high_volume_tables_secure(
    self,
    tenant_data_access: bool = False
) -> Dict[str, Any]:
    """
    Monitors high volume tables without accessing tenant data.
    
    Args:
        tenant_data_access: Must be False for security
        
    Returns:
        Dictionary with monitoring results
    """
    task_id = self.request.id or "unknown"
    
    try:
        # Security validation: No tenant data access allowed
        if tenant_data_access:
            raise SecurityViolationError(
                f"Task {task_id}: tenant_data_access must be False for infrastructure tasks"
            )
        
        security_logger.info(
            f"[SECURITY AUDIT] Infrastructure task {task_id}: monitor_high_volume_tables_secure starting"
        )
        
        # Import and execute monitoring task
        from src.workers.celery_tasks import monitor_high_volume_tables
        result = async_to_sync(monitor_high_volume_tables)()
        
        security_logger.info(
            f"[SECURITY AUDIT] Infrastructure task {task_id}: monitoring completed successfully"
        )
        
        return result
        
    except Exception as e:
        security_logger.error(
            f"[SECURITY ERROR] Infrastructure task {task_id}: Error in monitor_high_volume_tables_secure: {str(e)}"
        )
        return {
            "task_id": task_id,
            "error": str(e),
            "success": False,
            "timestamp": datetime.now(timezone.utc).isoformat()
        }

@celery_app.task(name="manage_partitions_task_secure", bind=True, max_retries=2, default_retry_delay=300)
def manage_partitions_task_secure(
    self,
    tenant_data_access: bool = False
) -> Dict[str, Any]:
    """
    Manages database partitions without accessing tenant data.
    
    Args:
        tenant_data_access: Must be False for security
        
    Returns:
        Dictionary with partition management results
    """
    task_id = self.request.id or "unknown"
    
    try:
        # Security validation: No tenant data access allowed
        if tenant_data_access:
            raise SecurityViolationError(
                f"Task {task_id}: tenant_data_access must be False for infrastructure tasks"
            )
        
        security_logger.info(
            f"[SECURITY AUDIT] Infrastructure task {task_id}: manage_partitions_task_secure starting"
        )
        
        # Import and execute partition management
        from src.workers.celery_tasks_partition import manage_partitions_task
        # Execute the original task logic but with security wrapper
        from scripts.manage_partitions import manage_partitions
        
        start_time = datetime.now(timezone.utc)
        async_to_sync(manage_partitions)()
        duration = (datetime.now(timezone.utc) - start_time).total_seconds()
        
        result = {
            "task_id": task_id,
            "status": "completed",
            "duration_seconds": duration,
            "timestamp": datetime.now(timezone.utc).isoformat(),
        }
        
        security_logger.info(
            f"[SECURITY AUDIT] Infrastructure task {task_id}: partition management completed successfully"
        )
        
        return result
        
    except Exception as e:
        security_logger.error(
            f"[SECURITY ERROR] Infrastructure task {task_id}: Error in manage_partitions_task_secure: {str(e)}"
        )
        return {
            "task_id": task_id,
            "error": str(e),
            "success": False,
            "timestamp": datetime.now(timezone.utc).isoformat()
        }

# Security module initialization complete
security_logger.info("Secure Celery tasks module loaded successfully") 