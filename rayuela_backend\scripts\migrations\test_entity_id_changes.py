#!/usr/bin/env python3
"""Script para verificar que los cambios de entity_id están funcionando correctamente."""

import sys
sys.path.append('.')

try:
    from src.db.models.audit_log import AuditLog
    from src.db.schemas.audit import AuditLogCreate
    import sqlalchemy
    
    print("=" * 60)
    print("🔍 VERIFICACIÓN DE CAMBIOS EN AuditLog.entity_id")
    print("=" * 60)
    
    # Verificar modelo SQLAlchemy
    print("\n✅ Verificando modelo AuditLog...")
    entity_id_column = AuditLog.__table__.columns['entity_id']
    print(f"   Tipo de columna entity_id: {entity_id_column.type}")
    print(f"   Es Integer: {isinstance(entity_id_column.type, sqlalchemy.Integer)}")
    print(f"   Nullable: {entity_id_column.nullable}")
    
    # Verificar schema Pydantic
    print("\n✅ Verificando schema AuditLogCreate...")
    schema_fields = AuditLogCreate.model_fields
    entity_id_field = schema_fields['entity_id']
    print(f"   Tipo de campo entity_id: {entity_id_field.annotation}")
    print(f"   Es int: {entity_id_field.annotation == int}")
    
    # Verificar creación de instancia
    print("\n✅ Verificando creación de instancia...")
    try:
        audit_log_data = AuditLogCreate(
            action="test_action",
            entity_type="product", 
            entity_id=123,  # Usar entero directamente
            performed_by="test_user",
            details="Test audit log"
        )
        print(f"   ✅ AuditLogCreate creado exitosamente")
        print(f"   entity_id value: {audit_log_data.entity_id}")
        print(f"   entity_id type: {type(audit_log_data.entity_id)}")
    except Exception as e:
        print(f"   ❌ Error creando AuditLogCreate: {e}")
    
    print("\n" + "=" * 60)
    print("✅ VERIFICACIÓN COMPLETA")
    print("   - Modelo actualizado a Integer ✅")
    print("   - Schema actualizado a int ✅") 
    print("   - Instanciación funcional ✅")
    print("=" * 60)
    
except Exception as e:
    print(f"❌ Error durante la verificación: {e}")
    import traceback
    traceback.print_exc()
    sys.exit(1) 