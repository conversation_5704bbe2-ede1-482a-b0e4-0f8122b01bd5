"use client";

import React from 'react';
import Link from 'next/link';
import { cn } from '@/lib/utils';

interface LogoProps {
  variant?: 'default' | 'marketing';
  className?: string;
  linkClassName?: string;
  href?: string;
}

export function Logo({
  variant = 'default',
  className,
  linkClassName,
  href = '/',
}: LogoProps) {
  const isMarketing = variant === 'marketing';
  
  // Standardize branding to "Rayuela.ai" for consistency
  const logoContent = (
    <div className={cn(
      "font-bold transition-all duration-300 ease-in-out rayuela-accent",
      isMarketing ? "text-display tracking-extra-tight" : "text-heading tracking-tight",
      "hover:scale-105",
      className
    )}>
      Rayuela.ai
    </div>
  );

  if (href) {
    return (
      <Link 
        href={href} 
        className={cn(
          "rayuela-interactive hover:opacity-90 rayuela-focus-ring rounded-lg p-1 -m-1",
          linkClassName
        )}
      >
        {logoContent}
      </Link>
    );
  }

  return logoContent;
}
