"""Change audit_log entity_id from String to Integer

Revision ID: 20250614_130000
Revises: 43bf25157985
Create Date: 2025-01-14 13:00:00.000000

"""
from alembic import op
import sqlalchemy as sa
from sqlalchemy.dialects import postgresql
from sqlalchemy.sql import text


# revision identifiers, used by Alembic.
revision = '20250614_130000'
down_revision = '43bf25157985'
branch_labels = None
depends_on = None


def upgrade() -> None:
    """Change entity_id from String to Integer in audit_logs table."""
    
    # First, we need to handle existing data.
    # Check if there are any audit logs with non-numeric entity_id values
    connection = op.get_bind()
    
    # Create a temporary column to store the converted values
    op.add_column('audit_logs', sa.Column('entity_id_temp', sa.Integer(), nullable=True))
    
    # Convert existing string entity_ids to integers where possible
    # For records where entity_id is a valid integer string, convert it
    # For non-numeric values, set to NULL (or a default value like 0)
    connection.execute(text("""
        UPDATE audit_logs 
        SET entity_id_temp = CASE 
            WHEN entity_id ~ '^[0-9]+$' THEN entity_id::integer
            ELSE NULL
        END
    """))
    
    # Drop the old entity_id column
    op.drop_column('audit_logs', 'entity_id')
    
    # Rename the temporary column to entity_id
    op.alter_column('audit_logs', 'entity_id_temp', new_column_name='entity_id')
    
    # Make the column NOT NULL after data migration
    op.alter_column('audit_logs', 'entity_id', nullable=False)


def downgrade() -> None:
    """Revert entity_id back to String type."""
    
    # Change entity_id back to String type
    # First make it nullable to handle the conversion
    op.alter_column('audit_logs', 'entity_id', nullable=True)
    
    # Create temporary column with String type
    op.add_column('audit_logs', sa.Column('entity_id_temp', sa.String(), nullable=True))
    
    # Convert integer values back to strings
    connection = op.get_bind()
    connection.execute(text("""
        UPDATE audit_logs 
        SET entity_id_temp = CASE 
            WHEN entity_id IS NOT NULL THEN entity_id::text
            ELSE ''
        END
    """))
    
    # Drop the integer column
    op.drop_column('audit_logs', 'entity_id')
    
    # Rename temp column back to entity_id
    op.alter_column('audit_logs', 'entity_id_temp', new_column_name='entity_id')
    
    # Make it NOT NULL again
    op.alter_column('audit_logs', 'entity_id', nullable=False) 