"""Add onboarding_checklist_status to accounts table

Revision ID: 43bf25157985
Revises: add_comprehensive_rls_policies
Create Date: 2025-06-06 13:54:58.348668

"""
from typing import Sequence, Union

from alembic import op
import sqlalchemy as sa


# revision identifiers, used by Alembic.
revision: str = '43bf25157985'
down_revision: Union[str, None] = 'add_comprehensive_rls_policies'
branch_labels: Union[str, Sequence[str], None] = None
depends_on: Union[str, Sequence[str], None] = None


def upgrade() -> None:
    """Upgrade schema."""
    # Add onboarding_checklist_status column to accounts table
    op.add_column('accounts', sa.Column('onboarding_checklist_status', sa.JSON(), nullable=True, comment='JSON object storing the status of onboarding checklist items'))


def downgrade() -> None:
    """Downgrade schema."""
    # Remove onboarding_checklist_status column from accounts table
    op.drop_column('accounts', 'onboarding_checklist_status')
