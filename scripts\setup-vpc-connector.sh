#!/bin/bash

# Setup VPC Connector for Rayuela
# Creates the VPC connector required for secure database and Redis access

set -e

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# Configuration
PROJECT_ID="${GCP_PROJECT_ID:-$(gcloud config get-value project)}"
REGION="${GCP_REGION:-us-central1}"
VPC_CONNECTOR_NAME="rayuela-vpc-connector"
VPC_NETWORK="default"
IP_CIDR_RANGE="********/28"  # Small range for connector
MIN_INSTANCES=2
MAX_INSTANCES=3

echo -e "${BLUE}🔧 Configurando VPC Connector para Rayuela${NC}"
echo "=============================================="
echo "📋 Proyecto: $PROJECT_ID"
echo "🌍 Región: $REGION"
echo "🔗 Nombre del Connector: $VPC_CONNECTOR_NAME"
echo "🌐 Red VPC: $VPC_NETWORK"
echo "📡 Rango IP: $IP_CIDR_RANGE"
echo ""

# Function to check if connector already exists
check_existing_connector() {
    echo -e "${BLUE}🔍 Verificando si el VPC Connector ya existe...${NC}"
    
    if gcloud compute networks vpc-access connectors describe $VPC_CONNECTOR_NAME \
        --region=$REGION --project=$PROJECT_ID &>/dev/null; then
        echo -e "${GREEN}✅ VPC Connector '$VPC_CONNECTOR_NAME' ya existe${NC}"
        
        # Show existing connector details
        echo "📋 Detalles del connector existente:"
        gcloud compute networks vpc-access connectors describe $VPC_CONNECTOR_NAME \
            --region=$REGION --project=$PROJECT_ID \
            --format="table(name,state,network,ipCidrRange,minInstances,maxInstances)"
        
        echo ""
        echo -e "${YELLOW}💡 El connector ya está configurado. No se requiere acción adicional.${NC}"
        return 0
    else
        echo -e "${YELLOW}⚠️ VPC Connector no existe. Procediendo con la creación...${NC}"
        return 1
    fi
}

# Function to enable required APIs
enable_apis() {
    echo -e "${BLUE}🔌 Habilitando APIs requeridas...${NC}"
    
    REQUIRED_APIS=(
        "vpcaccess.googleapis.com"
        "compute.googleapis.com"
        "servicenetworking.googleapis.com"
    )
    
    for api in "${REQUIRED_APIS[@]}"; do
        echo "📡 Habilitando $api..."
        gcloud services enable $api --project=$PROJECT_ID
        
        if [ $? -eq 0 ]; then
            echo -e "${GREEN}✅ $api habilitada${NC}"
        else
            echo -e "${RED}❌ Error habilitando $api${NC}"
            exit 1
        fi
    done
    
    echo ""
}

# Function to check VPC network
check_vpc_network() {
    echo -e "${BLUE}🌐 Verificando red VPC...${NC}"
    
    if gcloud compute networks describe $VPC_NETWORK --project=$PROJECT_ID &>/dev/null; then
        echo -e "${GREEN}✅ Red VPC '$VPC_NETWORK' existe${NC}"
        return 0
    else
        echo -e "${RED}❌ Red VPC '$VPC_NETWORK' no existe${NC}"
        echo "💡 Creando red VPC por defecto..."
        
        gcloud compute networks create $VPC_NETWORK \
            --subnet-mode=auto \
            --project=$PROJECT_ID
        
        if [ $? -eq 0 ]; then
            echo -e "${GREEN}✅ Red VPC '$VPC_NETWORK' creada${NC}"
        else
            echo -e "${RED}❌ Error creando red VPC${NC}"
            exit 1
        fi
    fi
    
    echo ""
}

# Function to create VPC connector
create_vpc_connector() {
    echo -e "${BLUE}🔗 Creando VPC Connector...${NC}"
    
    echo "⚙️ Configuración del VPC Connector:"
    echo "   Nombre: $VPC_CONNECTOR_NAME"
    echo "   Región: $REGION"
    echo "   Red: $VPC_NETWORK"
    echo "   Rango IP: $IP_CIDR_RANGE"
    echo "   Instancias: $MIN_INSTANCES-$MAX_INSTANCES"
    echo ""
    
    # Create the VPC connector
    gcloud compute networks vpc-access connectors create $VPC_CONNECTOR_NAME \
        --region=$REGION \
        --network=$VPC_NETWORK \
        --range=$IP_CIDR_RANGE \
        --min-instances=$MIN_INSTANCES \
        --max-instances=$MAX_INSTANCES \
        --project=$PROJECT_ID
    
    if [ $? -eq 0 ]; then
        echo -e "${GREEN}✅ VPC Connector creado exitosamente${NC}"
    else
        echo -e "${RED}❌ Error creando VPC Connector${NC}"
        echo ""
        echo -e "${YELLOW}💡 Posibles soluciones:${NC}"
        echo "   1. Verificar que el rango IP $IP_CIDR_RANGE no esté en uso"
        echo "   2. Intentar con un rango diferente: ********/28"
        echo "   3. Verificar permisos IAM"
        exit 1
    fi
    
    echo ""
}

# Function to verify connector is ready
verify_connector() {
    echo -e "${BLUE}🔍 Verificando estado del VPC Connector...${NC}"
    
    # Wait for connector to be ready
    echo "⏳ Esperando que el connector esté listo..."
    
    for i in {1..10}; do
        STATE=$(gcloud compute networks vpc-access connectors describe $VPC_CONNECTOR_NAME \
            --region=$REGION --project=$PROJECT_ID \
            --format="value(state)" 2>/dev/null)
        
        if [ "$STATE" = "READY" ]; then
            echo -e "${GREEN}✅ VPC Connector está listo${NC}"
            break
        elif [ "$STATE" = "CREATING" ]; then
            echo "⏳ Estado: CREATING... (intento $i/10)"
            sleep 30
        else
            echo -e "${YELLOW}⚠️ Estado: $STATE (intento $i/10)${NC}"
            sleep 30
        fi
        
        if [ $i -eq 10 ]; then
            echo -e "${RED}❌ Timeout esperando que el connector esté listo${NC}"
            echo "🔍 Estado actual: $STATE"
            exit 1
        fi
    done
    
    # Show final connector details
    echo ""
    echo "📋 Detalles finales del VPC Connector:"
    gcloud compute networks vpc-access connectors describe $VPC_CONNECTOR_NAME \
        --region=$REGION --project=$PROJECT_ID \
        --format="table(name,state,network,ipCidrRange,minInstances,maxInstances)"
    
    echo ""
}

# Function to provide next steps
provide_next_steps() {
    echo -e "${BLUE}🚀 Próximos Pasos${NC}"
    echo "================="
    echo ""
    
    echo -e "${GREEN}✅ VPC Connector configurado exitosamente${NC}"
    echo ""
    
    echo -e "${YELLOW}📋 Para completar la configuración de seguridad:${NC}"
    echo ""
    
    echo "1. 🔒 Configurar Cloud SQL para IP privada únicamente:"
    echo "   ./scripts/configure-sql-private.sh"
    echo ""
    
    echo "2. 🔒 Configurar Redis para red privada únicamente:"
    echo "   ./scripts/configure-redis-private.sh"
    echo ""
    
    echo "3. 🚀 Desplegar servicios con VPC Connector:"
    echo "   gcloud builds submit --config cloudbuild-deploy-production.yaml"
    echo ""
    
    echo "4. 🔍 Verificar configuración completa:"
    echo "   ./scripts/verify-vpc-security.sh"
    echo ""
    
    echo -e "${GREEN}💡 El VPC Connector ya está listo para ser usado por Cloud Run${NC}"
}

# Main execution
echo "🚀 Iniciando configuración del VPC Connector..."
echo ""

# Check if connector already exists
if check_existing_connector; then
    echo -e "${GREEN}🎉 Configuración completa - VPC Connector ya disponible${NC}"
    provide_next_steps
    exit 0
fi

# Create connector if it doesn't exist
echo "⚙️ Creando nuevo VPC Connector..."
echo ""

enable_apis
check_vpc_network
create_vpc_connector
verify_connector

echo ""
echo "======================================================"
echo -e "${GREEN}🎉 VPC Connector configurado exitosamente!${NC}"
echo "======================================================"

provide_next_steps

echo -e "${BLUE}📝 Nota: Los cambios en cloudbuild.yaml y cloudbuild-deploy-production.yaml${NC}"
echo -e "${BLUE}ya están implementados para usar este VPC Connector.${NC}" 