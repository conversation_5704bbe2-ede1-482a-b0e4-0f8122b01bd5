"""
Tests for CORS security configuration.

This module tests the CORS (Cross-Origin Resource Sharing) security hardening
to ensure that only authorized origins, methods, and headers are allowed.
"""

import pytest
from fastapi.testclient import TestClient
from unittest.mock import patch
import os

from src.core.config import Settings


class TestCORSSecurityConfiguration:
    """Test CORS security configuration and validation."""

    def test_cors_allowed_origins_development_default(self):
        """Test that development environment has secure default origins."""
        with patch.dict(os.environ, {"ENV": "development"}, clear=True):
            settings = Settings()
            
            # Should not contain wildcard
            assert "*" not in settings.ALLOWED_ORIGINS
            
            # Should contain localhost origins for development
            assert any("localhost" in origin for origin in settings.ALLOWED_ORIGINS)
            assert any("127.0.0.1" in origin for origin in settings.ALLOWED_ORIGINS)

    def test_cors_production_validation_rejects_wildcard(self):
        """Test that production environment rejects wildcard origins."""
        with patch.dict(os.environ, {"ENV": "production"}, clear=True):
            with pytest.raises(ValueError, match="must not contain '\\*'"):
                Settings(ALLOWED_ORIGINS=["*"])

    def test_cors_production_validation_rejects_localhost(self):
        """Test that production environment rejects localhost origins."""
        with patch.dict(os.environ, {"ENV": "production"}, clear=True):
            with pytest.raises(ValueError, match="cannot contain localhost"):
                Settings(ALLOWED_ORIGINS=["http://localhost:3000"])

    def test_cors_production_validation_rejects_empty_list(self):
        """Test that production environment rejects empty origins list."""
        with patch.dict(os.environ, {"ENV": "production"}, clear=True):
            with pytest.raises(ValueError, match="cannot be empty"):
                Settings(ALLOWED_ORIGINS=[])

    def test_cors_production_validation_accepts_valid_origins(self):
        """Test that production environment accepts valid HTTPS origins."""
        with patch.dict(os.environ, {"ENV": "production"}, clear=True):
            settings = Settings(ALLOWED_ORIGINS=[
                "https://app.example.com",
                "https://admin.example.com"
            ])
            
            assert settings.ALLOWED_ORIGINS == [
                "https://app.example.com",
                "https://admin.example.com"
            ]

    def test_cors_production_warns_about_http_origins(self):
        """Test that production environment warns about HTTP origins."""
        with patch.dict(os.environ, {"ENV": "production"}, clear=True):
            with pytest.warns(UserWarning, match="uses HTTP instead of HTTPS"):
                Settings(ALLOWED_ORIGINS=["http://app.example.com"])


class TestCORSMiddlewareConfiguration:
    """Test CORS middleware configuration in the application."""

    @pytest.fixture
    def app_client(self):
        """Create a test client with CORS middleware configured."""
        from fastapi import FastAPI
        from src.middleware.setup import setup_middleware
        
        app = FastAPI()
        setup_middleware(app)
        
        # Add a simple test endpoint
        @app.get("/test")
        async def test_endpoint():
            return {"message": "test"}
            
        return TestClient(app)

    def test_cors_allows_configured_origins(self, app_client):
        """Test that CORS allows requests from configured origins."""
        # Test with a development origin (assuming localhost is in ALLOWED_ORIGINS)
        response = app_client.options(
            "/test",
            headers={
                "Origin": "http://localhost:3000",
                "Access-Control-Request-Method": "GET",
            }
        )
        
        # Should allow the request (200 or 204 status)
        assert response.status_code in [200, 204]
        
        # Should include CORS headers
        assert "access-control-allow-origin" in response.headers

    def test_cors_rejects_unauthorized_origins(self, app_client):
        """Test that CORS rejects requests from unauthorized origins."""
        response = app_client.options(
            "/test",
            headers={
                "Origin": "https://malicious.com",
                "Access-Control-Request-Method": "GET",
            }
        )
        
        # Should not include CORS allow-origin header for unauthorized origin
        assert response.headers.get("access-control-allow-origin") != "https://malicious.com"

    def test_cors_allows_specific_methods_only(self, app_client):
        """Test that CORS only allows configured HTTP methods."""
        response = app_client.options(
            "/test",
            headers={
                "Origin": "http://localhost:3000",
                "Access-Control-Request-Method": "GET",
            }
        )
        
        # Should allow standard methods
        allowed_methods = response.headers.get("access-control-allow-methods", "")
        
        assert "GET" in allowed_methods
        assert "POST" in allowed_methods
        assert "PUT" in allowed_methods
        assert "DELETE" in allowed_methods
        assert "OPTIONS" in allowed_methods
        
        # Should not allow arbitrary methods (this is implicit by not using wildcard)

    def test_cors_allows_specific_headers_only(self, app_client):
        """Test that CORS only allows configured headers."""
        response = app_client.options(
            "/test",
            headers={
                "Origin": "http://localhost:3000",
                "Access-Control-Request-Method": "POST",
                "Access-Control-Request-Headers": "content-type,authorization",
            }
        )
        
        # Should allow configured headers
        allowed_headers = response.headers.get("access-control-allow-headers", "").lower()
        
        assert "content-type" in allowed_headers
        assert "authorization" in allowed_headers
        assert "x-api-key" in allowed_headers
        assert "x-tenant-id" in allowed_headers

    def test_cors_credentials_enabled(self, app_client):
        """Test that CORS allows credentials for cross-origin requests."""
        response = app_client.options(
            "/test",
            headers={
                "Origin": "http://localhost:3000",
                "Access-Control-Request-Method": "GET",
            }
        )
        
        # Should allow credentials
        assert response.headers.get("access-control-allow-credentials") == "true"


class TestCORSSecurityPrevention:
    """Test that CORS configuration prevents common security issues."""

    def test_prevents_csrf_with_strict_origins(self):
        """Test that strict origin validation helps prevent CSRF attacks."""
        with patch.dict(os.environ, {"ENV": "production"}, clear=True):
            settings = Settings(ALLOWED_ORIGINS=["https://app.example.com"])
            
            # Should only allow specific origins, not wildcards
            assert settings.ALLOWED_ORIGINS == ["https://app.example.com"]
            assert "*" not in settings.ALLOWED_ORIGINS

    def test_follows_principle_of_least_privilege(self):
        """Test that CORS configuration follows principle of least privilege."""
        from src.middleware.setup import setup_middleware
        from fastapi import FastAPI
        
        app = FastAPI()
        setup_middleware(app)
        
        # Find the CORS middleware
        cors_middleware = None
        for middleware in app.user_middleware:
            if "CORSMiddleware" in str(middleware.cls):
                cors_middleware = middleware
                break
        
        assert cors_middleware is not None
        
        # Should not use wildcard methods or headers
        cors_kwargs = cors_middleware.kwargs
        
        # Methods should be specific list, not wildcard
        assert cors_kwargs.get("allow_methods") != ["*"]
        assert isinstance(cors_kwargs.get("allow_methods"), list)
        
        # Headers should be specific list, not wildcard  
        assert cors_kwargs.get("allow_headers") != ["*"]
        assert isinstance(cors_kwargs.get("allow_headers"), list)


if __name__ == "__main__":
    pytest.main([__file__]) 