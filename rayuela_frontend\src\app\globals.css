@tailwind base;
@tailwind components;
@tailwind utilities;

@import "tw-animate-css";

@custom-variant dark (&:is(.dark *));

@theme inline {
  --color-background: var(--background);
  --color-foreground: var(--foreground);
  --font-sans: var(--font-geist-sans);
  --font-mono: var(--font-geist-mono);
  --color-sidebar-ring: var(--sidebar-ring);
  --color-sidebar-border: var(--sidebar-border);
  --color-sidebar-accent-foreground: var(--sidebar-accent-foreground);
  --color-sidebar-accent: var(--sidebar-accent);
  --color-sidebar-primary-foreground: var(--sidebar-primary-foreground);
  --color-sidebar-primary: var(--sidebar-primary);
  --color-sidebar-foreground: var(--sidebar-foreground);
  --color-sidebar: var(--sidebar);
  --color-chart-5: var(--chart-5);
  --color-chart-4: var(--chart-4);
  --color-chart-3: var(--chart-3);
  --color-chart-2: var(--chart-2);
  --color-chart-1: var(--chart-1);
  --color-ring: var(--ring);
  --color-input: var(--input);
  --color-border: var(--border);
  --color-destructive: var(--destructive);
  --color-accent-foreground: var(--accent-foreground);
  --color-accent: var(--accent);
  --color-muted-foreground: var(--muted-foreground);
  --color-muted: var(--muted);
  --color-secondary-foreground: var(--secondary-foreground);
  --color-secondary: var(--secondary);
  --color-primary-foreground: var(--primary-foreground);
  --color-primary: var(--primary);
  --color-popover-foreground: var(--popover-foreground);
  --color-popover: var(--popover);
  --color-card-foreground: var(--card-foreground);
  --color-card: var(--card);
  --radius-sm: calc(var(--radius) - 4px);
  --radius-md: calc(var(--radius) - 2px);
  --radius-lg: var(--radius);
  --radius-xl: calc(var(--radius) + 4px);
}

:root {
  --radius: 0.625rem;
  --background: oklch(1 0 0);
  --foreground: oklch(0.129 0.042 264.695);
  --card: oklch(1 0 0);
  --card-foreground: oklch(0.129 0.042 264.695);
  --popover: oklch(1 0 0);
  --popover-foreground: oklch(0.129 0.042 264.695);
  --primary: oklch(0.208 0.042 265.755);
  --primary-foreground: oklch(0.984 0.003 247.858);
  --secondary: oklch(0.968 0.007 247.896);
  --secondary-foreground: oklch(0.208 0.042 265.755);
  --muted: oklch(0.968 0.007 247.896);
  --muted-foreground: oklch(0.554 0.046 257.417);
  --accent: oklch(0.968 0.007 247.896);
  --accent-foreground: oklch(0.208 0.042 265.755);
  --destructive: oklch(0.577 0.245 27.325);
  --border: oklch(0.929 0.013 255.508);
  --input: oklch(0.929 0.013 255.508);
  --ring: oklch(0.704 0.04 256.788);
  --chart-1: oklch(0.646 0.222 41.116);
  --chart-2: oklch(0.6 0.118 184.704);
  --chart-3: oklch(0.398 0.07 227.392);
  --chart-4: oklch(0.828 0.189 84.429);
  --chart-5: oklch(0.769 0.188 70.08);
  --sidebar: oklch(0.984 0.003 247.858);
  --sidebar-foreground: oklch(0.129 0.042 264.695);
  --sidebar-primary: oklch(0.208 0.042 265.755);
  --sidebar-primary-foreground: oklch(0.984 0.003 247.858);
  --sidebar-accent: oklch(0.968 0.007 247.896);
  --sidebar-accent-foreground: oklch(0.208 0.042 265.755);
  --sidebar-border: oklch(0.929 0.013 255.508);
  --sidebar-ring: oklch(0.704 0.04 256.788);
  
  /* Colores semánticos adicionales armonizados con la paleta oklch */
  --success: oklch(0.646 0.222 145.0);
  --success-foreground: oklch(0.984 0.003 247.858);
  --success-light: oklch(0.97 0.022 145.0);
  --warning: oklch(0.828 0.189 84.429);
  --warning-foreground: oklch(0.129 0.042 264.695);
  --warning-light: oklch(0.97 0.022 84.429);
  --info: oklch(0.6 0.118 220.0);
  --info-foreground: oklch(0.984 0.003 247.858);
  --info-light: oklch(0.97 0.022 220.0);
  
  /* Variables para gradientes de marketing unificados */
  --marketing-gradient-start: oklch(0.984 0.003 247.858);
  --marketing-gradient-end: oklch(0.968 0.007 247.896);
  --marketing-gradient-accent: oklch(0.929 0.013 255.508);
}

.dark {
  --background: oklch(0.129 0.042 264.695);
  --foreground: oklch(0.984 0.003 247.858);
  --card: oklch(0.208 0.042 265.755);
  --card-foreground: oklch(0.984 0.003 247.858);
  --popover: oklch(0.208 0.042 265.755);
  --popover-foreground: oklch(0.984 0.003 247.858);
  --primary: oklch(0.929 0.013 255.508);
  --primary-foreground: oklch(0.208 0.042 265.755);
  --secondary: oklch(0.279 0.041 260.031);
  --secondary-foreground: oklch(0.984 0.003 247.858);
  --muted: oklch(0.279 0.041 260.031);
  --muted-foreground: oklch(0.704 0.04 256.788);
  --accent: oklch(0.279 0.041 260.031);
  --accent-foreground: oklch(0.984 0.003 247.858);
  --destructive: oklch(0.704 0.191 22.216);
  --border: oklch(1 0 0 / 10%);
  --input: oklch(1 0 0 / 15%);
  --ring: oklch(0.551 0.027 264.364);
  --chart-1: oklch(0.488 0.243 264.376);
  --chart-2: oklch(0.696 0.17 162.48);
  --chart-3: oklch(0.769 0.188 70.08);
  --chart-4: oklch(0.627 0.265 303.9);
  --chart-5: oklch(0.645 0.246 16.439);
  --sidebar: oklch(0.208 0.042 265.755);
  --sidebar-foreground: oklch(0.984 0.003 247.858);
  --sidebar-primary: oklch(0.488 0.243 264.376);
  --sidebar-primary-foreground: oklch(0.984 0.003 247.858);
  --sidebar-accent: oklch(0.279 0.041 260.031);
  --sidebar-accent-foreground: oklch(0.984 0.003 247.858);
  --sidebar-border: oklch(1 0 0 / 10%);
  --sidebar-ring: oklch(0.551 0.027 264.364);
  
  /* Colores semánticos para modo oscuro */
  --success: oklch(0.696 0.17 145.0);
  --success-foreground: oklch(0.129 0.042 264.695);
  --success-light: oklch(0.279 0.041 145.0);
  --warning: oklch(0.769 0.188 84.429);
  --warning-foreground: oklch(0.129 0.042 264.695);
  --warning-light: oklch(0.279 0.041 84.429);
  --info: oklch(0.488 0.243 220.0);
  --info-foreground: oklch(0.984 0.003 247.858);
  --info-light: oklch(0.279 0.041 220.0);
  
  /* Gradientes de marketing para modo oscuro */
  --marketing-gradient-start: oklch(0.129 0.042 264.695);
  --marketing-gradient-end: oklch(0.208 0.042 265.755);
  --marketing-gradient-accent: oklch(0.279 0.041 260.031);
}

@layer base {
  * {
    @apply border-border outline-ring/50;
  }

  body {
    @apply bg-background text-foreground font-sans;
    font-feature-settings: "rlig" 1, "calt" 1;
    text-rendering: optimizeLegibility;
    -webkit-font-smoothing: antialiased;
    -moz-osx-font-smoothing: grayscale;
  }

  /* Optimización de carga de fuentes */
  @font-face {
    font-family: 'Inter';
    font-display: swap;
  }
}

@layer components {
  /* Clases tipográficas semánticas */
  .text-display {
    @apply text-display-md font-bold tracking-extra-tight;
  }
  
  .text-display-large {
    @apply text-display-lg font-bold tracking-extra-tight;
  }
  
  .text-heading {
    @apply text-heading-lg font-semibold tracking-tight;
  }
  
  .text-heading-large {
    @apply text-heading-xl font-semibold tracking-tight;
  }
  
  .text-subheading {
    @apply text-heading-md font-medium;
  }
  
  .text-body {
    @apply text-body-md leading-body;
  }
  
  .text-body-large {
    @apply text-body-lg leading-body;
  }
  
  .text-caption {
    @apply text-caption font-medium text-muted-foreground;
  }
  
  .text-caption-large {
    @apply text-caption-lg font-medium text-muted-foreground;
  }
  
  .text-overline {
    @apply text-caption-sm font-semibold uppercase tracking-wider text-muted-foreground;
  }
  
  .text-code {
    @apply font-mono text-body-sm bg-muted px-2 py-1 rounded text-foreground;
  }
  
  .text-code-inline {
    @apply font-mono text-caption-lg bg-muted px-1.5 py-0.5 rounded text-foreground;
  }

  /* Clases para números y métricas */
  .text-metric {
    @apply text-display-lg font-bold tabular-nums tracking-tightest;
  }
  
  .text-metric-large {
    @apply text-display-xl font-bold tabular-nums tracking-tightest;
  }
  
  .text-metric-small {
    @apply text-heading-lg font-bold tabular-nums tracking-tight;
  }

  /* Clases para contenido de texto largo */
  .text-prose {
    @apply text-body leading-relaxed max-w-prose;
  }
  
  .text-prose h1 {
    @apply text-display-large mb-6 mt-8 first:mt-0;
  }
  
  .text-prose h2 {
    @apply text-heading-large mb-4 mt-6;
  }
  
  .text-prose h3 {
    @apply text-heading mb-3 mt-5;
  }
  
  .text-prose h4 {
    @apply text-subheading mb-2 mt-4;
  }
  
  .text-prose p {
    @apply mb-4;
  }
  
  .text-prose ul, .text-prose ol {
    @apply mb-4 pl-6;
  }
  
  .text-prose li {
    @apply mb-1;
  }

  /* Estados de interacción mejorados */
  .text-interactive {
    @apply transition-colors duration-200 ease-in-out;
  }
  
  .text-link {
    @apply text-interactive text-primary hover:text-primary/80 underline-offset-4 hover:underline;
  }

  /* Clases para toque creativo y exploración */
  .rayuela-accent {
    @apply text-primary;
  }
  
  .rayuela-accent-bg {
    @apply bg-primary text-primary-foreground;
  }
  
  .rayuela-info-accent {
    @apply text-info;
  }
  
  .rayuela-info-accent-bg {
    @apply bg-info text-info-foreground;
  }

  /* Gradientes sutiles para profundidad */
  .rayuela-card-gradient {
    @apply bg-gradient-to-br from-background to-muted/30;
  }
  
  .rayuela-subtle-gradient {
    @apply bg-gradient-to-br from-card to-card/90;
  }
  
  .rayuela-marketing-gradient {
    @apply bg-gradient-to-br from-marketing-gradient-start to-marketing-gradient-end;
  }

  /* Animaciones sutiles para interactividad */
  .rayuela-interactive {
    @apply transition-all duration-300 ease-in-out;
  }
  
  .rayuela-card-hover {
    @apply rayuela-interactive hover:shadow-md hover:-translate-y-0.5;
  }
  
  .rayuela-button-hover {
    @apply rayuela-interactive hover:scale-105 active:scale-95;
  }

  /* Efectos de progreso y exploración */
  .rayuela-progress-glow {
    @apply shadow-lg;
  }
  
  .rayuela-exploration-glow {
    @apply shadow-lg;
  }

  /* Estilos específicos para iconografía moderna */
  .rayuela-icon-accent {
    @apply text-primary group-hover:text-primary/80 transition-colors;
  }
  
  .rayuela-icon-progress {
    @apply text-success group-hover:text-success/80 transition-colors;
  }
  
  .rayuela-icon-exploration {
    @apply text-info group-hover:text-info/80 transition-colors;
  }

  /*
  SISTEMA DE COLORES UNIFICADO - RAYUELA
  =====================================
  
  Este sistema utiliza variables CSS basadas en oklch para garantizar:
  - Consistencia entre modo claro y oscuro
  - Armonía entre secciones de marketing y producto  
  - Accesibilidad y contraste óptimo
  
  MEJORES PRÁCTICAS:
  ✅ Usar: bg-background, bg-card, bg-muted
  ❌ Evitar: bg-gray-100, bg-white, bg-blue-50
  
  ✅ Usar: text-foreground, text-muted-foreground  
  ❌ Evitar: text-gray-800, text-blue-600
  
  ✅ Usar: Badge variant="success|warning|info"
  ❌ Evitar: bg-green-100 text-green-800
  
  ✅ Usar: from-marketing-gradient-start to-marketing-gradient-end
  ❌ Evitar: from-blue-50 to-indigo-100
  
  Las variables oklch proporcionan transiciones suaves y 
  control preciso de luminancia para máxima accesibilidad.
  */

  /* Clases para toque creativo y exploración */
  .rayuela-accent {
    @apply text-primary;
  }
  
  .rayuela-accent-bg {
    @apply bg-primary text-primary-foreground;
  }
  
  .rayuela-info-accent {
    @apply text-info;
  }
  
  .rayuela-info-accent-bg {
    @apply bg-info text-info-foreground;
  }

  /* Gradientes sutiles para profundidad */
  .rayuela-card-gradient {
    @apply bg-gradient-to-br from-background to-muted/30;
  }
  
  .rayuela-subtle-gradient {
    @apply bg-gradient-to-br from-card to-card/90;
  }
  
  .rayuela-marketing-gradient {
    @apply bg-gradient-to-br from-marketing-gradient-start to-marketing-gradient-end;
  }

  /* Animaciones sutiles para interactividad */
  .rayuela-interactive {
    @apply transition-all duration-300 ease-in-out;
  }
  
  .rayuela-card-hover {
    @apply rayuela-interactive hover:shadow-md hover:-translate-y-0.5;
  }
  
  .rayuela-button-hover {
    @apply rayuela-interactive hover:scale-105 active:scale-95;
  }
  
  .rayuela-fade-in {
    @apply animate-fade-in;
  }
  
  .rayuela-scale-in {
    @apply animate-scale-in;
  }

  /* Efectos de progreso y exploración */
  .rayuela-progress-glow {
    @apply shadow-glow-primary;
  }
  
  .rayuela-exploration-glow {
    @apply shadow-glow-info;
  }

  /* Estilos específicos para iconografía moderna */
  .rayuela-icon-accent {
    @apply text-primary group-hover:text-primary/80 transition-colors;
  }
  
  .rayuela-icon-progress {
    @apply text-success group-hover:text-success/80 transition-colors;
  }
  
  .rayuela-icon-exploration {
    @apply text-info group-hover:text-info/80 transition-colors;
  }
}

@layer utilities {
  /* Utilidades adicionales para tipografía */
  .font-feature-small-caps {
    font-feature-settings: "smcp" 1;
  }
  
  .font-feature-tabular {
    font-feature-settings: "tnum" 1;
  }
  
  .font-feature-oldstyle {
    font-feature-settings: "onum" 1;
  }
  
  /* Animaciones mejoradas para skeleton */
  @keyframes shimmer {
    0% {
      background-position: -200% 0;
    }
    100% {
      background-position: 200% 0;
    }
  }
  
  .animate-shimmer {
    animation: shimmer 2s ease-in-out infinite;
  }

  /* Utilidades para el toque creativo de Rayuela */
  .rayuela-shimmer {
    background: linear-gradient(
      110deg,
      transparent 0%,
      transparent 40%,
      rgba(255, 255, 255, 0.1) 50%,
      transparent 60%,
      transparent 100%
    );
    background-size: 200% 100%;
    @apply animate-shimmer;
  }

  /* Utilidades para sombras mejoradas */
  .shadow-soft {
    box-shadow: 0 2px 15px -3px rgba(0, 0, 0, 0.07), 0 10px 20px -2px rgba(0, 0, 0, 0.04);
  }
  
  .shadow-medium {
    box-shadow: 0 4px 25px -2px rgba(0, 0, 0, 0.08), 0 10px 30px -2px rgba(0, 0, 0, 0.06);
  }
  
  .shadow-glow {
    box-shadow: 0 0 20px -2px rgba(0, 0, 0, 0.1);
  }

  /* Estados de interacción específicos de Rayuela */
  .rayuela-focus-ring {
    @apply focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-primary focus-visible:ring-offset-2;
  }
  
  .rayuela-hover-lift {
    @apply transition-transform duration-200 hover:-translate-y-1;
  }
  
  .rayuela-active-press {
    @apply active:scale-95 transition-transform duration-75;
  }
}