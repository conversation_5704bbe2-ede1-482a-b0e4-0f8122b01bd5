# API Synchronization Guide

## 🔄 Resolución de Discrepancia Crítica: OpenAPI Sync

Este documento describe la solución implementada para la **discrepancia crítica** identificada entre frontend y backend en la sincronización de especificaciones OpenAPI.

## ❌ Problema Anterior

- `orval.config.ts` estaba configurado para leer desde archivo estático `src/lib/openapi/openapi.json`
- Sin sincronización automática con el backend
- Errores de integración difíciles de detectar hasta producción
- Desactualización manual del cliente API

## ✅ Solución Implementada

### 1. **Configuración Dinámica de Orval**

El archivo `orval.config.ts` ahora:
- **Desarrollo**: Lee directamente desde `${backendUrl}/api/openapi.json`
- **Producción**: Usa archivo estático como fallback
- **Configurable**: Variables de entorno para controlar el comportamiento

```typescript
// Configuración inteligente basada en entorno
const useDirectUrl = !isProduction && !isStaticBuild && !forceStaticFile;
```

### 2. **Scripts de NPM Mejorados**

| Script | Descripción | Uso |
|--------|-------------|-----|
| `npm run dev` | Desarrollo con sync automático | Uso diario |
| `npm run sync-api` | Sincronización inteligente | Sync manual |
| `npm run sync-api:force` | Forzar sync con archivo estático | Backup |
| `npm run build:production` | Build para producción | CI/CD |

### 3. **Variables de Entorno**

- `NEXT_PUBLIC_API_URL`: URL del backend (default: `http://localhost:8001`)
- `ORVAL_USE_STATIC`: Forzar uso de archivo estático
- `NODE_ENV`: Determina estrategia automática

## 🚀 Flujo de Trabajo

### Diagrama de Flujo de Sincronización

```mermaid
graph TD
    A["👨‍💻 Developer"] --> B["🔧 npm run dev"]
    B --> C{{"🔍 Environment Check"}}
    
    C -->|Development| D["📡 Direct URL Mode"]
    C -->|Production/CI| E["📁 Static File Mode"]
    
    D --> F["🌐 Backend Running?"]
    F -->|Yes| G["✅ orval reads from<br/>localhost:8001/api/openapi.json"]
    F -->|No| H["⚠️ Fallback to<br/>fetch-openapi + static file"]
    
    E --> I["📥 fetch-openapi.ts"]
    I --> J["💾 Save to<br/>src/lib/openapi/openapi.json"]
    J --> K["📖 orval reads<br/>static file"]
    
    H --> I
    G --> L["🔄 Generate TypeScript Client"]
    K --> L
    
    L --> M["📝 src/lib/generated/rayuelaAPI.ts"]
    M --> N["🚀 Next.js App with<br/>Synchronized API Client"]
    
    O["🏭 Backend API Changes"] --> P["📋 New OpenAPI Spec"]
    P --> Q{{"🔄 Auto-sync?"}}
    Q -->|Development| G
    Q -->|Production| R["📦 CI/CD Pipeline"]
    R --> I
    
    style A fill:#e1f5fe
    style G fill:#c8e6c9
    style K fill:#fff3e0
    style N fill:#f3e5f5
    style O fill:#ffebee
```

### Desarrollo Local
```bash
# 1. Iniciar backend
cd rayuela_backend && python main.py

# 2. Iniciar frontend (sync automático)
cd rayuela_frontend && npm run dev
```

### Sincronización Manual
```bash
# Sincronización inteligente
npm run sync-api

# Si el backend no está disponible
npm run sync-api:force
```

### Build de Producción
```bash
# Fetch + static file + build
npm run build:production
```

## 🔧 Configuración por Entorno

### Desarrollo
- ✅ URL directa al backend
- ✅ Sincronización automática
- ✅ Detección de cambios en tiempo real

### Staging/CI
- ✅ Fetch + archivo estático
- ✅ Validación de especificación
- ✅ Build determinístico

### Producción
- ✅ Archivo estático versionado
- ✅ Sin dependencias de backend en build
- ✅ Performance optimizada

## 📋 Checklist de Migración

- [x] Modificar `orval.config.ts` para configuración dinámica
- [x] Actualizar scripts de `package.json`
- [x] Crear script de sincronización `scripts/sync-api.js`
- [x] Documentar nuevo flujo de trabajo

## 🎯 Beneficios

1. **Sincronización Automática**: El frontend siempre refleja la API actual
2. **Detección Temprana**: Errores de integración en desarrollo, no en producción
3. **Flexibilidad**: Funciona en todos los entornos
4. **Mantenibilidad**: Reduce trabajo manual y errores humanos

## 🔍 Troubleshooting

### Backend no disponible
```bash
⚠️ Backend no está corriendo en localhost:8001
💡 Opciones:
   1. Iniciar backend: cd rayuela_backend && python main.py
   2. Forzar sincronización: npm run sync-api -- --force
```

### Errores de generación
```bash
# Limpiar y regenerar
rm -rf src/lib/generated/
npm run sync-api:force
```

### Build failure
```bash
# Verificar archivo OpenAPI existe
ls -la src/lib/openapi/openapi.json

# Regenerar si necesario
npm run fetch-openapi:force
npm run build:production
```

---

**Esta solución resuelve la discrepancia crítica identificada, garantizando sincronización automática entre frontend y backend mientras mantiene la flexibilidad para diferentes entornos de deployment.** 