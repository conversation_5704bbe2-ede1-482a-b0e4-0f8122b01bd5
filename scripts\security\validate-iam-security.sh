#!/bin/bash

# =============================================================================
# Script: Validate IAM Security Configuration
# Description: Validates that service accounts follow least privilege principles
#              and checks for security misconfigurations
# Usage: ./validate-iam-security.sh PROJECT_ID
# Security Reference: OWASP Top 10 A05:2021 – Security Misconfiguration
# =============================================================================

set -euo pipefail

# Check if project ID is provided
if [ $# -ne 1 ]; then
    echo "❌ Error: Project ID is required"
    echo "Usage: $0 PROJECT_ID"
    exit 1
fi

PROJECT_ID="$1"

echo "🔍 IAM Security Validation for Project: $PROJECT_ID"
echo "🔒 Checking for security misconfigurations and excessive permissions"
echo ""

# =============================================================================
# DEFINE SECURITY RULES AND EXPECTED CONFIGURATIONS
# =============================================================================

# Expected service accounts
EXPECTED_SA=(
    "rayuela-cloudbuild-sa@$PROJECT_ID.iam.gserviceaccount.com"
    "rayuela-backend-sa@$PROJECT_ID.iam.gserviceaccount.com"
    "rayuela-frontend-sa@$PROJECT_ID.iam.gserviceaccount.com"
    "rayuela-worker-sa@$PROJECT_ID.iam.gserviceaccount.com"
)

# Dangerous roles that should never be assigned
DANGEROUS_ROLES=(
    "roles/editor"
    "roles/owner"
    "roles/iam.serviceAccountAdmin"
    "roles/resourcemanager.projectIamAdmin"
    "roles/compute.admin"
    "roles/storage.admin"
    "roles/cloudsql.admin"
)

# Expected roles per service account
declare -A EXPECTED_ROLES_CLOUDBUILD=(
    ["roles/run.admin"]=1
    ["roles/secretmanager.secretAccessor"]=1
    ["roles/cloudsql.client"]=1
    ["roles/storage.objectAdmin"]=1
    ["roles/artifactregistry.writer"]=1
    ["roles/compute.networkViewer"]=1
)

declare -A EXPECTED_ROLES_BACKEND=(
    ["roles/secretmanager.secretAccessor"]=1
    ["roles/cloudsql.client"]=1
    ["roles/redis.editor"]=1
    ["roles/storage.objectViewer"]=1
)

declare -A EXPECTED_ROLES_FRONTEND=(
    ["roles/secretmanager.secretAccessor"]=1
)

declare -A EXPECTED_ROLES_WORKER=(
    ["roles/secretmanager.secretAccessor"]=1
    ["roles/cloudsql.client"]=1
    ["roles/redis.editor"]=1
    ["roles/storage.objectAdmin"]=1
)

# =============================================================================
# VALIDATION FUNCTIONS
# =============================================================================

# Initialize counters
TOTAL_CHECKS=0
PASSED_CHECKS=0
WARNINGS=0
ERRORS=0

check_passed() {
    ((TOTAL_CHECKS++))
    ((PASSED_CHECKS++))
    echo "  ✅ $1"
}

check_warning() {
    ((TOTAL_CHECKS++))
    ((WARNINGS++))
    echo "  ⚠️  WARNING: $1"
}

check_error() {
    ((TOTAL_CHECKS++))
    ((ERRORS++))
    echo "  ❌ ERROR: $1"
}

# =============================================================================
# 1. CHECK SERVICE ACCOUNT EXISTENCE
# =============================================================================
echo "📋 1. Checking Service Account Existence"
echo "========================================"

for sa_email in "${EXPECTED_SA[@]}"; do
    if gcloud iam service-accounts describe "$sa_email" --project="$PROJECT_ID" >/dev/null 2>&1; then
        check_passed "Service account exists: $sa_email"
    else
        check_error "Missing service account: $sa_email"
    fi
done

# =============================================================================
# 2. CHECK FOR DANGEROUS ROLE ASSIGNMENTS
# =============================================================================
echo ""
echo "🚨 2. Checking for Dangerous Role Assignments"
echo "=============================================="

for sa_email in "${EXPECTED_SA[@]}"; do
    echo "  Analyzing: $sa_email"
    
    # Get all roles for this service account
    SA_ROLES=$(gcloud projects get-iam-policy "$PROJECT_ID" \
        --flatten="bindings[].members" \
        --format="value(bindings.role)" \
        --filter="bindings.members:serviceAccount:$sa_email" 2>/dev/null || echo "")
    
    if [ -z "$SA_ROLES" ]; then
        check_warning "No roles found for $sa_email"
        continue
    fi
    
    # Check for dangerous roles
    DANGEROUS_FOUND=false
    while read -r role; do
        if [[ " ${DANGEROUS_ROLES[*]} " =~ " ${role} " ]]; then
            check_error "Dangerous role assigned to $sa_email: $role"
            DANGEROUS_FOUND=true
        fi
    done <<< "$SA_ROLES"
    
    if [ "$DANGEROUS_FOUND" = false ]; then
        check_passed "No dangerous roles found for $sa_email"
    fi
done

# =============================================================================
# 3. VALIDATE EXPECTED ROLE ASSIGNMENTS
# =============================================================================
echo ""
echo "🔑 3. Validating Expected Role Assignments"
echo "=========================================="

validate_sa_roles() {
    local sa_email="$1"
    local -n expected_roles_ref=$2
    local sa_name="$3"
    
    echo "  Validating: $sa_name ($sa_email)"
    
    # Get actual roles
    ACTUAL_ROLES=$(gcloud projects get-iam-policy "$PROJECT_ID" \
        --flatten="bindings[].members" \
        --format="value(bindings.role)" \
        --filter="bindings.members:serviceAccount:$sa_email" 2>/dev/null || echo "")
    
    if [ -z "$ACTUAL_ROLES" ]; then
        check_warning "No roles assigned to $sa_email"
        return
    fi
    
    # Check if all expected roles are present
    for expected_role in "${!expected_roles_ref[@]}"; do
        if echo "$ACTUAL_ROLES" | grep -q "^$expected_role$"; then
            check_passed "Required role present: $expected_role"
        else
            check_error "Missing required role for $sa_name: $expected_role"
        fi
    done
    
    # Check for unexpected roles
    while read -r actual_role; do
        if [ -n "$actual_role" ] && [ ! "${expected_roles_ref[$actual_role]+_}" ]; then
            # Skip dangerous roles as they're checked separately
            if [[ ! " ${DANGEROUS_ROLES[*]} " =~ " ${actual_role} " ]]; then
                check_warning "Unexpected role for $sa_name: $actual_role"
            fi
        fi
    done <<< "$ACTUAL_ROLES"
}

# Validate each service account
validate_sa_roles "rayuela-cloudbuild-sa@$PROJECT_ID.iam.gserviceaccount.com" EXPECTED_ROLES_CLOUDBUILD "Cloud Build"
validate_sa_roles "rayuela-backend-sa@$PROJECT_ID.iam.gserviceaccount.com" EXPECTED_ROLES_BACKEND "Backend"
validate_sa_roles "rayuela-frontend-sa@$PROJECT_ID.iam.gserviceaccount.com" EXPECTED_ROLES_FRONTEND "Frontend"
validate_sa_roles "rayuela-worker-sa@$PROJECT_ID.iam.gserviceaccount.com" EXPECTED_ROLES_WORKER "Worker"

# =============================================================================
# 4. CHECK CLOUD BUILD CONFIGURATION
# =============================================================================
echo ""
echo "🏗️  4. Checking Cloud Build Configuration"
echo "========================================"

# Check if Cloud Build configurations use the dedicated service account
CLOUDBUILD_FILES=("cloudbuild.yaml" "cloudbuild-deploy-production.yaml" "cloudbuild-deploy-frontend-only.yaml")

for file in "${CLOUDBUILD_FILES[@]}"; do
    if [ -f "$file" ]; then
        if grep -q "serviceAccount.*rayuela-cloudbuild-sa" "$file"; then
            check_passed "Cloud Build file $file uses dedicated service account"
        else
            check_warning "Cloud Build file $file may not be using dedicated service account"
        fi
    else
        check_warning "Cloud Build file not found: $file"
    fi
done

# =============================================================================
# 5. CHECK CLOUD RUN CONFIGURATION
# =============================================================================
echo ""
echo "🏃 5. Checking Cloud Run Service Account Usage"
echo "=============================================="

# This would typically require checking the actual Cloud Run services
# For now, we'll check if the configurations exist in the deployment files

check_cloudrun_sa_usage() {
    local service_name="$1"
    local expected_sa="$2"
    
    # Check if the service exists and uses the correct service account
    if gcloud run services describe "$service_name" --region=us-central1 --project="$PROJECT_ID" >/dev/null 2>&1; then
        ACTUAL_SA=$(gcloud run services describe "$service_name" --region=us-central1 --project="$PROJECT_ID" \
            --format="value(spec.template.spec.serviceAccountName)" 2>/dev/null || echo "")
        
        if [ "$ACTUAL_SA" = "$expected_sa" ]; then
            check_passed "Cloud Run service $service_name uses correct service account"
        else
            check_warning "Cloud Run service $service_name uses: $ACTUAL_SA (expected: $expected_sa)"
        fi
    else
        check_warning "Cloud Run service not found: $service_name"
    fi
}

# Note: These checks require the services to be deployed
echo "  Note: Cloud Run service account validation requires deployed services"

# =============================================================================
# 6. GENERATE SECURITY REPORT
# =============================================================================
echo ""
echo "📊 SECURITY VALIDATION REPORT"
echo "=============================="
echo ""
echo "🔢 Test Results:"
echo "   • Total Checks: $TOTAL_CHECKS"
echo "   • Passed: $PASSED_CHECKS"
echo "   • Warnings: $WARNINGS"
echo "   • Errors: $ERRORS"
echo ""

# Calculate security score
if [ $TOTAL_CHECKS -gt 0 ]; then
    SECURITY_SCORE=$(( (PASSED_CHECKS * 100) / TOTAL_CHECKS ))
    echo "📈 Security Score: $SECURITY_SCORE%"
else
    echo "📈 Security Score: N/A (No checks performed)"
    SECURITY_SCORE=0
fi

echo ""
if [ $ERRORS -eq 0 ] && [ $WARNINGS -eq 0 ]; then
    echo "🎉 EXCELLENT: All security checks passed!"
    echo "   ✅ IAM configuration follows security best practices"
    echo "   ✅ Principle of least privilege is properly implemented"
elif [ $ERRORS -eq 0 ]; then
    echo "✅ GOOD: No critical security issues found"
    echo "   ⚠️  Some warnings detected - review and address if needed"
elif [ $ERRORS -le 3 ]; then
    echo "⚠️  MODERATE: Some security issues detected"
    echo "   🔧 Address the errors to improve security posture"
else
    echo "❌ CRITICAL: Multiple security issues detected"
    echo "   🚨 Immediate action required to fix security misconfigurations"
fi

echo ""
echo "🔗 Security References:"
echo "   • OWASP Top 10 A05:2021 – Security Misconfiguration"
echo "   • GCP IAM Best Practices"
echo "   • Principle of Least Privilege"
echo ""

# Exit with appropriate code
if [ $ERRORS -eq 0 ]; then
    echo "✅ Security validation completed successfully"
    exit 0
else
    echo "❌ Security validation failed - $ERRORS error(s) found"
    exit 1
fi 