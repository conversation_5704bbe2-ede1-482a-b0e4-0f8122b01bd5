from sqlalchemy.ext.asyncio import AsyncSession
from src.utils.base_logger import log_error, log_info
from src.utils.audit_tasks import write_audit_log_to_db_task
from typing import Dict, Any, Optional


class SystemEventLogger:
    """
    Clase para registrar eventos del sistema en el log de auditoría.

    Esta clase es el mecanismo recomendado para registrar eventos del sistema
    que no están asociados a solicitudes HTTP, como:
    - Finalización de tareas de entrenamiento
    - Operaciones por lotes
    - Eventos programados
    - Procesos en segundo plano

    Para solicitudes HTTP, el AuditMiddleware ya registra automáticamente
    toda la información necesaria.
    """

    async def log_system_event(
        self,
        account_id: int,
        action: str,
        entity_type: str,
        entity_id: int = 0,
        success: bool = True,
        details: Optional[Dict[str, Any]] = None,
        performed_by: str = "system",
    ):
        """Método general para registrar eventos del sistema."""
        # Registrar en logs estándar
        status = "SUCCESS" if success else "FAILURE"
        log_info(
            f"System Event: {action} [{entity_type}] [Status: {status}] [Account: {account_id}]"
        )

        # Preparar datos para auditoría
        audit_data = {
            "account_id": account_id,
            "action": action,
            "entity_type": entity_type,
            "entity_id": entity_id,
            "changes": {"status": status},
            "performed_by": performed_by,
            "details": details,
        }

        try:
            # Usar el mecanismo consolidado de auditoría
            await write_audit_log_to_db_task(audit_data)
        except Exception as e:
            log_error(f"Error registrando evento del sistema: {e}", exc_info=True)

    async def log_training_event(
        self,
        account_id: int,
        success: bool,
        model_id: int = 0,
        error_message: Optional[str] = None,
    ):
        """Registra eventos de entrenamiento."""
        details = {"error": error_message} if error_message else None
        await self.log_system_event(
            account_id=account_id,
            action="TRAINING",
            entity_type="MODEL",
            entity_id=model_id,
            success=success,
            details=details,
        )

    async def log_recommendation_event(
        self,
        account_id: int,
        success: bool,
        user_id: int = 0,
        num_recommendations: Optional[int] = None,
        error_message: Optional[str] = None,
    ):
        """Registra eventos de recomendación."""
        details = (
            {"num_recommendations": num_recommendations, "error": error_message}
            if num_recommendations or error_message
            else None
        )

        await self.log_system_event(
            account_id=account_id,
            action="RECOMMENDATION",
            entity_type="USER",
            entity_id=user_id,
            success=success,
            details=details,
        )
