'use client';

import React, { useState, useEffect } from 'react';
import {
  Dialog,
  DialogContent,
  DialogHeader,
  DialogTitle,
  DialogDescription,
  DialogFooter,
} from '@/components/ui/dialog';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Alert, AlertDescription, AlertTitle } from '@/components/ui/alert';
import { Copy, Check, Save, Download, AlertTriangle, Terminal } from 'lucide-react';
import { toast } from 'sonner';
import { Checkbox } from '@/components/ui/checkbox';
import { Tooltip, TooltipContent, TooltipProvider, TooltipTrigger } from '@/components/ui/tooltip';

interface InitialApiKeyModalProps {
  apiKey: string;
  onClose: () => void;
}

const InitialApiKeyModal: React.FC<InitialApiKeyModalProps> = ({ apiKey, onClose }) => {
  const [copied, setCopied] = useState(false);
  const [codeCopied, setCodeCopied] = useState(false);
  const [confirmed, setConfirmed] = useState(false);
  const [downloadedOrCopied, setDownloadedOrCopied] = useState(false);
  const [showWarning, setShowWarning] = useState(false);

  // Get API base URL for the code snippet
  const apiBaseUrl = process.env.NEXT_PUBLIC_API_URL || 'http://localhost:8001/api/v1';

  // Hello World code snippet
  const helloWorldSnippet = `curl -X GET "${apiBaseUrl}/health/auth" \\
  -H "X-API-Key: ${apiKey}"`;

  // Mostrar advertencia si el usuario intenta cerrar sin confirmar
  useEffect(() => {
    const handleBeforeUnload = (e: BeforeUnloadEvent) => {
      if (!confirmed) {
        e.preventDefault();
        e.returnValue = '';
        return '';
      }
    };

    window.addEventListener('beforeunload', handleBeforeUnload);
    return () => window.removeEventListener('beforeunload', handleBeforeUnload);
  }, [confirmed]);

  const handleCopy = () => {
    navigator.clipboard
      .writeText(apiKey)
      .then(() => {
        setCopied(true);
        setDownloadedOrCopied(true);
        toast.success('¡API Key copiada al portapapeles!');
        setTimeout(() => setCopied(false), 2000);
      })
      .catch((err) => {
        toast.error('Error al copiar la API Key.');
        console.error('Error al copiar al portapapeles:', err);
      });
  };

  const handleCopyCode = (code: string, type: string) => {
    navigator.clipboard
      .writeText(code)
      .then(() => {
        setCodeCopied(true);
        toast.success(`¡Código ${type} copiado al portapapeles!`);
        setTimeout(() => setCodeCopied(false), 2000);
      })
      .catch((err) => {
        toast.error('Error al copiar el código.');
        console.error('Error al copiar al portapapeles:', err);
      });
  };

  const handleDownload = () => {
    try {
      // Crear un archivo de texto con la API Key
      const blob = new Blob(
        [
          `API Key de Rayuela\n`,
          `----------------\n`,
          `Fecha: ${new Date().toLocaleString()}\n`,
          `API Key: ${apiKey}\n\n`,
          `IMPORTANTE: Guarda este archivo en un lugar seguro. Esta clave no se mostrará completa nuevamente.`,
        ],
        { type: 'text/plain' },
      );

      // Crear un enlace para descargar el archivo
      const url = URL.createObjectURL(blob);
      const a = document.createElement('a');
      a.href = url;
      a.download = 'rayuela-api-key.txt';
      document.body.appendChild(a);
      a.click();

      // Limpiar
      setTimeout(() => {
        document.body.removeChild(a);
        URL.revokeObjectURL(url);
      }, 0);

      setDownloadedOrCopied(true);
      toast.success('API Key descargada como archivo de texto');
    } catch (err) {
      toast.error('Error al descargar la API Key');
      console.error('Error al descargar:', err);
    }
  };

  const handleCloseAttempt = () => {
    if (!downloadedOrCopied) {
      setShowWarning(true);
      return;
    }

    onClose();
  };

  return (
    <Dialog open={true} onOpenChange={(isOpen) => !isOpen && handleCloseAttempt()}>
      <DialogContent className="sm:max-w-[650px] max-h-[90vh] overflow-y-auto">
        <DialogHeader>
          <DialogTitle className="text-xl font-bold flex items-center gap-2">
            <span className="text-2xl">🔑</span> Tu API Key está lista
          </DialogTitle>
          <DialogDescription className="py-2">
            <p className="mb-3">
              ¡Bienvenido a Rayuela! Tu API Key se ha generado automáticamente. Úsala para
              autenticar todas tus solicitudes a la API.
            </p>

            <Alert variant="warning">
              <AlertTriangle className="h-4 w-4" />
              <AlertTitle>⚠️ Solo se muestra una vez</AlertTitle>
              <AlertDescription>
                Copia y guarda tu API Key ahora. No podrás verla completa nuevamente.
              </AlertDescription>
            </Alert>
          </DialogDescription>
        </DialogHeader>

        <div className="flex flex-col space-y-4 mt-2">
          {/* API Key Section */}
          <div>
            <label className="text-sm font-medium mb-2 block">Tu API Key:</label>
            <div className="flex items-center space-x-2">
              <Input
                id="apiKey"
                readOnly
                value={apiKey}
                className="flex-1 font-mono text-sm bg-muted border-2"
              />
              <Button type="button" size="sm" onClick={handleCopy} className="min-w-[90px]">
                {copied ? <Check className="h-4 w-4 mr-2" /> : <Copy className="h-4 w-4 mr-2" />}
                {copied ? '¡Copiado!' : 'Copiar'}
              </Button>
            </div>
          </div>

          {/* Hello World Section */}
          <div className="bg-success-light border border-success/20 rounded-lg p-4">
            <div className="flex items-center gap-2 mb-3">
              <Terminal className="h-5 w-5 text-success" />
              <h4 className="font-semibold text-success-foreground">
                🚀 Prueba tu API Key ahora
              </h4>
            </div>
            <p className="text-sm text-success-foreground mb-3">
              Ejecuta este comando para verificar que tu API Key funciona:
            </p>

            <div className="bg-card border rounded-md p-3 relative">
              <pre className="text-info text-xs overflow-x-auto">
                <code>{helloWorldSnippet}</code>
              </pre>
              <Button
                type="button"
                size="sm"
                variant="ghost"
                onClick={() => handleCopyCode(helloWorldSnippet, 'cURL')}
                className="absolute top-2 right-2 h-8 w-8 p-0 text-muted-foreground hover:text-foreground"
              >
                {codeCopied ? <Check className="h-4 w-4" /> : <Copy className="h-4 w-4" />}
              </Button>
            </div>

            <p className="text-xs text-success-foreground mt-2">
              Deberías recibir una respuesta con status 200 y un mensaje de bienvenida.
            </p>
          </div>

          {/* Acciones */}
          <div className="flex items-center justify-between gap-4 pt-4">
            <div className="flex items-center gap-3">
              <Button
                type="button"
                onClick={handleDownload}
                variant="outline"
                size="sm"
                className="flex items-center gap-2"
              >
                <Download className="h-4 w-4" />
                Descargar como archivo
              </Button>
            </div>

            <div className="flex items-center space-x-2">
              <Checkbox
                id="confirmSaved"
                checked={confirmed}
                onCheckedChange={(checked) => setConfirmed(checked as boolean)}
              />
              <label
                htmlFor="confirmSaved"
                className="text-sm font-medium leading-none peer-disabled:cursor-not-allowed peer-disabled:opacity-70"
              >
                He guardado mi API Key de forma segura
              </label>
            </div>
          </div>

          {/* Información adicional */}
          <Alert variant="info">
            <AlertTriangle className="h-4 w-4" />
            <AlertTitle>Información importante</AlertTitle>
            <AlertDescription>
              <ul className="list-disc list-inside space-y-1 text-sm mt-2">
                <li>Usa esta API Key en el header <code className="text-code-inline">X-API-Key</code> de tus solicitudes</li>
                <li>No compartas tu API Key públicamente</li>
                <li>Puedes crear múltiples API Keys desde tu panel de control</li>
                <li>Si pierdes tu API Key, puedes crear una nueva y revocar la anterior</li>
              </ul>
            </AlertDescription>
          </Alert>
        </div>

        <DialogFooter>
          <Button
            onClick={onClose}
            disabled={!downloadedOrCopied}
            className="w-full"
          >
            {downloadedOrCopied ? 'Continuar al Dashboard' : 'Primero copia o descarga tu API Key'}
          </Button>
        </DialogFooter>

        {/* Modal de advertencia */}
        {showWarning && (
          <Alert variant="warning" className="mt-4">
            <AlertTriangle className="h-4 w-4" />
            <AlertTitle>⚠️ Advertencia</AlertTitle>
            <AlertDescription>
              <p className="text-sm mb-3">
                Aún no has copiado o descargado tu API Key. Esta es la única vez que podrás verla completa.
              </p>
              <div className="flex gap-2">
                <Button onClick={handleCopy} size="sm" variant="outline">
                  <Copy className="h-4 w-4 mr-2" />
                  Copiar API Key
                </Button>
                <Button onClick={handleDownload} size="sm" variant="outline">
                  <Download className="h-4 w-4 mr-2" />
                  Descargar
                </Button>
                <Button onClick={() => setShowWarning(false)} size="sm" variant="ghost">
                  Cancelar
                </Button>
              </div>
            </AlertDescription>
          </Alert>
        )}
      </DialogContent>
    </Dialog>
  );
};

export default InitialApiKeyModal;
