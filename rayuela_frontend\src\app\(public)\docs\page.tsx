import { <PERSON>, CardContent, CardDescription, <PERSON><PERSON><PERSON><PERSON>, CardTitle } from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import Link from "next/link";
import { generateMetadata as generateSEOMetadata, generateJsonLd } from '@/lib/seo';
import { 
  BookOpen, 
  Code, 
  Zap, 
  Database,
  BarChart3,
  Shield,
  ArrowRight
} from "lucide-react";

export const metadata = generateSEOMetadata({
  title: 'Documentación API',
  description: 'Documentación completa de la API de Rayuela. Guías de inicio rápido, referencias de API, ejemplos de código y mejores prácticas.',
  path: '/docs',
  keywords: ['documentación', 'API', 'guía', 'tutorial', 'referencia', 'SDK', 'ejemplos'],
});

const docSections = [
  {
    icon: Zap,
    title: "Inicio Rápido",
    description: "Comienza a usar Rayuela en minutos",
    links: [
      { title: "Python", href: "/docs/quickstart/python" },
      { title: "JavaScript/Node.js", href: "/docs/quickstart/nodejs" },
      { title: "PHP", href: "/docs/quickstart/php" }
    ]
  },
  {
    icon: Code,
    title: "Referencia de API",
    description: "Documentación completa de todos los endpoints",
    links: [
      { title: "Autenticación", href: "/docs/api/authentication" },
      { title: "Recomendaciones", href: "/docs/api/recommendations" },
      { title: "Pipeline de Entrenamiento", href: "/docs/api/pipeline" }
    ]
  },
  {
    icon: Database,
    title: "Ingesta de Datos",
    description: "Cómo enviar datos de productos, usuarios e interacciones",
    links: [
      { title: "Guía de Ingesta", href: "/docs/guides/data-ingestion" },
      { title: "Formatos de Datos", href: "/docs/guides/data-formats" },
      { title: "Mejores Prácticas", href: "/docs/guides/best-practices" }
    ]
  },
  {
    icon: BarChart3,
    title: "Analytics y Métricas",
    description: "Monitorea el rendimiento de tus recomendaciones",
    links: [
      { title: "Métricas Disponibles", href: "/docs/analytics/metrics" },
      { title: "Dashboards", href: "/docs/analytics/dashboards" },
      { title: "Reportes", href: "/docs/analytics/reports" }
    ]
  },
  {
    icon: Shield,
    title: "Seguridad",
    description: "Autenticación, autorización y mejores prácticas",
    links: [
      { title: "API Keys", href: "/docs/security/api-keys" },
      { title: "JWT Tokens", href: "/docs/security/jwt" },
      { title: "Rate Limiting", href: "/docs/security/rate-limiting" }
    ]
  },
  {
    icon: BookOpen,
    title: "Guías Avanzadas",
    description: "Casos de uso específicos y configuraciones avanzadas",
    links: [
      { title: "Cold Start", href: "/docs/guides/cold-start" },
      { title: "A/B Testing", href: "/docs/guides/ab-testing" },
      { title: "Personalización", href: "/docs/guides/personalization" }
    ]
  }
];

export default function DocsPage() {
  const apiSchema = generateJsonLd('APIReference', {
    name: 'Rayuela API Documentation',
    description: 'Complete API reference for Rayuela recommendation system',
    url: 'https://rayuela.ai/docs',
  });

  return (
    <>
      <script
        type="application/ld+json"
        dangerouslySetInnerHTML={{
          __html: JSON.stringify(apiSchema),
        }}
      />
      
      <div className="min-h-screen bg-gradient-to-br from-marketing-gradient-start to-marketing-gradient-end">
        <div className="container mx-auto px-4 py-16">
          {/* Hero Section */}
          <div className="text-center mb-16">
            <h1 className="text-4xl md:text-5xl font-bold text-gray-900 dark:text-white mb-6">
              Documentación de Rayuela
            </h1>
            <p className="text-xl text-gray-600 dark:text-gray-300 max-w-3xl mx-auto mb-8">
              Todo lo que necesitas para integrar sistemas de recomendación en tu aplicación. 
              Desde guías de inicio rápido hasta referencias detalladas de API.
            </p>
            
            <div className="flex justify-center gap-4">
              <Button asChild size="lg">
                <Link href="/docs/quickstart/python">
                  <Zap className="w-5 h-5 mr-2" />
                  Inicio Rápido
                </Link>
              </Button>
              <Button variant="outline" size="lg" asChild>
                <Link href="/docs/api/recommendations">
                  <Code className="w-5 h-5 mr-2" />
                  Referencia API
                </Link>
              </Button>
            </div>
          </div>

          {/* Documentation Sections */}
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8 mb-16">
            {docSections.map((section, index) => {
              const Icon = section.icon;
              return (
                <Card key={index} className="h-full">
                  <CardHeader>
                    <div className="w-12 h-12 bg-blue-100 dark:bg-blue-900 rounded-lg flex items-center justify-center mb-4">
                      <Icon className="w-6 h-6 text-blue-600 dark:text-blue-400" />
                    </div>
                    <CardTitle className="text-xl">{section.title}</CardTitle>
                    <CardDescription>{section.description}</CardDescription>
                  </CardHeader>
                  <CardContent>
                    <ul className="space-y-2">
                      {section.links.map((link, linkIndex) => (
                        <li key={linkIndex}>
                          <Link 
                            href={link.href}
                            className="flex items-center text-blue-600 hover:text-blue-800 dark:text-blue-400 dark:hover:text-blue-300 transition-colors"
                          >
                            <ArrowRight className="w-4 h-4 mr-2" />
                            {link.title}
                          </Link>
                        </li>
                      ))}
                    </ul>
                  </CardContent>
                </Card>
              );
            })}
          </div>

          {/* Popular Guides */}
          <div className="bg-white dark:bg-gray-800 rounded-lg p-8 shadow-lg">
            <h2 className="text-3xl font-bold text-center text-gray-900 dark:text-white mb-8">
              Guías Populares
            </h2>
            
            <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
              <Link 
                href="/docs/quickstart/python"
                className="p-6 border rounded-lg hover:shadow-md transition-shadow"
              >
                <h3 className="text-lg font-semibold text-gray-900 dark:text-white mb-2">
                  🐍 Inicio Rápido con Python
                </h3>
                <p className="text-gray-600 dark:text-gray-300">
                  Integra Rayuela en tu aplicación Python en menos de 5 minutos.
                </p>
              </Link>
              
              <Link 
                href="/docs/guides/data-ingestion"
                className="p-6 border rounded-lg hover:shadow-md transition-shadow"
              >
                <h3 className="text-lg font-semibold text-gray-900 dark:text-white mb-2">
                  📊 Guía de Ingesta de Datos
                </h3>
                <p className="text-gray-600 dark:text-gray-300">
                  Aprende a enviar datos de productos, usuarios e interacciones.
                </p>
              </Link>
              
              <Link 
                href="/docs/api/recommendations"
                className="p-6 border rounded-lg hover:shadow-md transition-shadow"
              >
                <h3 className="text-lg font-semibold text-gray-900 dark:text-white mb-2">
                  🎯 API de Recomendaciones
                </h3>
                <p className="text-gray-600 dark:text-gray-300">
                  Referencia completa para obtener recomendaciones personalizadas.
                </p>
              </Link>
              
              <Link 
                href="/docs/guides/cold-start"
                className="p-6 border rounded-lg hover:shadow-md transition-shadow"
              >
                <h3 className="text-lg font-semibold text-gray-900 dark:text-white mb-2">
                  🚀 Manejo de Cold Start
                </h3>
                <p className="text-gray-600 dark:text-gray-300">
                  Estrategias para nuevos usuarios y productos sin historial.
                </p>
              </Link>
            </div>
          </div>
        </div>
      </div>
    </>
  );
}
