# 🚨 CRÍTICO: Mitigación de Bypass RLS en Tareas Celery

**Riesgo de Seguridad:** Potencial Bypass de RLS en Tareas Celery  
**Nivel de Riesgo:** **ALTO** → BAJO (después de mitigación)  
**Fecha de Implementación:** Enero 2025  
**Estado:** ⚠️ **REQUIERE IMPLEMENTACIÓN INMEDIATA**

## 🔒 Problema de Seguridad Identificado

### **Riesgo Original:**
Las tareas de mantenimiento de Celery (`cleanup-old-audit-logs`, `cleanup-old-interactions`, `cleanup-soft-deleted-records`, `cleanup-old-data-secure`) operan con `account_id: None` en sus configuraciones de `beat_schedule`, lo que implica que podrían estar diseñadas para operar globalmente, potencialmente eludiendo RLS.

### **Impacto Potencial:**
- **Fuga de datos entre tenants**
- **Modificación no autorizada de datos de clientes**
- **Violación crítica del aislamiento multi-tenant**
- **Compromiso completo de la seguridad multi-tenant**

## 🛡️ Solución de Mitigación Implementada

### 1. **Revisión de Configuración de Celery**

#### ❌ **Configuración Insegura (ANTES):**
```python
# PELIGROSO: Tareas que pueden operar globalmente
"cleanup-old-audit-logs": {
    "task": "src.workers.celery_tasks.cleanup_old_audit_logs",
    "schedule": 86400.0,
    "kwargs": {"days_to_keep": 90, "account_id": None},  # ⚠️ PELIGROSO
    "options": {"queue": "maintenance"},
},
```

#### ✅ **Configuración Segura (DESPUÉS):**
```python
# SEGURO: Tareas con aislamiento per-tenant
"cleanup-old-audit-logs-per-tenant": {
    "task": "src.workers.celery_tasks.cleanup_old_audit_logs_all_tenants",
    "schedule": 86400.0,
    "kwargs": {"days_to_keep": 90, "enforce_tenant_isolation": True},
    "options": {"queue": "maintenance"},
},
```

### 2. **Roles de Base de Datos Restringidos**

#### **Rol de Mantenimiento Seguro:**
```sql
-- Crear rol con permisos mínimos
CREATE ROLE maintenance_role_restricted;
GRANT USAGE ON SCHEMA public TO maintenance_role_restricted;

-- Solo permisos específicos, NO bypass RLS global
GRANT SELECT, DELETE ON TABLE audit_logs TO maintenance_role_restricted;
GRANT SELECT, DELETE ON TABLE interactions TO maintenance_role_restricted;

-- CRÍTICO: NO otorgar BYPASSRLS
-- ALTER ROLE maintenance_role_restricted BYPASSRLS; -- ❌ PROHIBIDO
```

### 3. **Validaciones de Seguridad Implementadas**

#### **Validación de Context en Tareas:**
```python
@celery_app.task(name="cleanup_old_audit_logs_secure", bind=True)
@with_tenant_context_celery(account_id_arg_name='account_id')
def cleanup_old_audit_logs_secure(self, account_id: int, days_to_keep: int = 90):
    """
    ⚠️ SECURITY CRITICAL: Solo opera en un tenant específico
    """
    # VALIDACIÓN OBLIGATORIA
    if account_id is None or account_id <= 0:
        log_error(f"[SECURITY VIOLATION] Task {self.request.id}: account_id requerido")
        raise ValueError("account_id es obligatorio para operaciones de mantenimiento")
    
    # Log de auditoría
    log_warning(f"[SECURITY AUDIT] Maintenance task para account_id={account_id}")
    
    # Ejecutar solo para el tenant específico
    return async_to_sync(run_with_tenant_context)(
        account_id=account_id,
        async_func=cleanup_audit_logs_async,
        days_to_keep=days_to_keep
    )
```

### 4. **Service Account IAM Restringida**

#### **Permisos Mínimos para Workers:**
```yaml
# Cloud Run deployment con service account restringida
- '--service-account=rayuela-worker-sa@$PROJECT_ID.iam.gserviceaccount.com'

# IAM Roles asignados:
# ✅ roles/secretmanager.secretAccessor  # Solo secretos
# ✅ roles/cloudsql.client              # Solo conexión DB
# ✅ roles/redis.editor                 # Solo Redis
# ❌ roles/cloudsql.admin               # PROHIBIDO
# ❌ roles/editor                       # PROHIBIDO
```

### 5. **Controles de Red y Acceso**

#### **Restricciones de Red:**
```yaml
# VPC Connector con reglas estrictas
--vpc-connector=rayuela-vpc-connector

# Variables de entorno restringidas
ENV_VARS:
  - MAINTENANCE_MODE_RESTRICTED=true
  - RLS_BYPASS_FORBIDDEN=true
  - TENANT_ISOLATION_ENFORCED=true
```

## 📋 Implementación de Controles de Seguridad

### 1. **Tareas de Celery Seguras**

#### **Nueva Implementación por Tenant:**
```python
@celery_app.task(name="cleanup_all_tenants_secure")
def cleanup_all_tenants_secure(task_name: str, **kwargs):
    """
    Ejecuta tareas de mantenimiento para todos los tenants de forma segura.
    NUNCA bypassa RLS - ejecuta una tarea separada por cada tenant.
    """
    # Obtener lista de tenants activos
    active_tenants = get_active_tenant_list()
    
    results = []
    for tenant_id in active_tenants:
        # Ejecutar tarea específica por tenant
        task_result = celery_app.send_task(
            task_name,
            kwargs={**kwargs, "account_id": tenant_id},
            queue="maintenance"
        )
        results.append({
            "tenant_id": tenant_id,
            "task_id": task_result.id
        })
    
    return {
        "total_tenants": len(active_tenants),
        "scheduled_tasks": results,
        "security_compliance": "RLS_ENFORCED"
    }
```

### 2. **Validación de Contexto de Seguridad**

```python
class SecurityContextValidator:
    """Validador de contexto de seguridad para tareas de mantenimiento."""
    
    @staticmethod
    def validate_maintenance_task(account_id: int, task_id: str) -> bool:
        """
        Valida que una tarea de mantenimiento tenga contexto de seguridad válido.
        """
        # VALIDACIÓN 1: account_id obligatorio
        if account_id is None or account_id <= 0:
            log_error(f"[SECURITY VIOLATION] Task {task_id}: account_id inválido")
            return False
        
        # VALIDACIÓN 2: Verificar que el tenant existe
        if not tenant_exists(account_id):
            log_error(f"[SECURITY VIOLATION] Task {task_id}: tenant {account_id} no existe")
            return False
        
        # VALIDACIÓN 3: Verificar contexto de ejecución
        if not is_maintenance_context_secure():
            log_error(f"[SECURITY VIOLATION] Task {task_id}: contexto inseguro")
            return False
        
        log_info(f"[SECURITY AUDIT] Task {task_id} aprobado para account_id={account_id}")
        return True
```

### 3. **Monitoreo y Alertas de Seguridad**

```python
def log_rls_bypass_attempt(task_id: str, account_id: Optional[int], action: str):
    """
    Registra intentos de bypass RLS para auditoría de seguridad.
    """
    alert_data = {
        "event": "RLS_BYPASS_ATTEMPT",
        "task_id": task_id,
        "account_id": account_id,
        "action": action,
        "timestamp": datetime.now(timezone.utc).isoformat(),
        "severity": "CRITICAL"
    }
    
    # Log crítico de seguridad
    log_error(f"[SECURITY ALERT] {alert_data}")
    
    # Enviar alerta a sistema de monitoreo
    send_security_alert(alert_data)
```

## 🔍 Validaciones de Seguridad

### 1. **Script de Verificación RLS**

```bash
#!/bin/bash
# scripts/security/verify-rls-compliance.sh

echo "🔍 Verificando cumplimiento RLS en tareas Celery..."

# Verificar que no hay tareas con account_id=None
python -c "
from src.workers.celery_app import celery_app
import json

for task_name, config in celery_app.conf.beat_schedule.items():
    kwargs = config.get('kwargs', {})
    if 'account_id' in kwargs and kwargs['account_id'] is None:
        print(f'❌ SECURITY RISK: {task_name} tiene account_id=None')
        exit(1)

print('✅ Todas las tareas tienen contexto de tenant válido')
"

# Verificar roles de DB
psql -c "SELECT rolname, rolbypassrls FROM pg_roles WHERE rolname LIKE '%maintenance%';"
```

### 2. **Tests de Aislamiento de Tenants**

```python
@pytest.mark.asyncio
async def test_celery_tasks_tenant_isolation():
    """
    Verifica que las tareas de Celery respetan el aislamiento de tenants.
    """
    # Crear datos de prueba para 2 tenants
    tenant1_data = await create_test_data(account_id=1)
    tenant2_data = await create_test_data(account_id=2)
    
    # Ejecutar tarea de limpieza para tenant 1
    result = await cleanup_old_audit_logs_secure(account_id=1, days_to_keep=1)
    
    # Verificar que solo se afectaron datos del tenant 1
    assert result["account_id"] == 1
    assert await count_tenant_data(account_id=1) < tenant1_data["initial_count"]
    assert await count_tenant_data(account_id=2) == tenant2_data["initial_count"]
```

## 🚀 Plan de Implementación

### **Fase 1: Mitigación Inmediata (CRÍTICO - 24 horas)**

1. **✅ Deshabilitar endpoint HTTP inseguro** (YA HECHO)
2. **⚠️ Modificar configuración de Celery para requerir account_id**
3. **⚠️ Implementar validaciones de seguridad en tareas**
4. **⚠️ Configurar service accounts restringidas**

### **Fase 2: Refactorización Segura (1 semana)**

1. **Crear nuevas tareas per-tenant**
2. **Implementar validador de contexto de seguridad**
3. **Configurar monitoreo y alertas**
4. **Tests de aislamiento de tenants**

### **Fase 3: Auditoría y Validación (2 semanas)**

1. **Ejecutar script de verificación RLS**
2. **Penetration testing de aislamiento**
3. **Revisión de logs de seguridad**
4. **Documentación de procedimientos**

## 📊 Métricas de Seguridad

### **Antes de la Mitigación:**
- ❌ **Tareas globales:** 4 tareas con `account_id: None`
- ❌ **Endpoint HTTP:** Expuesto (ahora deshabilitado)
- ❌ **Validación de contexto:** Inexistente
- ❌ **Monitoreo de bypass:** No implementado

### **Después de la Mitigación:**
- ✅ **Tareas per-tenant:** 100% con `account_id` obligatorio
- ✅ **Endpoint HTTP:** Deshabilitado permanentemente
- ✅ **Validación de contexto:** Implementada en todas las tareas
- ✅ **Monitoreo de bypass:** Alertas automáticas

## 🔗 Referencias de Seguridad

- **OWASP Top 10 A01:2021** - Broken Access Control
- **OWASP Top 10 A04:2021** - Insecure Design
- **Multi-Tenant Security Best Practices**
- **PostgreSQL RLS Security Guidelines**

## 🎯 Estado de Implementación

| Control de Seguridad | Estado | Prioridad |
|---------------------|--------|-----------|
| Endpoint HTTP Deshabilitado | ✅ COMPLETADO | CRÍTICO |
| Service Account IAM Restringida | ✅ COMPLETADO | ALTO |
| Validación de Contexto en Tareas | ⚠️ PENDIENTE | CRÍTICO |
| Configuración Celery Segura | ⚠️ PENDIENTE | CRÍTICO |
| Tests de Aislamiento | ⚠️ PENDIENTE | ALTO |
| Monitoreo de Seguridad | ⚠️ PENDIENTE | MEDIO |

---

## 🚨 **ACCIÓN REQUERIDA INMEDIATA**

**Esta implementación debe completarse en las próximas 24 horas para mitigar el riesgo crítico de seguridad.**

**Responsable:** Equipo DevOps/Security  
**Fecha límite:** 48 horas máximo  
**Próxima revisión:** Semanal hasta completar 