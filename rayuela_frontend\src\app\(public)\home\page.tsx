// src/app/(public)/home/<USER>
import Link from 'next/link';
import { Button } from '@/components/ui/button';
import { generateMetadata as generateSEOMetadata, generateJsonLd } from '@/lib/seo';
import { Container, Stack, Group, IconText } from '@/components/ui/spacing-system';

export const metadata = generateSEOMetadata({
  title: 'Rayuela: Personalización de E-commerce con IA para LATAM',
  description: 'Impulsa tus ventas con recomendaciones inteligentes. API-first diseñada para PyMES de E-commerce y Contenido Digital. Integración con Mercado Pago. Fácil. Rápido. Rentable.',
  path: '/',
  keywords: ['personalización e-commerce', 'recomendaciones IA', 'PyMES LATAM', 'API recomendaciones', 'Mercado Pago', 'machine learning', 'e-commerce Argentina'],
});

export default function HomePage() {
  const organizationSchema = generateJsonLd('Organization', {});
  const softwareSchema = generateJsonLd('SoftwareApplication', {
    name: 'Rayuela - Personalización E-commerce IA',
    description: 'Sistema de recomendaciones inteligentes para PyMES de E-commerce y Contenido Digital en LATAM',
  });

  return (
    <>
      <script
        type="application/ld+json"
        dangerouslySetInnerHTML={{
          __html: JSON.stringify(organizationSchema),
        }}
      />
      <script
        type="application/ld+json"
        dangerouslySetInnerHTML={{
          __html: JSON.stringify(softwareSchema),
        }}
      />

      <div className="min-h-screen bg-gradient-to-br from-marketing-gradient-start to-marketing-gradient-end">
        <div className="container mx-auto px-4 py-20">
          {/* Hero Section */}
          <div className="text-center mb-16">
            <h1 className="text-4xl md:text-6xl font-bold text-gray-900 dark:text-white mb-6">
              Rayuela: Personalización de E-commerce con IA para LATAM
            </h1>
            <p className="text-xl md:text-2xl text-gray-600 dark:text-gray-300 mb-4 max-w-4xl mx-auto">
              <strong>Fácil. Rápido. Rentable.</strong>
            </p>
            <p className="text-lg text-gray-600 dark:text-gray-300 mb-8 max-w-3xl mx-auto">
              Impulsa tus ventas y el engagement de tus usuarios con recomendaciones inteligentes, integradas vía API en cuestión de horas. Sin necesidad de expertos en ML.
            </p>
            <div className="flex flex-col sm:flex-row justify-center gap-4 mb-12">
              <Button asChild size="lg" className="text-lg px-8 py-3">
                <Link href="/register">Regístrate Gratis y Prueba Rayuela Hoy</Link>
              </Button>
              <Button variant="outline" asChild size="lg" className="text-lg px-8 py-3">
                <Link href="/contact-sales">Solicita tu Demo Personalizada</Link>
              </Button>
            </div>
          </div>

          {/* Value Propositions */}
          <div className="grid grid-cols-1 md:grid-cols-3 gap-8 mb-16">
            <div className="bg-white dark:bg-gray-800 rounded-lg p-6 shadow-lg text-center">
              <div className="w-16 h-16 bg-green-100 dark:bg-green-900 rounded-full flex items-center justify-center mx-auto mb-4">
                <span className="text-2xl">📈</span>
              </div>
              <h3 className="text-xl font-bold text-gray-900 dark:text-white mb-3">
                Aumenta la Conversión
              </h3>
              <p className="text-gray-600 dark:text-gray-300">
                Recomendaciones personalizadas que tus clientes realmente quieren, impulsando el valor de tu carrito y las compras repetidas.
              </p>
            </div>

            <div className="bg-white dark:bg-gray-800 rounded-lg p-6 shadow-lg text-center">
              <div className="w-16 h-16 bg-blue-100 dark:bg-blue-900 rounded-full flex items-center justify-center mx-auto mb-4">
                <span className="text-2xl">⚡</span>
              </div>
              <h3 className="text-xl font-bold text-gray-900 dark:text-white mb-3">
                Integración sin Dolor
              </h3>
              <p className="text-gray-600 dark:text-gray-300">
                API-first, diseñada por desarrolladores para desarrolladores. Docs claras, SDKs listos. ¡Empieza en minutos!
              </p>
            </div>

            <div className="bg-white dark:bg-gray-800 rounded-lg p-6 shadow-lg text-center">
              <div className="w-16 h-16 bg-purple-100 dark:bg-purple-900 rounded-full flex items-center justify-center mx-auto mb-4">
                <span className="text-2xl">🌎</span>
              </div>
              <h3 className="text-xl font-bold text-gray-900 dark:text-white mb-3">
                Inteligencia Mundial, Localmente
              </h3>
              <p className="text-gray-600 dark:text-gray-300">
                Algoritmos de IA/ML de vanguardia (híbridos, Learning-to-Rank) optimizados para tu negocio. Pagos con Mercado Pago.
              </p>
            </div>
          </div>

          {/* Key Benefits */}
          <div className="bg-white dark:bg-gray-800 rounded-lg p-8 shadow-lg mb-16">
            <h2 className="text-3xl font-bold text-center text-gray-900 dark:text-white mb-8">
              ¿Por qué PyMES de E-commerce eligen Rayuela?
            </h2>
            <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
              <IconText 
                icon={
                  <div className="w-6 h-6 bg-green-500 rounded-full flex items-center justify-center flex-shrink-0">
                    <span className="text-white text-sm">✓</span>
                  </div>
                }
                align="start"
              >
                <div>
                  <h4 className="font-semibold text-gray-900 dark:text-white mb-1">
                    Entiende tus Recomendaciones
                  </h4>
                  <p className="text-gray-600 dark:text-gray-300">
                    Métricas de rendimiento claras y explicaciones de por qué cada producto es recomendado. Toma decisiones basadas en datos.
                  </p>
                </div>
              </IconText>

              <div className="flex items-start space-x-3">
                <div className="w-6 h-6 bg-green-500 rounded-full flex items-center justify-center flex-shrink-0 mt-1">
                  <span className="text-white text-sm">✓</span>
                </div>
                <div>
                  <h4 className="font-semibold text-gray-900 dark:text-white mb-1">
                    Escalabilidad y Seguridad
                  </h4>
                  <p className="text-gray-600 dark:text-gray-300">
                    Arquitectura multi-tenant robusta, lista para crecer contigo, con la seguridad de tus datos garantizada.
                  </p>
                </div>
              </div>

              <div className="flex items-start space-x-3">
                <div className="w-6 h-6 bg-green-500 rounded-full flex items-center justify-center flex-shrink-0 mt-1">
                  <span className="text-white text-sm">✓</span>
                </div>
                <div>
                  <h4 className="font-semibold text-gray-900 dark:text-white mb-1">
                    Transforma en Días, no Meses
                  </h4>
                  <p className="text-gray-600 dark:text-gray-300">
                    La dificultad y el tiempo de implementar personalización avanzada es cosa del pasado. Resultados inmediatos.
                  </p>
                </div>
              </div>

              <div className="flex items-start space-x-3">
                <div className="w-6 h-6 bg-green-500 rounded-full flex items-center justify-center flex-shrink-0 mt-1">
                  <span className="text-white text-sm">✓</span>
                </div>
                <div>
                  <h4 className="font-semibold text-gray-900 dark:text-white mb-1">
                    Precios Adaptados a LATAM
                  </h4>
                  <p className="text-gray-600 dark:text-gray-300">
                    Democratización de la IA para competir con grandes jugadores. Facilidad de pago con Mercado Pago.
                  </p>
                </div>
              </div>
            </div>
          </div>

          {/* Final CTA */}
          <div className="text-center">
            <h2 className="text-3xl font-bold text-gray-900 dark:text-white mb-4">
              Comienza a Personalizar tu E-commerce Hoy
            </h2>
            <p className="text-lg text-gray-600 dark:text-gray-300 mb-6">
              Únete a las PyMES que ya están aumentando sus ventas con Rayuela
            </p>
            <Button asChild size="lg" className="text-lg px-8 py-3">
              <Link href="/register">Comenzar Gratis</Link>
            </Button>
          </div>
        </div>
      </div>
    </>
  );
}
