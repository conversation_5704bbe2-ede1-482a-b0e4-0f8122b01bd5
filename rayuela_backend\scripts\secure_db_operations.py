#!/usr/bin/env python3
"""
Secure Database Operations Script

This script provides secure database operations that should NOT be exposed 
as public API endpoints. It's designed to be run directly on the server
or via secure Cloud Run jobs.

Usage:
    python -m scripts.secure_db_operations migrate
    python -m scripts.secure_db_operations status
    python -m scripts.secure_db_operations rollback <revision>

Security Notes:
- This script requires direct server access or secure cloud job execution
- Database credentials are loaded from environment variables or Secret Manager
- All operations are logged with timestamps
- No network endpoints are exposed
"""

import asyncio
import logging
import sys
import subprocess
from pathlib import Path
from datetime import datetime
from typing import Optional

import click
from src.core.config import settings
from src.utils.base_logger import logger

@click.group()
def cli():
    """Secure Database Operations - Internal Use Only"""
    pass

@cli.command()
def migrate():
    """Apply pending database migrations securely"""
    logger.info("🔄 Starting secure database migration...")
    logger.info(f"Environment: {settings.ENV}")
    logger.info(f"Timestamp: {datetime.now().isoformat()}")
    
    try:
        # Change to backend directory for alembic
        backend_dir = Path(__file__).parent.parent
        
        # Check current migration status
        result = subprocess.run(
            ["alembic", "current"], 
            cwd=backend_dir,
            capture_output=True, 
            text=True
        )
        
        if result.returncode == 0:
            logger.info(f"📊 Current migration: {result.stdout.strip()}")
        else:
            logger.error(f"❌ Error checking migration status: {result.stderr}")
            return False
            
        # Apply migrations
        result = subprocess.run(
            ["alembic", "upgrade", "head"], 
            cwd=backend_dir,
            capture_output=True, 
            text=True
        )
        
        if result.returncode == 0:
            logger.info("✅ Migrations applied successfully")
            logger.info(result.stdout)
            
            # Show final status
            final_result = subprocess.run(
                ["alembic", "current"], 
                cwd=backend_dir,
                capture_output=True, 
                text=True
            )
            logger.info(f"📊 Final migration status: {final_result.stdout.strip()}")
            return True
        else:
            logger.error(f"❌ Migration failed: {result.stderr}")
            return False
            
    except Exception as e:
        logger.error(f"❌ Unexpected error during migration: {e}", exc_info=True)
        return False

@cli.command()
def status():
    """Check current database migration status"""
    logger.info("📊 Checking database migration status...")
    
    try:
        backend_dir = Path(__file__).parent.parent
        
        # Get current migration
        result = subprocess.run(
            ["alembic", "current"], 
            cwd=backend_dir,
            capture_output=True, 
            text=True
        )
        
        if result.returncode == 0:
            current = result.stdout.strip()
            logger.info(f"Current migration: {current}")
        else:
            logger.error(f"Error checking current: {result.stderr}")
            
        # Get migration history
        result = subprocess.run(
            ["alembic", "history", "--verbose"], 
            cwd=backend_dir,
            capture_output=True, 
            text=True
        )
        
        if result.returncode == 0:
            logger.info("Migration history:")
            logger.info(result.stdout)
        else:
            logger.error(f"Error checking history: {result.stderr}")
            
    except Exception as e:
        logger.error(f"Error checking status: {e}", exc_info=True)

@cli.command()
@click.argument('revision')
def rollback(revision: str):
    """Rollback to a specific migration revision"""
    logger.warning(f"🔄 Rolling back to revision: {revision}")
    logger.warning("⚠️  This is a potentially destructive operation!")
    
    # Require confirmation in interactive mode
    if sys.stdin.isatty():
        confirm = input(f"Are you sure you want to rollback to {revision}? (yes/no): ")
        if confirm.lower() != 'yes':
            logger.info("Rollback cancelled by user")
            return
    
    try:
        backend_dir = Path(__file__).parent.parent
        
        result = subprocess.run(
            ["alembic", "downgrade", revision], 
            cwd=backend_dir,
            capture_output=True, 
            text=True
        )
        
        if result.returncode == 0:
            logger.info(f"✅ Successfully rolled back to {revision}")
            logger.info(result.stdout)
        else:
            logger.error(f"❌ Rollback failed: {result.stderr}")
            
    except Exception as e:
        logger.error(f"Error during rollback: {e}", exc_info=True)

@cli.command()
def verify_connection():
    """Verify database connection without making changes"""
    logger.info("🔌 Verifying database connection...")
    
    try:
        from src.db.session import get_database_url
        from sqlalchemy import create_engine, text
        
        database_url = get_database_url()
        engine = create_engine(database_url)
        
        with engine.connect() as conn:
            result = conn.execute(text("SELECT version()"))
            version = result.fetchone()[0]
            logger.info(f"✅ Database connection successful")
            logger.info(f"PostgreSQL version: {version}")
            
        engine.dispose()
        return True
        
    except Exception as e:
        logger.error(f"❌ Database connection failed: {e}")
        return False

if __name__ == "__main__":
    # Ensure we're in the correct environment
    if settings.ENV == "production":
        logger.info("🔒 Running in PRODUCTION mode - all operations will be logged")
    
    cli() 