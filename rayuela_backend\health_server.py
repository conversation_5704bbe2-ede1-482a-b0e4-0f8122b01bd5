import os
import asyncio
from aiohttp import web
import subprocess
import sys

async def health_check(request):
    try:
        worker_type = os.getenv('WORKER_TYPE', 'default')
        
        if worker_type == 'beat':
            # For Celery Beat, just check if the process is running
            # <PERSON> doesn't respond to ping commands like workers do
            result = subprocess.run(
                ["pgrep", "-f", "celery.*beat"],
                capture_output=True,
                text=True,
                timeout=5
            )
            if result.returncode == 0:
                return web.Response(text="Celery Beat OK", status=200)
            else:
                return web.Response(text="Celery Beat not running", status=503)
        else:
            # Check if Celery worker is responding
            result = subprocess.run(
                ["celery", "-A", "src.workers.celery_app", "inspect", "ping", "-d", f"celery@{os.getenv('HOSTNAME', 'localhost')}"],
                capture_output=True,
                text=True,
                timeout=5
            )
            if result.returncode == 0:
                return web.Response(text="Celery Worker OK", status=200)
            else:
                return web.Response(text="Celery worker not responding", status=503)
    except Exception as e:
        return web.Response(text=f"Health check failed: {str(e)}", status=503)

async def start_health_server():
    app = web.Application()
    app.router.add_get('/health', health_check)
    app.router.add_get('/', health_check)
    
    port = int(os.getenv('PORT', 8080))
    runner = web.AppRunner(app)
    await runner.setup()
    site = web.TCPSite(runner, '0.0.0.0', port)
    await site.start()
    print(f"Health server started on port {port}")

if __name__ == "__main__":
    async def main():
        await start_health_server()
        # Keep the server running indefinitely
        try:
            while True:
                await asyncio.sleep(3600)  # Sleep for 1 hour at a time
        except KeyboardInterrupt:
            print("Health server shutting down...")
    
    asyncio.run(main()) 