// Ruta EXACTA: rayuela-frontend/src/components/dashboard/Header.tsx
"use client";
// Asegúrate de haber añadido 'button' con shadcn: npx shadcn-ui@latest add button
import { Button } from '@/components/ui/button';
import { Logo } from '@/components/ui/logo';
import { useAuth } from '@/lib/auth'; // Importamos el hook de autenticación
import { Group } from '@/components/ui/spacing-system';

export default function Header() {
  // Obtenemos la función logout del contexto de autenticación
  const { logout, user } = useAuth(); // También podrías obtener 'user' si quieres mostrar el email, etc.

  return (
    <header className="bg-card shadow-sm border-b border-border h-16 shrink-0">
      <div className="flex justify-between items-center h-full px-4">
        {/* Puedes añadir un título o breadcrumbs aquí si quieres */}
        <Group>
          {/* Icono de Menú para móvil (opcional) */}
          {/* <button className="md:hidden">...</button> */}
          <Logo className="text-foreground hidden md:block" />
          <span className="text-heading text-foreground">Dashboard</span>
        </Group>

        <Group spacing="normal">
          {/* Aquí podrías añadir notificaciones, selector de tema, etc. */}
          {user && ( // Muestra el botón de logout solo si hay un usuario
            <Button onClick={logout} variant="outline" size="sm">
              Logout
            </Button>
          )}
        </Group>
      </div>
    </header>
  );
}
