// src/app/(public)/login/page.tsx
"use client";

import React, { useState, useEffect } from 'react';
import { useForm } from 'react-hook-form';
import { zodResolver } from '@hookform/resolvers/zod';
import * as z from 'zod';
import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card";
import { Alert, AlertDescription, AlertTitle } from "@/components/ui/alert";
import { useAuth } from '@/lib/auth';

import { toast } from "sonner";
import Link from 'next/link';
import { MailIcon, AlertCircleIcon } from 'lucide-react';

// Esquema de validación con Zod
const loginSchema = z.object({
  email: z.string().email({ message: "Email inválido." }),
  password: z.string().min(8, { message: "Contraseña debe tener al menos 8 caracteres." }),
});

type LoginFormValues = z.infer<typeof loginSchema>;

export default function LoginPage() {
  const [isLoading, setIsLoading] = useState(false);
  const [isResendingEmail, setIsResendingEmail] = useState(false);
  const { login, emailVerificationError, requestNewVerificationEmail } = useAuth();

  const form = useForm<LoginFormValues>({
    resolver: zodResolver(loginSchema),
    defaultValues: {
      email: emailVerificationError?.email || '',
      password: emailVerificationError?.password || '',
    },
  });

  // Actualizar los valores del formulario si cambia emailVerificationError
  useEffect(() => {
    if (emailVerificationError) {
      form.setValue('email', emailVerificationError.email);
      form.setValue('password', emailVerificationError.password);
    }
  }, [emailVerificationError, form]);

  // Función para manejar el reenvío del email de verificación
  const handleResendVerificationEmail = async () => {
    setIsResendingEmail(true);
    try {
      const success = await requestNewVerificationEmail();
      if (success) {
        toast.success("Email de verificación enviado. Por favor, revisa tu bandeja de entrada.");
      }
    } catch (error: unknown) {
      console.error("Error al reenviar email de verificación:", error);
      toast.error((error as Error).message || "Error al reenviar email de verificación.");
    } finally {
      setIsResendingEmail(false);
    }
  };

  const onSubmit = async (data: LoginFormValues) => {
    setIsLoading(true);
    try {
      // Llamar directamente a la función login del contexto
      const success = await login(data.email, data.password);

      if (!success) {
        // El mensaje de error ya lo muestra la función login
        console.error("Login failed");
      }
      // La redirección la maneja el hook useAuth
    } catch (error: unknown) {
      console.error("Login failed:", error);
      toast.error((error as Error).message || "Error al iniciar sesión. Verifica tus credenciales.");
    } finally {
      setIsLoading(false);
    }
  };

  return (
    <div className="flex items-center justify-center min-h-screen bg-gray-100">
      <Card className="w-full max-w-md">
        <CardHeader>
          <CardTitle>Iniciar Sesión</CardTitle>
          <CardDescription>Accede a tu panel de Rayuela.</CardDescription>
        </CardHeader>
        <CardContent>
          {/* Alerta de verificación de email */}
          {emailVerificationError && (
            <Alert className="mb-6 bg-amber-50 border-amber-200">
              <AlertCircleIcon className="h-4 w-4 text-amber-600" />
              <AlertTitle className="text-amber-800">Verificación de email requerida</AlertTitle>
              <AlertDescription className="text-amber-700">
                {emailVerificationError.message}
                <Button
                  variant="outline"
                  size="sm"
                  className="mt-2 border-amber-300 text-amber-700 hover:bg-amber-100 hover:text-amber-800"
                  onClick={handleResendVerificationEmail}
                  disabled={isResendingEmail}
                >
                  <MailIcon className="mr-2 h-4 w-4" />
                  {isResendingEmail ? 'Enviando...' : 'Reenviar email de verificación'}
                </Button>
              </AlertDescription>
            </Alert>
          )}

          <form onSubmit={form.handleSubmit(onSubmit)} className="space-y-4">
            <div>
              <Label htmlFor="email">Email</Label>
              <Input
                id="email"
                type="email"
                placeholder="<EMAIL>"
                {...form.register('email')}
                disabled={isLoading}
              />
              {form.formState.errors.email && (
                <p className="text-red-500 text-sm mt-1">{form.formState.errors.email.message}</p>
              )}
            </div>
            <div>
              <Label htmlFor="password">Contraseña</Label>
              <Input
                id="password"
                type="password"
                placeholder="********"
                {...form.register('password')}
                disabled={isLoading}
              />
              {form.formState.errors.password && (
                <p className="text-red-500 text-sm mt-1">{form.formState.errors.password.message}</p>
              )}
            </div>
            <Button type="submit" className="w-full" disabled={isLoading || isResendingEmail}>
              {isLoading ? 'Ingresando...' : 'Ingresar'}
            </Button>
          </form>
          <p className="mt-4 text-center text-sm text-gray-600">
            ¿No tienes cuenta?{' '}
            <Link href="/register" className="font-medium text-indigo-600 hover:text-indigo-500">
              Regístrate
            </Link>
          </p>
        </CardContent>
      </Card>
    </div>
  );
}
