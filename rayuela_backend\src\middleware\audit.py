from fastapi import Request, BackgroundTasks
from starlette.middleware.base import BaseHTTPMiddleware
import time
from src.utils.base_logger import log_info, log_error
from src.utils.audit_tasks import write_audit_log_to_db_task
from src.utils.api_analytics import APIAnalyticsService
from src.core.auth_utils import get_user_and_account_ids
from src.db.session import get_db


class AuditMiddleware(BaseHTTPMiddleware):
    """
    Middleware para auditoría de solicitudes API.

    IMPORTANTE: Este middleware es el MECANISMO PRINCIPAL para registrar todas las solicitudes API.
    NO CREAR mecanismos alternativos de auditoría para solicitudes HTTP.

    Registra información detallada sobre cada solicitud y respuesta, incluyendo:
    - Método HTTP y ruta
    - Código de estado de la respuesta
    - Tiempo de procesamiento
    - Información del cliente (IP, user-agent)
    - ID de cuenta y usuario

    OPTIMIZACIÓN DE COSTOS:
    - TODOS los logs se envían a Cloud Logging (costo-efectivo para grandes volúmenes)
    - SOLO las acciones críticas se almacenan en la base de datos Cloud SQL:
      * POST, PUT, DELETE en recursos clave
      * Accesos a endpoints de administración/mantenimiento
      * Operaciones que modifican el estado del sistema

    Además, utiliza tareas en segundo plano para:
    1. Escribir registros de auditoría críticos en la base de datos
    2. Rastrear analíticas de API para endpoints específicos

    Flujo de auditoría:
    1. Para solicitudes HTTP: AuditMiddleware (automático)
    2. Para eventos del sistema: SystemEventLogger (manual)
       Ejemplo: await SystemEventLogger().log_system_event(...)
    """

    # Definir patrones de endpoints críticos que requieren auditoría en BD
    CRITICAL_ENDPOINTS = {
        # Endpoints de administración y mantenimiento
        "/api/v1/maintenance",
        "/api/v1/system-users", 
        "/api/v1/roles",
        "/api/v1/permissions",
        "/api/v1/accounts",
        
        # Endpoints de recursos clave de negocio
        "/api/v1/products",
        "/api/v1/recommendations", 
        "/api/v1/pipeline",
        "/api/v1/ingestion",
        "/api/v1/billing",
        "/api/v1/api-keys",
        
        # Endpoints de autenticación
        "/api/v1/auth",
    }

    # Métodos que representan cambios de estado significativos
    STATE_CHANGING_METHODS = {"POST", "PUT", "PATCH", "DELETE"}

    def _should_audit_to_database(self, method: str, path: str) -> bool:
        """
        Determina si una solicitud debe ser auditada en la base de datos.
        
        Criterios para auditar en BD:
        1. Métodos que cambian estado (POST, PUT, PATCH, DELETE) en endpoints críticos
        2. Cualquier acceso a endpoints de administración/mantenimiento
        3. Operaciones de autenticación
        
        Args:
            method: Método HTTP de la solicitud
            path: Ruta de la solicitud
            
        Returns:
            bool: True si debe auditarse en la base de datos
        """
        # Siempre auditar endpoints de mantenimiento y administración
        if any(path.startswith(endpoint) for endpoint in [
            "/api/v1/maintenance", 
            "/api/v1/system-users"
        ]):
            return True
        
        # Auditar operaciones de autenticación
        if path.startswith("/api/v1/auth"):
            return True
            
        # Auditar métodos que cambian estado en endpoints críticos
        if method in self.STATE_CHANGING_METHODS:
            return any(path.startswith(endpoint) for endpoint in self.CRITICAL_ENDPOINTS)
        
        return False

    async def dispatch(self, request: Request, call_next):
        start_time = time.time()
        path = request.url.path
        method = request.method

        # Store request information for logging
        request_info = {
            "method": method,
            "path": path,
            "query_params": str(request.query_params),
            "client_ip": request.client.host if request.client else None,
            "user_agent": request.headers.get("user-agent"),
            "request_id": request.headers.get("x-request-id", "-"),
        }

        # Process the request and catch any exceptions
        try:
            response = await call_next(request)
            status_code = response.status_code
            is_error = status_code >= 400
        except Exception as e:
            # Log the exception and re-raise
            log_error(
                f"Exception during request processing: {str(e)}",
                {
                    "request": request_info,
                    "error": str(e),
                },
                exc_info=True,
            )
            raise

        # Calculate processing time
        process_time = time.time() - start_time

        # Add response time header
        response.headers["X-Process-Time"] = f"{process_time:.4f}"

        # Default values
        user_id = "anonymous"
        account_id = None

        # Get account and user IDs
        try:
            db = None
            async with get_db() as db:
                user_id, account_id = await get_user_and_account_ids(request, db)

                # If user_id is None, use 'anonymous'
                if user_id is None:
                    user_id = "anonymous"
        except Exception as e:
            log_error(f"Error getting user/account IDs: {str(e)}", exc_info=True)

        # Prepare audit data
        audit_data = {
            "method": method,
            "url": path,
            "client_ip": request_info["client_ip"],
            "user_agent": request_info["user_agent"],
            "request_id": request_info["request_id"],
            "account_id": account_id,
            "user_id": user_id,
            "status_code": status_code,
            "process_time": process_time,
            "is_error": is_error,
            "timestamp": time.time(),
        }

        # SIEMPRE enviar logs estructurados a Cloud Logging (costo-efectivo)
        if account_id:
            log_info(
                f"{method} {path}",
                {
                    "audit": audit_data,
                    "response_time": process_time,
                    "status_code": status_code,
                    "account_id": account_id,
                    "user_id": user_id,
                    "critical_action": self._should_audit_to_database(method, path),
                },
            )
        else:
            # Log minimal info for non-account requests
            log_info(
                f"{method} {path}",
                {
                    "response_time": process_time,
                    "status_code": status_code,
                    "client_ip": request_info["client_ip"],
                    "critical_action": self._should_audit_to_database(method, path),
                },
            )

        # OPTIMIZACIÓN: Solo escribir a BD las acciones críticas
        if account_id and self._should_audit_to_database(method, path):
            # Create background tasks
            background_tasks = BackgroundTasks()

            # Add task to write audit log to database (SOLO para acciones críticas)
            background_tasks.add_task(write_audit_log_to_db_task, audit_data)
            
            log_info(
                f"Critical action logged to database: {method} {path}",
                {
                    "account_id": account_id,
                    "user_id": user_id,
                    "action_type": "database_audit"
                }
            )

            # Add task to track API analytics if it's an API endpoint
            if path.startswith("/api/"):
                background_tasks.add_task(
                    self._track_api_analytics,
                    account_id=account_id,
                    endpoint=path,
                    method=method,
                    response_time=process_time,
                    status_code=status_code,
                    is_error=is_error,
                )

            # Attach background tasks to the response
            response.background = background_tasks
        elif account_id and path.startswith("/api/"):
            # Para endpoints API no críticos, solo hacer analytics (sin escritura en BD)
            background_tasks = BackgroundTasks()
            background_tasks.add_task(
                self._track_api_analytics,
                account_id=account_id,
                endpoint=path,
                method=method,
                response_time=process_time,
                status_code=status_code,
                is_error=is_error,
            )
            response.background = background_tasks

        return response

    async def _track_api_analytics(
        self,
        account_id: int,
        endpoint: str,
        method: str,
        response_time: float,
        status_code: int,
        is_error: bool,
    ) -> None:
        """Track API analytics in a background task."""
        try:
            # Get a new database session
            async with get_db() as db:
                # Create analytics service and track the API call
                analytics_service = APIAnalyticsService(db, account_id)
                await analytics_service.track_api_call(
                    endpoint=endpoint,
                    method=method,
                    response_time=response_time,
                    status_code=status_code,
                    is_error=is_error,
                )

                # Calculate percentiles periodically (every ~100 calls)
                if response_time > 0 and hash(f"{account_id}:{endpoint}") % 100 == 0:
                    await analytics_service.calculate_percentiles()
        except Exception as e:
            log_error(f"Error tracking API analytics: {str(e)}", exc_info=True)
