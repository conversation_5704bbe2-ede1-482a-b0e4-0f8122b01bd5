import type { Config } from "tailwindcss";

const config: Config = {
    darkMode: ["class"],
    content: [
        './pages/**/*.{ts,tsx}',
        './components/**/*.{ts,tsx}',
        './app/**/*.{ts,tsx}',
        './src/**/*.{ts,tsx}',
    ],
    prefix: "",
    theme: {
        container: {
            center: true,
            padding: "2rem",
            screens: {
                "2xl": "1400px",
            },
        },
        extend: {
            fontFamily: {
                sans: ['var(--font-geist-sans)', 'Inter', 'system-ui', 'sans-serif'],
                mono: ['var(--font-geist-mono)', 'Monaco', 'Consolas', 'monospace'],
            },
            fontSize: {
                // Escala tipográfica semántica
                'display-2xl': ['4.5rem', { lineHeight: '1.1', letterSpacing: '-0.02em' }],
                'display-xl': ['3.75rem', { lineHeight: '1.2', letterSpacing: '-0.02em' }],
                'display-lg': ['3rem', { lineHeight: '1.2', letterSpacing: '-0.01em' }],
                'display-md': ['2.25rem', { lineHeight: '1.3', letterSpacing: '-0.01em' }],
                'heading-xl': ['1.875rem', { lineHeight: '1.4', letterSpacing: '-0.01em' }],
                'heading-lg': ['1.5rem', { lineHeight: '1.4', letterSpacing: '-0.005em' }],
                'heading-md': ['1.25rem', { lineHeight: '1.5', letterSpacing: '-0.005em' }],
                'heading-sm': ['1.125rem', { lineHeight: '1.5' }],
                'body-lg': ['1.125rem', { lineHeight: '1.6' }],
                'body-md': ['1rem', { lineHeight: '1.6' }],
                'body-sm': ['0.875rem', { lineHeight: '1.5' }],
                'caption-lg': ['0.875rem', { lineHeight: '1.4', letterSpacing: '0.01em' }],
                'caption': ['0.75rem', { lineHeight: '1.4', letterSpacing: '0.01em' }],
                'caption-sm': ['0.6875rem', { lineHeight: '1.3', letterSpacing: '0.02em' }],
            },
            letterSpacing: {
                'tightest': '-0.025em',
                'extra-tight': '-0.02em',
                'display': '-0.015em',
            },
            lineHeight: {
                'extra-tight': '1.1',
                'display': '1.2',
                'heading': '1.4',
                'body': '1.6',
                'relaxed': '1.75',
            },
            colors: {
                border: "hsl(var(--border))",
                input: "hsl(var(--input))",
                ring: "hsl(var(--ring))",
                background: "hsl(var(--background))",
                foreground: "hsl(var(--foreground))",
                primary: {
                    DEFAULT: "hsl(var(--primary))",
                    foreground: "hsl(var(--primary-foreground))",
                },
                secondary: {
                    DEFAULT: "hsl(var(--secondary))",
                    foreground: "hsl(var(--secondary-foreground))",
                },
                destructive: {
                    DEFAULT: "hsl(var(--destructive))",
                    foreground: "hsl(var(--destructive-foreground))",
                },
                muted: {
                    DEFAULT: "hsl(var(--muted))",
                    foreground: "hsl(var(--muted-foreground))",
                },
                accent: {
                    DEFAULT: "hsl(var(--accent))",
                    foreground: "hsl(var(--accent-foreground))",
                },
                popover: {
                    DEFAULT: "hsl(var(--popover))",
                    foreground: "hsl(var(--popover-foreground))",
                },
                card: {
                    DEFAULT: "hsl(var(--card))",
                    foreground: "hsl(var(--card-foreground))",
                },
                // Colores semánticos unificados
                success: {
                    DEFAULT: "hsl(var(--success))",
                    foreground: "hsl(var(--success-foreground))",
                    light: "hsl(var(--success-light))",
                },
                warning: {
                    DEFAULT: "hsl(var(--warning))",
                    foreground: "hsl(var(--warning-foreground))",
                    light: "hsl(var(--warning-light))",
                },
                info: {
                    DEFAULT: "hsl(var(--info))",
                    foreground: "hsl(var(--info-foreground))",
                    light: "hsl(var(--info-light))",
                },
                // Gradientes de marketing unificados
                'marketing-gradient': {
                    start: "hsl(var(--marketing-gradient-start))",
                    end: "hsl(var(--marketing-gradient-end))",
                    accent: "hsl(var(--marketing-gradient-accent))",
                },
            },
            borderRadius: {
                lg: "var(--radius)",
                md: "calc(var(--radius) - 2px)",
                sm: "calc(var(--radius) - 4px)",
            },
            keyframes: {
                "accordion-down": {
                    from: { height: "0" },
                    to: { height: "var(--radix-accordion-content-height)" },
                },
                "accordion-up": {
                    from: { height: "var(--radix-accordion-content-height)" },
                    to: { height: "0" },
                },
                // Enhanced animations for creative touch
                "fade-up": {
                    from: {
                        opacity: "0",
                        transform: "translateY(10px)",
                    },
                    to: {
                        opacity: "1",
                        transform: "translateY(0)",
                    },
                },
                "fade-in": {
                    from: { opacity: "0" },
                    to: { opacity: "1" },
                },
                "scale-in": {
                    from: {
                        opacity: "0",
                        transform: "scale(0.9)",
                    },
                    to: {
                        opacity: "1",
                        transform: "scale(1)",
                    },
                },
                "shimmer": {
                    from: {
                        backgroundPosition: "0% 0%",
                    },
                    to: {
                        backgroundPosition: "-200% 0%",
                    },
                },
            },
            animation: {
                "accordion-down": "accordion-down 0.2s ease-out",
                "accordion-up": "accordion-up 0.2s ease-out",
                // Enhanced animations for creative touch
                "fade-up": "fade-up 0.5s ease-out",
                "fade-in": "fade-in 0.3s ease-out",
                "scale-in": "scale-in 0.2s ease-out",
                "shimmer": "shimmer 2s linear infinite",
            },
            // Enhanced background gradients for creative touch
            backgroundImage: {
                'gradient-radial': 'radial-gradient(var(--tw-gradient-stops))',
                'gradient-conic': 'conic-gradient(from 180deg at 50% 50%, var(--tw-gradient-stops))',
                'subtle-gradient': 'linear-gradient(135deg, var(--tw-gradient-stops))',
                'shimmer': 'linear-gradient(110deg, oklch(0 0 0 / 0) 0%, oklch(0 0 0 / 0) 40%, oklch(1 0 0 / 0.15) 50%, oklch(0 0 0 / 0) 60%, oklch(0 0 0 / 0) 100%)',
            },
            // Enhanced shadow for creative depth
            boxShadow: {
                'soft': '0 2px 15px -3px rgba(0, 0, 0, 0.07), 0 10px 20px -2px rgba(0, 0, 0, 0.04)',
                'medium': '0 4px 25px -2px rgba(0, 0, 0, 0.08), 0 10px 30px -2px rgba(0, 0, 0, 0.06)',
                'glow': '0 0 20px -2px rgba(0, 0, 0, 0.1)',
                'glow-primary': '0 0 20px -2px hsl(var(--primary) / 0.3)',
                'glow-info': '0 0 20px -2px hsl(var(--info) / 0.3)',
            },
        },
    },
    plugins: [require("tailwindcss-animate")],
};

export default config; 