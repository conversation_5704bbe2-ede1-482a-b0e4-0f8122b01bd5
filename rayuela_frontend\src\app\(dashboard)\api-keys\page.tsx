// src/app/(dashboard)/api-keys/page.tsx
"use client";

import { useState } from 'react';
import { <PERSON><PERSON> } from "@/components/ui/button";
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card";
import { Skeleton } from "@/components/ui/skeleton";
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from "@/components/ui/table";
import { Badge } from "@/components/ui/badge";
import { Alert, AlertDescription, AlertTitle } from "@/components/ui/alert";
import { useAuth } from '@/lib/auth';
import { useApiKeys } from '@/lib/useApiKeys';
import { ApiKey } from '@/lib/api';
import { toast } from "sonner";
import { handleApiError } from "@/lib/error-handler";
import { format } from 'date-fns';
import { es } from 'date-fns/locale';
import { AlertCircle, Plus, Edit, Trash2, Key, Co<PERSON> } from 'lucide-react';
import Link from 'next/link';
import InitialApiKeyModal from '@/components/auth/InitialApiKeyModal';
import {
  AlertDialog,
  AlertDialogAction,
  AlertDialogCancel,
  AlertDialogContent,
  AlertDialogDescription,
  AlertDialogFooter,
  AlertDialogHeader,
  AlertDialogTitle,
  AlertDialogTrigger,
} from "@/components/ui/alert-dialog";
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
  DialogTrigger,
} from "@/components/ui/dialog";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";

export default function ApiKeysPage() {
  const { token, user, isLoading: isAuthLoading } = useAuth();
  const [showNewKeyModal, setShowNewKeyModal] = useState(false);
  const [newApiKey, setNewApiKey] = useState<string | null>(null);
  const [showCreateDialog, setShowCreateDialog] = useState(false);
  const [showEditDialog, setShowEditDialog] = useState(false);
  const [editingApiKey, setEditingApiKey] = useState<ApiKey | null>(null);
  const [newKeyName, setNewKeyName] = useState('');
  const [editKeyName, setEditKeyName] = useState('');

  // Using the consolidated useApiKeys hook
  const {
    data: apiKeysData,
    error: apiKeysError,
    isLoading: isApiKeysLoading,
    createApiKey,
    updateApiKey,
    revokeApiKey,
    isCreating,
    isUpdating,
    isRevoking,
    getFormattedApiKey
  } = useApiKeys({
    revalidateOnFocus: true,
    refreshInterval: 30000, // Actualizar cada 30 segundos
    dedupingInterval: 5000, // Evitar peticiones duplicadas en 5 segundos
    errorRetryCount: 3
  });

  const handleCreateApiKey = async () => {
    try {
      const result = await createApiKey({
        name: newKeyName.trim() || `API Key ${new Date().toLocaleDateString('es-ES')}`
      });
      if (result?.api_key) {
        setNewApiKey(result.api_key);
        setShowNewKeyModal(true);
        setShowCreateDialog(false);
        setNewKeyName('');
        toast.success("API Key creada con éxito");
      }
    } catch (error: unknown) {
      handleApiError(error, "Error al crear la API Key");
    }
  };

  const handleEditApiKey = async () => {
    if (!editingApiKey) return;

    try {
      const result = await updateApiKey(editingApiKey.id, {
        name: editKeyName.trim()
      });
      if (result) {
        setShowEditDialog(false);
        setEditingApiKey(null);
        setEditKeyName('');
        toast.success("API Key actualizada con éxito");
      }
    } catch (error: unknown) {
      handleApiError(error, "Error al actualizar la API Key");
    }
  };

  const handleRevokeApiKey = async (apiKeyId: number, apiKeyName?: string) => {
    try {
      await revokeApiKey(apiKeyId);
      toast.success(`API Key ${apiKeyName ? `"${apiKeyName}"` : ''} revocada con éxito`);
    } catch (error: unknown) {
      handleApiError(error, "Error al revocar la API Key");
    }
  };

  const handleCopyApiKey = (apiKey: ApiKey) => {
    const formattedKey = getFormattedApiKey(apiKey);
    if (formattedKey) {
      navigator.clipboard.writeText(formattedKey);
      toast.success("API Key copiada al portapapeles");
    }
  };

  const openEditDialog = (apiKey: ApiKey) => {
    setEditingApiKey(apiKey);
    setEditKeyName(apiKey.name || '');
    setShowEditDialog(true);
  };

  const handleModalClose = () => {
    setShowNewKeyModal(false);
  };

  // Estado de carga combinado
  const isLoading = isAuthLoading || isApiKeysLoading;

  // Estado de Error (Auth o API Key)
  if (apiKeysError) {
    return (
      <div className="container mx-auto py-8">
        <Alert variant="destructive">
          <AlertCircle className="h-4 w-4" />
          <AlertTitle>Error al cargar las API Keys</AlertTitle>
          <AlertDescription>
            {apiKeysError.message}
            <br />
            Intenta refrescar la página o contacta a soporte.
          </AlertDescription>
        </Alert>
      </div>
    );
  }

  // Estado: No autenticado
  if (!token || !user) {
    return (
      <div className="container mx-auto py-8">
        <Alert variant="warning">
          <AlertCircle className="h-4 w-4" />
          <AlertTitle>Sesión no verificada</AlertTitle>
          <AlertDescription>
            No se pudo verificar tu sesión. Por favor, intenta <Link href="/login" className="underline">iniciar sesión</Link> de nuevo.
          </AlertDescription>
        </Alert>
      </div>
    );
  }

  // Mostrar loading mientras se cargan los datos
  if (isLoading) {
    return (
      <div className="container mx-auto py-8 space-y-8">
        {/* Header Section with subtle background grouping */}
        <div className="bg-card/30 border border-border/50 rounded-lg p-6">
          <div className="flex items-center justify-between">
            <div>
              <h1 className="text-3xl font-bold">API Keys</h1>
              <p className="text-muted-foreground mt-2">
                Gestiona múltiples claves de API para acceder a los servicios de Rayuela
              </p>
            </div>
            <Skeleton className="h-10 w-32" />
          </div>

          <Card>
            <CardHeader>
              <CardTitle className="flex items-center gap-2">
                <Skeleton className="h-6 w-6" />
                <Skeleton className="h-6 w-48" />
              </CardTitle>
              <CardDescription>
                <Skeleton className="h-4 w-64" />
              </CardDescription>
            </CardHeader>
            <CardContent className="space-y-4">
              <div className="space-y-2">
                <Skeleton className="h-10 w-full" />
                <Skeleton className="h-10 w-full" />
                <Skeleton className="h-10 w-full" />
              </div>
            </CardContent>
          </Card>
        </div>
      </div>
    );
  }

  return (
    <div className="container mx-auto py-8 space-y-8">
      {/* Header Section with subtle background */}
      <div className="bg-card/50 border border-border/50 rounded-lg p-6">
        <div className="flex items-center justify-between">
          <div>
            <h1 className="text-3xl font-bold">API Keys</h1>
            <p className="text-muted-foreground mt-2">
              Gestiona múltiples claves de API para acceder a los servicios de Rayuela
            </p>
          </div>
          <Dialog open={showCreateDialog} onOpenChange={setShowCreateDialog}>
            <DialogTrigger asChild>
              <Button className="flex items-center gap-2">
                <Plus className="h-4 w-4" />
                Nueva API Key
              </Button>
            </DialogTrigger>
            <DialogContent>
              <DialogHeader>
                <DialogTitle>Crear Nueva API Key</DialogTitle>
                <DialogDescription>
                  Crea una nueva API Key para acceder a los servicios de Rayuela. Puedes asignarle un nombre descriptivo.
                </DialogDescription>
              </DialogHeader>
              <div className="space-y-4">
                <div>
                  <Label htmlFor="newKeyName">Nombre de la API Key (opcional)</Label>
                  <Input
                    id="newKeyName"
                    placeholder="ej. Producción, Desarrollo, Equipo Frontend..."
                    value={newKeyName}
                    onChange={(e) => setNewKeyName(e.target.value)}
                  />
                </div>
              </div>
              <DialogFooter>
                <Button variant="outline" onClick={() => setShowCreateDialog(false)}>
                  Cancelar
                </Button>
                <Button onClick={handleCreateApiKey} disabled={isCreating}>
                  {isCreating ? 'Creando...' : 'Crear API Key'}
                </Button>
              </DialogFooter>
            </DialogContent>
          </Dialog>
        </div>
      </div>

      {/* Divider with subtle line */}
      <div className="border-t border-border/50"></div>

      {/* Main Content Section */}
      <Card className="shadow-sm border-border/50">
        <CardHeader className="border-b border-border/20 bg-muted/20">
          <CardTitle className="flex items-center gap-2">
            <Key className="h-5 w-5 text-primary" />
            API Keys Activas
          </CardTitle>
          <CardDescription>
            Claves de API activas para tu cuenta. Puedes crear múltiples claves para diferentes proyectos.
          </CardDescription>
        </CardHeader>
        
        <CardContent className="p-0">
          {/* Enhanced Table Section */}
          <div className="overflow-hidden">
            <Table>
              <TableHeader className="bg-muted/10">
                <TableRow className="border-b border-border/30">
                  <TableHead className="font-semibold">Nombre</TableHead>
                  <TableHead className="font-semibold">Clave</TableHead>
                  <TableHead className="font-semibold">Estado</TableHead>
                  <TableHead className="font-semibold">Creación</TableHead>
                  <TableHead className="font-semibold">Último uso</TableHead>
                  <TableHead className="text-right font-semibold">Acciones</TableHead>
                </TableRow>
              </TableHeader>
              <TableBody>
                {apiKeysData?.api_keys && apiKeysData.api_keys.length > 0 ? (
                  apiKeysData.api_keys.map((apiKey: ApiKey, index: number) => (
                    <TableRow 
                      key={apiKey.id} 
                      className={`
                        border-b border-border/20 
                        hover:bg-muted/30 
                        transition-colors
                        ${index % 2 === 0 ? 'bg-background' : 'bg-muted/5'}
                      `}
                    >
                      <TableCell className="font-medium py-4">
                        <div className="flex items-center gap-2">
                          <div className="w-2 h-2 bg-success rounded-full"></div>
                          {apiKey.name || `API Key ${apiKey.id}`}
                        </div>
                      </TableCell>
                      <TableCell className="py-4">
                        <div className="flex items-center gap-2">
                          <code className="text-sm bg-muted/50 px-2 py-1 rounded border">
                            {getFormattedApiKey(apiKey)?.slice(0, 16)}...
                          </code>
                          <Button
                            variant="ghost"
                            size="sm"
                            onClick={() => handleCopyApiKey(apiKey)}
                            className="h-6 w-6 p-0 hover:bg-muted/50"
                          >
                            <Copy className="h-3 w-3" />
                          </Button>
                        </div>
                      </TableCell>
                      <TableCell className="py-4">
                        <Badge variant="success">
                          Activa
                        </Badge>
                      </TableCell>
                      <TableCell className="text-muted-foreground py-4">
                        {apiKey.created_at ? format(new Date(apiKey.created_at), "d 'de' MMMM, yyyy", { locale: es }) : 'N/A'}
                      </TableCell>
                      <TableCell className="text-muted-foreground py-4">
                        {apiKey.last_used
                          ? format(new Date(apiKey.last_used), "d 'de' MMMM, yyyy", { locale: es })
                          : 'Nunca'
                        }
                      </TableCell>
                      <TableCell className="text-right py-4">
                        <div className="flex items-center justify-end gap-1">
                          <Button
                            variant="ghost"
                            size="sm"
                            onClick={() => openEditDialog(apiKey)}
                            className="h-8 w-8 p-0 hover:bg-muted/50"
                          >
                            <Edit className="h-4 w-4" />
                          </Button>
                          <AlertDialog>
                            <AlertDialogTrigger asChild>
                              <Button
                                variant="ghost"
                                size="sm"
                                className="h-8 w-8 p-0 text-destructive hover:text-destructive hover:bg-destructive/10"
                              >
                                <Trash2 className="h-4 w-4" />
                              </Button>
                            </AlertDialogTrigger>
                            <AlertDialogContent>
                              <AlertDialogHeader>
                                <AlertDialogTitle>¿Estás seguro?</AlertDialogTitle>
                                <AlertDialogDescription>
                                  Esta acción no se puede deshacer. Esto revocará permanentemente la API Key
                                  "{apiKey.name || `API Key ${apiKey.id}`}" y cualquier aplicación que la use dejará de funcionar.
                                </AlertDialogDescription>
                              </AlertDialogHeader>
                              <AlertDialogFooter>
                                <AlertDialogCancel>Cancelar</AlertDialogCancel>
                                <AlertDialogAction
                                  onClick={() => handleRevokeApiKey(apiKey.id, apiKey.name || undefined)}
                                  className="bg-destructive text-destructive-foreground hover:bg-destructive/90"
                                  disabled={isRevoking}
                                >
                                  {isRevoking ? 'Revocando...' : 'Revocar API Key'}
                                </AlertDialogAction>
                              </AlertDialogFooter>
                            </AlertDialogContent>
                          </AlertDialog>
                        </div>
                      </TableCell>
                    </TableRow>
                  ))
                ) : (
                  <TableRow>
                    <TableCell colSpan={6} className="text-center py-8">
                      <div className="flex flex-col items-center gap-2 text-muted-foreground">
                        <AlertCircle className="h-8 w-8" />
                        <p>No tienes API Keys creadas aún</p>
                        <p className="text-sm">Crea tu primera API Key para comenzar a usar la API</p>
                      </div>
                    </TableCell>
                  </TableRow>
                )}
              </TableBody>
            </Table>
          </div>
        </CardContent>
      </Card>

      {/* Información importante sobre las API Keys - Mejorado con Alert component */}
      <Alert variant="warning">
        <AlertCircle className="h-4 w-4" />
        <AlertTitle>Información importante sobre tus API Keys</AlertTitle>
        <AlertDescription>
          <div className="space-y-3 text-sm mt-2">
              <p>
                Por razones de seguridad, las API Keys completas <strong>solo se muestran una vez</strong> al momento de crearlas.
                Es crucial que las copies y almacenes en un lugar seguro inmediatamente.
              </p>
              <p className="font-medium">
                Ventajas del sistema multi-API Key:
              </p>
              <ul className="list-disc list-inside space-y-1 pl-2">
                <li><strong>Seguridad mejorada:</strong> Revoca claves específicas sin afectar otras integraciones</li>
                <li><strong>Organización:</strong> Asigna nombres descriptivos para diferentes entornos o equipos</li>
                <li><strong>Flexibilidad:</strong> Crea tantas claves como necesites para diferentes propósitos</li>
                <li><strong>Trazabilidad:</strong> Ve cuándo fue la última vez que se usó cada clave</li>
              </ul>
            </div>
        </AlertDescription>
      </Alert>

      {/* Modal para mostrar la nueva API Key */}
      {showNewKeyModal && newApiKey && (
        <InitialApiKeyModal
          apiKey={newApiKey}
          onClose={handleModalClose}
        />
      )}

      {/* Dialog para editar API Key */}
      <Dialog open={showEditDialog} onOpenChange={setShowEditDialog}>
        <DialogContent>
          <DialogHeader>
            <DialogTitle>Editar API Key</DialogTitle>
            <DialogDescription>
              Actualiza el nombre descriptivo de tu API Key. La clave en sí no se puede modificar.
            </DialogDescription>
          </DialogHeader>
          <div className="space-y-4">
            <div>
              <Label htmlFor="editKeyName">Nombre de la API Key</Label>
              <Input
                id="editKeyName"
                placeholder="ej. Producción, Desarrollo, Equipo Frontend..."
                value={editKeyName}
                onChange={(e) => setEditKeyName(e.target.value)}
              />
            </div>
          </div>
          <DialogFooter>
            <Button variant="outline" onClick={() => setShowEditDialog(false)}>
              Cancelar
            </Button>
            <Button onClick={handleEditApiKey} disabled={isUpdating}>
              {isUpdating ? 'Actualizando...' : 'Actualizar'}
            </Button>
          </DialogFooter>
        </DialogContent>
      </Dialog>
    </div>
  );
}
