#!/bin/bash

# =============================================================================
# Script: Setup IAM Service Accounts with Least Privilege
# Description: Creates dedicated service accounts for Cloud Build and Cloud Run
#              with minimal required permissions to mitigate security risks
# Usage: ./setup-iam-service-accounts.sh PROJECT_ID
# Security Reference: OWASP Top 10 A05:2021 – Security Misconfiguration
# =============================================================================

set -euo pipefail

# Check if project ID is provided
if [ $# -ne 1 ]; then
    echo "❌ Error: Project ID is required"
    echo "Usage: $0 PROJECT_ID"
    exit 1
fi

PROJECT_ID="$1"
REGION="us-central1"

echo "🚀 Setting up IAM Service Accounts with Least Privilege for Project: $PROJECT_ID"
echo "🔒 Security Focus: Implementing principle of least privilege for Cloud Build and Cloud Run"

# =============================================================================
# 1. CREATE CLOUD BUILD SERVICE ACCOUNT
# =============================================================================
echo ""
echo "📋 Creating Cloud Build Service Account..."

CB_SA_NAME="rayuela-cloudbuild-sa"
CB_SA_EMAIL="$CB_SA_NAME@$PROJECT_ID.iam.gserviceaccount.com"

# Create Cloud Build Service Account
if gcloud iam service-accounts describe "$CB_SA_EMAIL" --project="$PROJECT_ID" >/dev/null 2>&1; then
    echo "✅ Cloud Build service account already exists: $CB_SA_EMAIL"
else
    gcloud iam service-accounts create "$CB_SA_NAME" \
        --project="$PROJECT_ID" \
        --display-name="Rayuela Cloud Build Service Account" \
        --description="Minimal permissions service account for Cloud Build operations"
    echo "✅ Created Cloud Build service account: $CB_SA_EMAIL"
fi

# Cloud Build Required Permissions (Complete Set for Deployment)
echo "🔑 Assigning required permissions to Cloud Build Service Account..."

CB_ROLES=(
    # Core deployment permissions
    "roles/run.admin"                        # Full Cloud Run management (including IAM policies)
    "roles/secretmanager.secretAccessor"     # Access secrets
    "roles/cloudsql.client"                  # Connect to Cloud SQL
    "roles/storage.objectAdmin"              # Upload Docker images and SQL files
    "roles/storage.objectViewer"             # Read from Cloud Storage buckets (for SQL imports)
    "roles/artifactregistry.writer"          # Push to Artifact Registry
    "roles/compute.networkViewer"            # Access VPC connector info
    "roles/iam.serviceAccountUser"           # Act as other service accounts during deployment
    
    # Additional permissions needed for deployment (based on actual failures)
    "roles/logging.logWriter"                # Write logs to Cloud Logging
    "roles/artifactregistry.reader"          # Read images from registry during deployment
    "roles/cloudsql.editor"                  # Full Cloud SQL Proxy operations
    "roles/serviceusage.serviceUsageConsumer" # Use GCP services
    "roles/monitoring.metricWriter"          # Write metrics (for health checks)
)

for role in "${CB_ROLES[@]}"; do
    gcloud projects add-iam-policy-binding "$PROJECT_ID" \
        --member="serviceAccount:$CB_SA_EMAIL" \
        --role="$role" \
        --condition=None
    echo "  ✓ Assigned: $role"
done

# =============================================================================
# 2. CREATE CLOUD RUN SERVICE ACCOUNTS
# =============================================================================
echo ""
echo "📋 Creating Cloud Run Service Accounts..."

# Backend Service Account
BACKEND_SA_NAME="rayuela-backend-sa"
BACKEND_SA_EMAIL="$BACKEND_SA_NAME@$PROJECT_ID.iam.gserviceaccount.com"

if gcloud iam service-accounts describe "$BACKEND_SA_EMAIL" --project="$PROJECT_ID" >/dev/null 2>&1; then
    echo "✅ Backend service account already exists: $BACKEND_SA_EMAIL"
else
    gcloud iam service-accounts create "$BACKEND_SA_NAME" \
        --project="$PROJECT_ID" \
        --display-name="Rayuela Backend Service Account" \
        --description="Minimal permissions service account for backend operations"
    echo "✅ Created Backend service account: $BACKEND_SA_EMAIL"
fi

# Frontend Service Account
FRONTEND_SA_NAME="rayuela-frontend-sa"
FRONTEND_SA_EMAIL="$FRONTEND_SA_NAME@$PROJECT_ID.iam.gserviceaccount.com"

if gcloud iam service-accounts describe "$FRONTEND_SA_EMAIL" --project="$PROJECT_ID" >/dev/null 2>&1; then
    echo "✅ Frontend service account already exists: $FRONTEND_SA_EMAIL"
else
    gcloud iam service-accounts create "$FRONTEND_SA_NAME" \
        --project="$PROJECT_ID" \
        --display-name="Rayuela Frontend Service Account" \
        --description="Minimal permissions service account for frontend operations"
    echo "✅ Created Frontend service account: $FRONTEND_SA_EMAIL"
fi

# Worker Service Account
WORKER_SA_NAME="rayuela-worker-sa"
WORKER_SA_EMAIL="$WORKER_SA_NAME@$PROJECT_ID.iam.gserviceaccount.com"

if gcloud iam service-accounts describe "$WORKER_SA_EMAIL" --project="$PROJECT_ID" >/dev/null 2>&1; then
    echo "✅ Worker service account already exists: $WORKER_SA_EMAIL"
else
    gcloud iam service-accounts create "$WORKER_SA_NAME" \
        --project="$PROJECT_ID" \
        --display-name="Rayuela Worker Service Account" \
        --description="Minimal permissions service account for worker/beat operations"
    echo "✅ Created Worker service account: $WORKER_SA_EMAIL"
fi

# =============================================================================
# 3. ASSIGN MINIMAL PERMISSIONS TO BACKEND SERVICE ACCOUNT
# =============================================================================
echo ""
echo "🔑 Assigning minimal permissions to Backend Service Account..."

BACKEND_ROLES=(
    "roles/secretmanager.secretAccessor"     # Access application secrets
    "roles/cloudsql.client"                  # Connect to Cloud SQL database
    "roles/redis.editor"                     # Redis operations (cache/sessions)
    "roles/storage.objectViewer"             # Read from GCS buckets (if needed)
)

for role in "${BACKEND_ROLES[@]}"; do
    gcloud projects add-iam-policy-binding "$PROJECT_ID" \
        --member="serviceAccount:$BACKEND_SA_EMAIL" \
        --role="$role" \
        --condition=None
    echo "  ✓ Assigned: $role"
done

# =============================================================================
# 4. ASSIGN MINIMAL PERMISSIONS TO FRONTEND SERVICE ACCOUNT
# =============================================================================
echo ""
echo "🔑 Assigning minimal permissions to Frontend Service Account..."

FRONTEND_ROLES=(
    "roles/secretmanager.secretAccessor"     # Access frontend secrets (if any)
)

for role in "${FRONTEND_ROLES[@]}"; do
    gcloud projects add-iam-policy-binding "$PROJECT_ID" \
        --member="serviceAccount:$FRONTEND_SA_EMAIL" \
        --role="$role" \
        --condition=None
    echo "  ✓ Assigned: $role"
done

# =============================================================================
# 5. ASSIGN MINIMAL PERMISSIONS TO WORKER SERVICE ACCOUNT
# =============================================================================
echo ""
echo "🔑 Assigning minimal permissions to Worker Service Account..."

WORKER_ROLES=(
    "roles/secretmanager.secretAccessor"     # Access application secrets
    "roles/cloudsql.client"                  # Connect to Cloud SQL database
    "roles/redis.editor"                     # Redis operations for task queue
    "roles/storage.objectAdmin"              # GCS operations for batch/archival data
)

for role in "${WORKER_ROLES[@]}"; do
    gcloud projects add-iam-policy-binding "$PROJECT_ID" \
        --member="serviceAccount:$WORKER_SA_EMAIL" \
        --role="$role" \
        --condition=None
    echo "  ✓ Assigned: $role"
done

# =============================================================================
# 6. ARTIFACT REGISTRY SETUP
# =============================================================================
echo ""
echo "🏗️ Setting up Artifact Registry..."

# Create Artifact Registry repository if it doesn't exist
REPO_EXISTS=$(gcloud artifacts repositories describe rayuela-repo \
    --location=us-central1 \
    --project=$PROJECT_ID \
    --format="value(name)" 2>/dev/null || echo "")

if [ -z "$REPO_EXISTS" ]; then
    echo "📦 Creating Artifact Registry repository..."
    gcloud artifacts repositories create rayuela-repo \
        --repository-format=docker \
        --location=us-central1 \
        --description="Rayuela application Docker images" \
        --project=$PROJECT_ID
    echo "✅ Artifact Registry repository created"
else
    echo "✅ Artifact Registry repository already exists"
fi

# =============================================================================
# 7. SECURITY VALIDATIONS
# =============================================================================
echo ""
echo "🔒 Performing Security Validations..."

# Check that service accounts don't have overly broad permissions
DANGEROUS_ROLES=("roles/editor" "roles/owner" "roles/iam.serviceAccountAdmin" "roles/resourcemanager.projectIamAdmin")

echo "🚨 Checking for dangerous role assignments..."
for sa_email in "$CB_SA_EMAIL" "$BACKEND_SA_EMAIL" "$FRONTEND_SA_EMAIL" "$WORKER_SA_EMAIL"; do
    echo "  Checking: $sa_email"
    for dangerous_role in "${DANGEROUS_ROLES[@]}"; do
        if gcloud projects get-iam-policy "$PROJECT_ID" \
           --flatten="bindings[].members" \
           --format="table(bindings.role)" \
           --filter="bindings.members:serviceAccount:$sa_email AND bindings.role:$dangerous_role" | grep -q "$dangerous_role"; then
            echo "    ⚠️  WARNING: Found dangerous role assignment: $dangerous_role"
        fi
    done
done

# =============================================================================
# 8. GENERATE SUMMARY REPORT
# =============================================================================
echo ""
echo "📊 SECURITY IMPLEMENTATION SUMMARY"
echo "=================================="
echo ""
echo "✅ Created Service Accounts with Least Privilege:"
echo "   • Cloud Build SA: $CB_SA_EMAIL"
echo "   • Backend SA: $BACKEND_SA_EMAIL"
echo "   • Frontend SA: $FRONTEND_SA_EMAIL"
echo "   • Worker SA: $WORKER_SA_EMAIL"
echo ""
echo "🔒 Security Improvements Implemented:"
echo "   • Eliminated use of default Compute Engine service accounts"
echo "   • Applied principle of least privilege"
echo "   • Removed Editor/Owner role dependencies"
echo "   • Isolated permissions per service function"
echo ""
echo "📋 Next Steps:"
echo "   1. Update Cloud Build configuration to use: $CB_SA_EMAIL"
echo "   2. Update Cloud Run deployments to use respective service accounts"
echo "   3. Run security validation: ./validate-iam-security.sh $PROJECT_ID"
echo "   4. Monitor IAM permissions regularly"
echo ""
echo "🔗 References:"
echo "   • OWASP Top 10 A05:2021 – Security Misconfiguration"
echo "   • GCP Security Best Practices"
echo "   • Principle of Least Privilege"
echo ""
echo "✅ IAM Service Account setup completed successfully!" 