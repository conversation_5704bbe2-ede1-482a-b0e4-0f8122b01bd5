#!/usr/bin/env python3
"""
Script de verificación de correcciones de seguridad para TenantMiddleware.
No requiere dependencias externas, solo utiliza la biblioteca estándar de Python.
"""

import os
import re
import sys
from pathlib import Path
from typing import List, Dict, Tuple


class SecurityFixVerifier:
    """Verificador de correcciones de seguridad implementadas."""
    
    def __init__(self, base_path: str):
        self.base_path = Path(base_path)
        self.errors = []
        self.warnings = []
        self.success = []
    
    def verify_all(self) -> bool:
        """Ejecuta todas las verificaciones de seguridad."""
        print("🔍 Iniciando verificación de correcciones de seguridad...")
        print("=" * 60)
        
        # Verificaciones de archivos modificados
        self.verify_auth_service_imports()
        self.verify_auth_endpoints_imports()
        self.verify_security_logging()
        self.verify_tenant_context_management()
        self.verify_manual_filters()
        self.verify_middleware_logging()
        self.verify_documentation()
        
        # Mostrar resultados
        self.print_results()
        
        return len(self.errors) == 0
    
    def verify_auth_service_imports(self):
        """Verificar imports de contexto tenant en AuthService."""
        file_path = self.base_path / "src/services/auth_service.py"
        
        if not file_path.exists():
            self.errors.append(f"❌ Archivo no encontrado: {file_path}")
            return
        
        content = file_path.read_text()
        
        # Verificar import de tenant_context
        if "from src.core.tenant_context import get_current_tenant_id, set_current_tenant_id" in content:
            self.success.append("✅ AuthService: Import de tenant_context presente")
        else:
            self.errors.append("❌ AuthService: Falta import de tenant_context")
    
    def verify_auth_endpoints_imports(self):
        """Verificar imports de contexto tenant en endpoints de auth."""
        file_path = self.base_path / "src/api/v1/endpoints/auth.py"
        
        if not file_path.exists():
            self.errors.append(f"❌ Archivo no encontrado: {file_path}")
            return
        
        content = file_path.read_text()
        
        if "from src.core.tenant_context import get_current_tenant_id, set_current_tenant_id" in content:
            self.success.append("✅ Auth Endpoints: Import de tenant_context presente")
        else:
            self.errors.append("❌ Auth Endpoints: Falta import de tenant_context")
    
    def verify_security_logging(self):
        """Verificar que el logging de seguridad está implementado."""
        file_path = self.base_path / "src/services/auth_service.py"
        
        if not file_path.exists():
            return
        
        content = file_path.read_text()
        
        # Verificar logs de contexto tenant
        security_logs = [
            "tenant_context.*current_tenant",
            "email.*\\[:3\\]\\*\\*\\*",  # Sanitización de emails
            "SEGURIDAD.*INTENCIONADA",
            "Login attempt.*tenant_context",
            "Registration.*tenant_context"
        ]
        
        found_logs = 0
        for log_pattern in security_logs:
            if re.search(log_pattern, content):
                found_logs += 1
        
        if found_logs >= 3:
            self.success.append(f"✅ AuthService: Logging de seguridad implementado ({found_logs}/{len(security_logs)} patrones encontrados)")
        else:
            self.warnings.append(f"⚠️ AuthService: Logging de seguridad incompleto ({found_logs}/{len(security_logs)} patrones encontrados)")
    
    def verify_tenant_context_management(self):
        """Verificar que el contexto de tenant se maneja correctamente."""
        file_path = self.base_path / "src/services/auth_service.py"
        
        if not file_path.exists():
            return
        
        content = file_path.read_text()
        
        # Verificar establecimiento de contexto
        context_set_patterns = [
            "set_current_tenant_id\\(user\\.account_id\\)",
            "set_current_tenant_id\\(account\\.account_id\\)",
            "set_current_tenant_id\\(None\\)"  # Limpieza en errores
        ]
        
        found_patterns = 0
        for pattern in context_set_patterns:
            if re.search(pattern, content):
                found_patterns += 1
        
        if found_patterns >= 2:
            self.success.append("✅ AuthService: Contexto de tenant se establece correctamente")
        else:
            self.errors.append("❌ AuthService: Falta establecimiento de contexto de tenant")
    
    def verify_manual_filters(self):
        """Verificar que los filtros manuales por tenant están documentados."""
        file_path = self.base_path / "src/services/auth_service.py"
        
        if not file_path.exists():
            return
        
        content = file_path.read_text()
        
        # Verificar filtros con documentación
        if re.search(r"CRÍTICO.*Filtro por tenant", content):
            self.success.append("✅ AuthService: Filtros manuales documentados como críticos")
        else:
            self.warnings.append("⚠️ AuthService: Filtros manuales no están claramente documentados")
        
        # Verificar que las actualizaciones incluyen account_id
        if "SystemUser.account_id == user.account_id" in content:
            self.success.append("✅ AuthService: Actualizaciones incluyen filtro por account_id")
        else:
            self.errors.append("❌ AuthService: Actualizaciones no filtran por account_id")
    
    def verify_middleware_logging(self):
        """Verificar logging en TenantMiddleware."""
        file_path = self.base_path / "src/middleware/tenant.py"
        
        if not file_path.exists():
            self.warnings.append("⚠️ TenantMiddleware: Archivo no encontrado")
            return
        
        content = file_path.read_text()
        
        if "TENANT_MIDDLEWARE_EXCLUDED" in content:
            self.success.append("✅ TenantMiddleware: Logging de rutas excluidas implementado")
        else:
            self.warnings.append("⚠️ TenantMiddleware: Falta logging de rutas excluidas")
        
        if "Auth endpoints manejan su propio contexto" in content:
            self.success.append("✅ TenantMiddleware: Documentación de exclusiones mejorada")
        else:
            self.warnings.append("⚠️ TenantMiddleware: Falta documentación de exclusiones")
    
    def verify_documentation(self):
        """Verificar que la documentación de seguridad está presente."""
        doc_files = [
            "docs/security/TENANT_MIDDLEWARE_SECURITY_FIXES.md",
            "tests/security/test_tenant_middleware_excluded_routes.py"
        ]
        
        for doc_file in doc_files:
            file_path = self.base_path / doc_file
            if file_path.exists():
                self.success.append(f"✅ Documentación: {doc_file} presente")
            else:
                self.warnings.append(f"⚠️ Documentación: {doc_file} no encontrado")
    
    def print_results(self):
        """Imprimir resultados de la verificación."""
        print("\n" + "=" * 60)
        print("📊 RESULTADOS DE VERIFICACIÓN")
        print("=" * 60)
        
        if self.success:
            print("\n✅ ÉXITOS:")
            for success in self.success:
                print(f"  {success}")
        
        if self.warnings:
            print("\n⚠️ ADVERTENCIAS:")
            for warning in self.warnings:
                print(f"  {warning}")
        
        if self.errors:
            print("\n❌ ERRORES:")
            for error in self.errors:
                print(f"  {error}")
        
        print("\n" + "=" * 60)
        total_checks = len(self.success) + len(self.warnings) + len(self.errors)
        success_rate = (len(self.success) / total_checks * 100) if total_checks > 0 else 0
        
        print(f"📈 RESUMEN:")
        print(f"  Total verificaciones: {total_checks}")
        print(f"  Éxitos: {len(self.success)}")
        print(f"  Advertencias: {len(self.warnings)}")
        print(f"  Errores: {len(self.errors)}")
        print(f"  Tasa de éxito: {success_rate:.1f}%")
        
        if len(self.errors) == 0:
            print("\n🎉 TODAS LAS CORRECCIONES DE SEGURIDAD ESTÁN IMPLEMENTADAS")
        else:
            print(f"\n🚨 SE ENCONTRARON {len(self.errors)} ERRORES QUE REQUIEREN ATENCIÓN")


def main():
    """Función principal del script."""
    # Detectar directorio base (desde scripts/ hacia rayuela_backend/)
    script_dir = Path(__file__).parent
    base_dir = script_dir.parent
    
    print(f"📁 Directorio base: {base_dir}")
    
    verifier = SecurityFixVerifier(str(base_dir))
    success = verifier.verify_all()
    
    # Retornar código de salida apropiado
    sys.exit(0 if success else 1)


if __name__ == "__main__":
    main() 