#!/usr/bin/env python3
"""
Validation script for end_users table schema after fixing redundant id column issue.

This script validates that:
1. end_users table has only one Identity() column (user_id)
2. No redundant id column exists
3. Primary key constraint is properly defined
4. All foreign key relationships are intact
"""

import sys
import os
import asyncio
from typing import List, Dict, Any

# Add the src directory to the path
sys.path.insert(0, os.path.join(os.path.dirname(__file__), '..', '..', 'src'))

from sqlalchemy import create_engine, text, MetaData, inspect
from sqlalchemy.exc import SQLAlchemyError
from src.core.config import get_settings
from src.db.session import get_db_url


async def validate_end_users_schema() -> Dict[str, Any]:
    """Validate the end_users table schema for correctness."""
    
    results = {
        'status': 'success',
        'issues': [],
        'validations': {},
        'schema_info': {}
    }
    
    try:
        # Get database URL
        settings = get_settings()
        db_url = get_db_url()
        
        # Create engine and inspector
        engine = create_engine(db_url)
        inspector = inspect(engine)
        
        print("🔍 Validating end_users table schema...")
        
        # Check if end_users table exists
        if 'end_users' not in inspector.get_table_names():
            results['status'] = 'error'
            results['issues'].append('end_users table does not exist')
            return results
        
        # Get table columns
        columns = inspector.get_columns('end_users')
        column_names = [col['name'] for col in columns]
        
        results['schema_info']['columns'] = column_names
        
        # Validation 1: Check that user_id exists and is Identity
        user_id_column = None
        for col in columns:
            if col['name'] == 'user_id':
                user_id_column = col
                break
        
        if not user_id_column:
            results['issues'].append('user_id column not found')
            results['validations']['user_id_exists'] = False
        else:
            results['validations']['user_id_exists'] = True
            
            # Check if user_id is Identity (auto-increment)
            is_identity = user_id_column.get('autoincrement', False) or \
                         'identity' in str(user_id_column.get('default', '')).lower()
            results['validations']['user_id_is_identity'] = is_identity
            
            if not is_identity:
                results['issues'].append('user_id column is not configured as Identity/auto-increment')
        
        # Validation 2: Check that redundant id column does NOT exist
        id_column_exists = 'id' in column_names
        results['validations']['redundant_id_removed'] = not id_column_exists
        
        if id_column_exists:
            results['issues'].append('Redundant id column still exists - should be removed')
        
        # Validation 3: Check primary key constraint
        primary_keys = inspector.get_pk_constraint('end_users')
        pk_columns = primary_keys.get('constrained_columns', [])
        results['schema_info']['primary_key_columns'] = pk_columns
        
        expected_pk = ['user_id', 'account_id']  # Composite primary key
        results['validations']['correct_primary_key'] = set(pk_columns) == set(expected_pk)
        
        if set(pk_columns) != set(expected_pk):
            results['issues'].append(f'Primary key mismatch. Expected: {expected_pk}, Found: {pk_columns}')
        
        # Validation 4: Check foreign key constraints
        foreign_keys = inspector.get_foreign_keys('end_users')
        results['schema_info']['foreign_keys'] = foreign_keys
        
        # Should have FK to accounts table
        account_fk_found = False
        for fk in foreign_keys:
            if fk['referred_table'] == 'accounts' and 'account_id' in fk['constrained_columns']:
                account_fk_found = True
                break
        
        results['validations']['account_fk_exists'] = account_fk_found
        
        if not account_fk_found:
            results['issues'].append('Foreign key to accounts table not found')
        
        # Validation 5: Check for duplicate Identity columns
        identity_columns = []
        for col in columns:
            if col.get('autoincrement', False) or 'identity' in str(col.get('default', '')).lower():
                identity_columns.append(col['name'])
        
        results['schema_info']['identity_columns'] = identity_columns
        results['validations']['single_identity_column'] = len(identity_columns) == 1
        
        if len(identity_columns) > 1:
            results['issues'].append(f'Multiple Identity columns found: {identity_columns}. Should only have user_id.')
        elif len(identity_columns) == 0:
            results['issues'].append('No Identity columns found. user_id should be Identity.')
        
        # Validation 6: Check indexes
        indexes = inspector.get_indexes('end_users')
        index_names = [idx['name'] for idx in indexes]
        results['schema_info']['indexes'] = index_names
        
        # Check that redundant id index is removed
        redundant_index_exists = 'idx_end_users_id' in index_names
        results['validations']['redundant_index_removed'] = not redundant_index_exists
        
        if redundant_index_exists:
            results['issues'].append('Redundant index idx_end_users_id still exists')
        
        # Overall status
        if results['issues']:
            results['status'] = 'warning' if len(results['issues']) == 1 else 'error'
        
        engine.dispose()
        
    except SQLAlchemyError as e:
        results['status'] = 'error'
        results['issues'].append(f'Database error: {str(e)}')
    except Exception as e:
        results['status'] = 'error'
        results['issues'].append(f'Unexpected error: {str(e)}')
    
    return results


def print_validation_results(results: Dict[str, Any]) -> None:
    """Print validation results in a formatted way."""
    
    status = results['status']
    if status == 'success':
        print("✅ end_users table schema validation PASSED")
    elif status == 'warning':
        print("⚠️ end_users table schema validation completed with WARNINGS")
    else:
        print("❌ end_users table schema validation FAILED")
    
    print("\n📊 Schema Information:")
    schema_info = results.get('schema_info', {})
    
    print(f"  Columns: {schema_info.get('columns', [])}")
    print(f"  Primary Key: {schema_info.get('primary_key_columns', [])}")
    print(f"  Identity Columns: {schema_info.get('identity_columns', [])}")
    print(f"  Indexes: {schema_info.get('indexes', [])}")
    
    print("\n🔍 Validation Results:")
    validations = results.get('validations', {})
    
    for check, passed in validations.items():
        status_icon = "✅" if passed else "❌"
        print(f"  {status_icon} {check}: {'PASS' if passed else 'FAIL'}")
    
    if results.get('issues'):
        print("\n🚨 Issues Found:")
        for issue in results['issues']:
            print(f"  • {issue}")
    
    print("\n📋 Summary:")
    if status == 'success':
        print("  🎉 The end_users table schema is correctly configured!")
        print("  🎯 user_id is the single Identity() column and primary key component")
        print("  🗑️ Redundant id column has been successfully removed")
    else:
        print("  ⚠️ Schema issues detected. Please review and fix before proceeding.")


async def main():
    """Main function to run the validation."""
    
    print("🔧 End Users Table Schema Validator")
    print("=" * 50)
    print("Validating schema after redundant id column fix...\n")
    
    results = await validate_end_users_schema()
    print_validation_results(results)
    
    # Exit with appropriate code
    if results['status'] == 'error':
        sys.exit(1)
    elif results['status'] == 'warning':
        sys.exit(2)
    else:
        sys.exit(0)


if __name__ == "__main__":
    asyncio.run(main()) 